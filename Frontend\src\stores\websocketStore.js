import { defineStore } from 'pinia'

export const useWebsocketStore = defineStore('websocket', {
  state: () => ({
    isConnected: false,
    connectionAttempts: 0,
    lastConnectionTime: null,
    alertMessages: [],
    // 按设备ID分组的警报
    alertsByDevice: {}
  }),
  
  actions: {
    setIsConnected(status) {
      const prevStatus = this.isConnected;
      this.isConnected = status;
      console.log(`[WebSocket状态] 连接状态: ${prevStatus} -> ${status}`);
      
      if (status) {
        this.lastConnectionTime = new Date().toISOString();
        console.log(`[WebSocket连接] 连接时间: ${this.lastConnectionTime}`);
      }
    },
    
    incrementConnectionAttempts() {
      this.connectionAttempts++;
      console.log(`[连接尝试] 连接尝试次数增加到: ${this.connectionAttempts}`);
    },
    
    resetConnectionAttempts() {
      const prevAttempts = this.connectionAttempts;
      this.connectionAttempts = 0;
      console.log(`[连接重置] 连接尝试次数重置: ${prevAttempts} -> 0`);
    },
    
    // 生成用于查找警报的唯一键
    generateAlertKey(message) {
      const deviceId = message.deviceId || message.device?.id || 'unknown';
      const alertType = message.type || 'unknown';
      
      if (alertType === 'metric_alert' && message.device?.metric) {
        return `${alertType}-${deviceId}-${message.device.metric}`;
      } else if (alertType === 'device_alert' || alertType === 'connection_alert') {
        return `${alertType}-${deviceId}`;
      } else {
        // 对于其他类型的警报，使用ID
        return message.id;
      }
    },
    
    // 添加或更新警报消息
    addAlertMessage(message) {
      const alertKey = this.generateAlertKey(message);
      
      // 获取设备ID
      const deviceId = message.deviceId || message.device?.id || 'unknown';
      
      // 检查是否应该更新现有警报
      const existingAlertIndex = this.alertMessages.findIndex(alert => 
        this.generateAlertKey(alert) === alertKey
      );
      
      // 记录日志
      console.log(`[警报处理] 处理警报: ${message.title}, 设备: ${deviceId}, 键: ${alertKey}`);
      
      if (existingAlertIndex !== -1) {
        // 更新现有警报
        console.log(`[警报更新] 更新现有警报, 索引: ${existingAlertIndex}`);
        
        // 保留原始警报的一些元数据
        const originalTimestamp = this.alertMessages[existingAlertIndex].originalTimestamp || 
                                 this.alertMessages[existingAlertIndex].timestamp;
        
        // 更新警报内容，保留原始接收时间
        const updatedAlert = {
          ...message,
          originalTimestamp: originalTimestamp,
          updateCount: (this.alertMessages[existingAlertIndex].updateCount || 0) + 1,
          lastUpdated: new Date().toISOString()
        };
        
        // 更新全局警报列表和设备特定列表
        this.alertMessages.splice(existingAlertIndex, 1);
        this.alertMessages.unshift(updatedAlert);
        
        // 更新设备特定的警报列表
        if (this.alertsByDevice[deviceId]) {
          const deviceAlertIndex = this.alertsByDevice[deviceId].findIndex(alert => 
            this.generateAlertKey(alert) === alertKey
          );
          
          if (deviceAlertIndex !== -1) {
            this.alertsByDevice[deviceId].splice(deviceAlertIndex, 1);
            this.alertsByDevice[deviceId].unshift(updatedAlert);
          } else {
            this.alertsByDevice[deviceId].unshift(updatedAlert);
          }
        } else {
          this.alertsByDevice[deviceId] = [updatedAlert];
        }
        
        console.log(`[警报更新] 警报已更新: ${updatedAlert.title}, 更新次数: ${updatedAlert.updateCount}`);
      } else {
        // 添加新警报
        const newAlert = {
          ...message,
          originalTimestamp: message.timestamp,
          updateCount: 0,
          lastUpdated: message.timestamp
        };
        
        // 添加消息到列表开头（最新的在前面）
        this.alertMessages.unshift(newAlert);
        console.log(`[警报存储] 新警报已添加: ${newAlert.title}`);
        
        // 增加警报存储上限至100条
        const MAX_GLOBAL_ALERTS = 100;
        if (this.alertMessages.length > MAX_GLOBAL_ALERTS) {
          const removed = this.alertMessages.length - MAX_GLOBAL_ALERTS;
          this.alertMessages = this.alertMessages.slice(0, MAX_GLOBAL_ALERTS); // 保留最新的
          console.log(`[警报管理] 移除${removed}条旧警报，当前存储警报数: ${this.alertMessages.length}`);
        }
        
        // 按设备ID分组存储警报
        if (!this.alertsByDevice[deviceId]) {
          this.alertsByDevice[deviceId] = [];
        }
        
        // 添加到设备特定的警报列表（最新的在前面）
        this.alertsByDevice[deviceId].unshift(newAlert);
        
        // 每个设备最多保留20条警报
        const MAX_DEVICE_ALERTS = 20;
        if (this.alertsByDevice[deviceId].length > MAX_DEVICE_ALERTS) {
          this.alertsByDevice[deviceId] = this.alertsByDevice[deviceId].slice(0, MAX_DEVICE_ALERTS);
        }
      }
      
      console.log(`[设备警报] 设备 ${deviceId} 当前警报数: ${this.alertsByDevice[deviceId]?.length || 0}`);
      console.log(`[警报统计] 当前存储警报总数: ${this.alertMessages.length}, 设备数: ${Object.keys(this.alertsByDevice).length}`);
    },
    
    // 清空所有警报
    clearAlerts() {
      const previousCount = this.alertMessages.length;
      this.alertMessages = [];
      this.alertsByDevice = {};
      console.log(`[警报管理] 清空所有警报，共清除${previousCount}条警报`);
    },
    
    // 移除单个警报
    removeAlert(alertId) {
      const initialCount = this.alertMessages.length;
      this.alertMessages = this.alertMessages.filter(alert => alert.id !== alertId);
      
      // 同时从设备特定的警报列表中移除
      for (const deviceId in this.alertsByDevice) {
        this.alertsByDevice[deviceId] = this.alertsByDevice[deviceId].filter(
          alert => alert.id !== alertId
        );
        
        // 如果设备没有警报了，删除该设备的条目
        if (this.alertsByDevice[deviceId].length === 0) {
          delete this.alertsByDevice[deviceId];
        }
      }
      
      console.log(`[警报管理] 移除警报ID: ${alertId}, 警报列表由${initialCount}减少到${this.alertMessages.length}`);
    }
  },
  
  getters: {
    isConnectionStable: (state) => {
      const isStable = state.isConnected && state.connectionAttempts < 5;
      console.log(`[连接稳定性] 连接状态: ${isStable ? '稳定' : '不稳定'}, 尝试次数: ${state.connectionAttempts}`);
      return isStable;
    },
    
    recentAlerts: (state) => {
      // 返回最新的5条消息
      const recentAlerts = state.alertMessages.slice(0, 5);
      console.log(`[最近警报] 获取最近${recentAlerts.length}条警报`);
      return recentAlerts;
    },
    
    // 获取特定设备的警报
    alertsForDevice: (state) => (deviceId) => {
      const alerts = state.alertsByDevice[deviceId] || [];
      console.log(`[设备警报] 获取设备 ${deviceId} 的警报, 共${alerts.length}条`);
      return alerts;
    },
    
    // 获取所有设备ID
    allDeviceIds: (state) => {
      return Object.keys(state.alertsByDevice);
    },
    
    // 获取总警报数量
    totalAlerts: (state) => {
      return state.alertMessages.length;
    },
    
    // 获取显示的警报列表（用于UI展示，可以按时间倒序）
    displayAlerts: (state) => {
      // 直接返回警报列表，已经按最新优先排序了
      return state.alertMessages;
    }
  }
}) 