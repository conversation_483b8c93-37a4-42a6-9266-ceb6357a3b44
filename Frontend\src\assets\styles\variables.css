:root {
  /* 背景颜色 */
  --bg: #ffffff;
  --bg-light: #f6f8fa;
  --bg-lighter: #f3f4f6;
  --bg-darker: #eaeaea;

  /* 文本颜色 */
  --text: #24292e;
  --text-secondary: #586069;

  /* 主题颜色 */
  --primary: #1890ff;
  --primary-light: #40a9ff;
  --primary-dark: #096dd9;
  --primary-rgb: 24, 144, 255;
  --error: #f5222d;
  --error-rgb: 245, 82, 45;
  --error-bg: #fff1f0;
  --success: #52c41a;
  --warning: #faad14;

  /* 边框和阴影 */
  --border: rgba(0, 0, 0, 0.1);
  --border-strong: rgba(0, 0, 0, 0.15);
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);

  /* 模态框背景 */
  --modal-overlay: rgba(0, 0, 0, 0.3);
  
  /* 确保没有遗漏的变量 */
  --bg-panel: #ffffff;
  --bg-card: #ffffff;
  --bg-input: #ffffff;
  --bg-hover: #f0f2f5;
  --bg-active: #e6f7ff;
}

/* 暗色主题 - 暂时禁用
@media (prefers-color-scheme: dark) {
  :root {
    --bg: #1a1b1e;
    --bg-light: #25262b;
    --bg-lighter: #2c2e33;
    --bg-darker: #141517;
    
    --text: #e2e2e3;
    --text-secondary: #909296;
    
    --primary: #4dabf7;
    --primary-light: #74c0fc;
    --primary-dark: #339af0;
    --primary-rgb: 77, 171, 247;
    --error: #ff6b6b;
    --error-rgb: 255, 107, 107;
    --error-bg: #3b161b;
    --success: #51cf66;
    --warning: #fcc419;
    
    --border: rgba(255, 255, 255, 0.1);
    --border-strong: rgba(255, 255, 255, 0.15);
    
    --modal-overlay: rgba(0, 0, 0, 0.75);
    
    --bg-panel: #25262b;
    --bg-card: #2c2e33;
    --bg-input: #1a1b1e;
    --bg-hover: #3f4146;
    --bg-active: #2b3655;
  }
}
*/ 