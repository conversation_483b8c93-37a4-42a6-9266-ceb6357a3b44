"""
MySQL数据库代理类
功能：
- 提供MySQL数据库连接和操作接口
- 负责向量数据的存储和检索
- 支持数据库初始化和表创建

状态：已实现基本功能，正常运行中
"""

import mysql.connector
from mysql.connector import Error
from typing import List, Dict, Optional, Any, Generator
import datetime
import json
import math
import os

class MySQLAgent:
    def __init__(self):
        self.connection_config = {
            "host": os.environ.get("MYSQL_HOST", "*************"),
            "port": int(os.environ.get("MYSQL_PORT", "8011")),
            "user": os.environ.get("MYSQL_USER", "root"),
            "password": os.environ.get("MYSQL_PASSWORD", "1qazXSW@")
        }
        # 批量插入的大小
        self.batch_size = 1000
        # 标记是否已初始化
        self._initialized = False
        
    def is_database_configured(self) -> bool:
        """检查数据库是否配置完整"""
        # 检查必要的环境变量是否配置
        required_vars = ["MYSQL_HOST", "MYSQL_PORT", "MYSQL_USER", "MYSQL_PASSWORD"]
        return all(os.environ.get(var) for var in required_vars)
        
    def connect(self, database: Optional[str] = None) -> Optional[mysql.connector.MySQLConnection]:
        """建立MySQL连接"""
        try:
            config = self.connection_config.copy()
            if database:
                config["database"] = database
            connection = mysql.connector.connect(**config)
            if connection.is_connected():
                return connection
        except Error as e:
            print(f"连接MySQL时出错: {e}")
            return None

    def init_database(self) -> bool:
        """初始化知识库数据库和表(如果不存在)"""
        # 如果已经初始化过，直接返回True
        if self._initialized:
            return True
            
        # 如果数据库没有配置，跳过初始化
        if not self.is_database_configured():
            print("数据库未配置，跳过初始化")
            return False
            
        try:
            connection = self.connect()
            if not connection:
                return False
                
            cursor = connection.cursor()
            
            # 创建数据库(如果不存在)
            cursor.execute("CREATE DATABASE IF NOT EXISTS knowledge_base")
            cursor.execute("USE knowledge_base")
            
            # 创建文档向量表(如果不存在)
            if not self.table_exists(connection, "document_vectors"):
                create_table_query = """
                CREATE TABLE document_vectors (
                    id VARCHAR(128) PRIMARY KEY,           /* 增加ID长度 */
                    content MEDIUMTEXT NOT NULL,           /* 使用MEDIUMTEXT支持最大16MB文本 */
                    vector LONGTEXT NOT NULL,              /* 使用LONGTEXT存储向量，支持最大4GB */
                    metadata JSON,                         /* 使用JSON类型存储元数据 */
                    source VARCHAR(512),                   /* 增加来源字段 */
                    doc_type VARCHAR(100),                 /* 文档类型 */
                    chunk_id INT,                         /* 分块ID */
                    embedding_model VARCHAR(100),          /* 嵌入模型 */
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    INDEX source_idx (source),
                    INDEX doc_type_idx (doc_type),
                    INDEX chunk_id_idx (chunk_id),
                    FULLTEXT INDEX content_idx (content)   /* 全文索引 */
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
                """
                cursor.execute(create_table_query)
                print("创建document_vectors表成功")
                
            # 创建同步记录表(如果不存在)
            if not self.table_exists(connection, "sync_records"):
                create_sync_table_query = """
                CREATE TABLE sync_records (
                    id BIGINT AUTO_INCREMENT PRIMARY KEY,  /* 使用BIGINT避免ID用尽 */
                    collection_name VARCHAR(255) NOT NULL,
                    batch_number INT NOT NULL,             /* 批次号 */
                    records_count INT NOT NULL,            /* 记录数量 */
                    last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status VARCHAR(50) NOT NULL,
                    message MEDIUMTEXT,                    /* 使用MEDIUMTEXT存储详细信息 */
                    INDEX collection_idx (collection_name),
                    INDEX batch_idx (batch_number)
                ) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
                """
                cursor.execute(create_sync_table_query)
                print("创建sync_records表成功")
            
            connection.commit()
            self._initialized = True
            return True
                
        except Error as e:
            print(f"初始化数据库时出错: {e}")
            return False
        finally:
            if 'connection' in locals() and connection and connection.is_connected():
                cursor.close()
                connection.close()

    def batch_generator(self, data: List[Any], batch_size: int) -> Generator[List[Any], None, None]:
        """生成批量数据的生成器"""
        for i in range(0, len(data), batch_size):
            yield data[i:i + batch_size]

    def store_vectors_batch(self, vectors_data: List[Dict[str, Any]]) -> bool:
        """批量存储文档向量"""
        # 确保数据库已初始化
        if not self._initialized and not self.init_database():
            print("数据库未初始化，无法存储向量")
            return False
            
        try:
            connection = self.connect("knowledge_base")
            if not connection:
                return False
                
            cursor = connection.cursor()
            
            query = """
            INSERT INTO document_vectors 
                (id, content, vector, metadata, source, doc_type, chunk_id, embedding_model)
            VALUES 
                (%s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE 
                content = VALUES(content),
                vector = VALUES(vector),
                metadata = VALUES(metadata),
                source = VALUES(source),
                doc_type = VALUES(doc_type),
                chunk_id = VALUES(chunk_id),
                embedding_model = VALUES(embedding_model),
                updated_at = CURRENT_TIMESTAMP
            """
            
            success_count = 0
            for batch in self.batch_generator(vectors_data, self.batch_size):
                values = [
                    (
                        item['id'],
                        item['content'],
                        json.dumps(item['vector']),
                        json.dumps(item.get('metadata', {})),
                        item.get('source', ''),
                        item.get('doc_type', ''),
                        item.get('chunk_id', None),
                        item.get('embedding_model', '')
                    )
                    for item in batch
                ]
                
                cursor.executemany(query, values)
                success_count += len(batch)
                
                # 记录同步状态
                self.record_sync_status(
                    collection_name=batch[0].get('collection_name', 'default'),
                    batch_number=math.ceil(success_count / self.batch_size),
                    records_count=len(batch),
                    status="SUCCESS",
                    message=f"成功同步{len(batch)}条记录"
                )
                
            connection.commit()
            print(f"成功存储{success_count}条记录")
            return True
            
        except Error as e:
            print(f"批量存储向量时出错: {e}")
            return False
        finally:
            if 'connection' in locals() and connection and connection.is_connected():
                cursor.close()
                connection.close()

    def record_sync_status(
        self, 
        collection_name: str, 
        batch_number: int,
        records_count: int,
        status: str, 
        message: Optional[str] = None
    ) -> bool:
        """记录同步状态"""
        # 确保数据库已初始化
        if not self._initialized and not self.init_database():
            print("数据库未初始化，无法记录同步状态")
            return False
            
        try:
            connection = self.connect("knowledge_base")
            if not connection:
                return False
                
            cursor = connection.cursor()
            
            query = """
            INSERT INTO sync_records 
                (collection_name, batch_number, records_count, status, message)
            VALUES 
                (%s, %s, %s, %s, %s)
            """
            
            cursor.execute(query, (
                collection_name,
                batch_number,
                records_count,
                status,
                message
            ))
            connection.commit()
            return True
            
        except Error as e:
            print(f"记录同步状态时出错: {e}")
            return False
        finally:
            if 'connection' in locals() and connection and connection.is_connected():
                cursor.close()
                connection.close()

    def get_sync_status_summary(self, collection_name: str) -> Dict[str, Any]:
        """获取同步状态摘要"""
        # 确保数据库已初始化
        if not self._initialized and not self.init_database():
            print("数据库未初始化，无法获取同步状态摘要")
            return {}
            
        try:
            connection = self.connect("knowledge_base")
            if not connection:
                return {}
                
            cursor = connection.cursor(dictionary=True)
            
            query = """
            SELECT 
                collection_name,
                COUNT(*) as total_batches,
                SUM(records_count) as total_records,
                MIN(last_sync_time) as first_sync,
                MAX(last_sync_time) as last_sync,
                COUNT(CASE WHEN status = 'SUCCESS' THEN 1 END) as success_batches,
                COUNT(CASE WHEN status != 'SUCCESS' THEN 1 END) as failed_batches
            FROM sync_records 
            WHERE collection_name = %s 
            GROUP BY collection_name
            """
            
            cursor.execute(query, (collection_name,))
            return cursor.fetchone() or {}
            
        except Error as e:
            print(f"获取同步状态摘要时出错: {e}")
            return {}
        finally:
            if 'connection' in locals() and connection and connection.is_connected():
                cursor.close()
                connection.close()

    def table_exists(self, connection: mysql.connector.MySQLConnection, table_name: str) -> bool:
        """检查表是否存在"""
        cursor = connection.cursor()
        cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
        result = cursor.fetchone()
        cursor.close()
        return result is not None

# 懒加载方式创建MySQL代理实例
mysql_agent = MySQLAgent()

# 注意：不再在模块级别初始化数据库和表，避免在导入时就连接数据库
# 在需要使用时，通过调用init_database()方法初始化

# 以下测试代码仅在直接运行此文件时执行
if __name__ == "__main__":
    # 初始化数据库和表
    mysql_agent.init_database()
    
    # 存储向量示例
    doc_id = "doc1"
    content = "这是一个测试文档"
    vector = [0.1, 0.2, 0.3, 0.4]
    metadata = {"source": "test", "type": "article"}
    mysql_agent.store_vectors_batch([{"id": doc_id, "content": content, "vector": vector, "metadata": metadata}])