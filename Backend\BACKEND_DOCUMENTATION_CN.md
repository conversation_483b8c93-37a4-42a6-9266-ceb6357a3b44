# IOT Agent 后端系统文档

## 1. 系统架构

IOT Agent 后端系统是一个基于 Python 的物联网设备监控平台，主要由以下几个部分组成：

- **FastAPI 应用服务器**：提供 RESTful API 接口
- **MQTT 客户端**：连接到 MQTT 服务器获取实时设备数据
- **WebSocket 服务**：向前端推送实时告警和设备状态
- **数据处理模块**：处理设备数据并生成告警
- **数据存储**：使用 SQLite 数据库存储设备和告警信息

系统采用异步编程模型，使用 FastAPI 和 asyncio 实现高并发处理能力。

## 2. MQTT 通信

### 2.1 MQTT 连接配置

```python
# MQTT服务器设置
MQTT_HOST = "127.0.0.1"
MQTT_PORT = 5710
MQTT_WEBSOCKET_PATH = "/mqtt"
USE_WEBSOCKET = True  # 使用WebSocket协议
```

### 2.2 MQTT 连接流程

1. **创建 MQTT 客户端**

```python
# 创建MQTT客户端
client_id = f"alert-monitor-{int(time.time())}"

if USE_WEBSOCKET:
    client = mqtt.Client(client_id, transport="websockets")
    client.ws_set_options(path=MQTT_WEBSOCKET_PATH)
else:
    client = mqtt.Client(client_id)
```

2. **设置回调函数**

```python
# 设置回调函数
client.on_connect = on_connect
client.on_message = on_message
client.on_disconnect = on_disconnect
```

3. **连接到 MQTT 服务器**

```python
client.connect(MQTT_HOST, MQTT_PORT, 60)
client.loop_start()
```

### 2.3 主题订阅

系统在连接成功后订阅多个设备主题：

```python
# 设备主题列表
TOPICS = [
    "/HN3S1/JH001",
    "/HN3S1/JH002",
    "/HN3S1/JH006",
    "/HN3S1/JH007",
    "/HN3S1/JH008",
    "/HN3S1/JH009",
    "/HN3S1/HN15V3",
    "/HN3S1/HN15V4",
    # ... 更多设备主题
]

# 订阅所有主题
for topic in TOPICS:
    client.subscribe(topic)
```

### 2.4 消息处理流程

1. **接收消息**

在 `on_message` 回调函数中接收 MQTT 消息：

```python
def on_message(client, userdata, msg):
    topic = msg.topic
    current_time = datetime.datetime.now()
    
    try:
        # 解析JSON数据
        payload = msg.payload.decode('utf-8')
        data = json.loads(payload)
        
        # 从主题中提取设备ID
        device_id = topic.split('/')[-1]
        
        # 更新设备数据
        update_device_data_sync(device_id, data)
        
    except Exception as e:
        # 处理异常
        pass
```

2. **数据处理**

接收到消息后，系统会：

- 解析 JSON 格式的消息载荷
- 从主题中提取设备 ID
- 更新设备数据存储
- 检查设备数据是否超出阈值

3. **告警生成**

系统根据预设的阈值检查设备数据，生成告警信息：

```python
# 阈值设置
THRESHOLDS = {
    "温度": {"min": 20, "max": 80},   # 温度阈值(℃)
    "压力": {"min": 0.1, "max": 1.0}, # 压力阈值(MPa)
    "电流": {"min": 0.5, "max": 2.0}, # 电流阈值(A)
    "振动": {"min": 0, "max": 10}     # 振动阈值(mm)
}
```

告警生成后，通过 WebSocket 广播给前端。

## 3. 数据处理模块

### 3.1 设备数据更新

```python
def update_device_data_sync(self, device_id: str, data: dict) -> None:
    # 更新设备数据
    self.mqtt_devices[device_id] = {
        **processed_data,
        "last_update": received_time
    }
    
    # 检查是否需要生成警报
    alerts = self.check_device_alerts(device_id)
    if alerts:
        # 处理告警
        pass
```

### 3.2 告警检查

```python
def check_device_alerts(self, device_id: str) -> List[dict]:
    alerts = []
    device_data = self.mqtt_devices.get(device_id)
    
    if not device_data:
        return alerts
    
    # 检查设备状态
    status = device_data.get("status")
    if status is None:
        status = device_data.get("Status")
        
    if status != 0:
        alerts.append(self.generate_device_alert(device_id, device_data))
    
    # 检查各项指标
    for metric, thresholds in self.device_thresholds.items():
        value = device_data.get(metric)
        if value is not None:
            if value < thresholds["min"] or value > thresholds["max"]:
                alerts.append(self.generate_metric_alert(device_id, metric, value, thresholds))
    
    return alerts
```

## 4. API 接口

### 4.1 设备状态接口

- **GET /api/devices/status**：获取所有设备的状态信息
- **GET /api/devices/{device_id}**：获取指定设备的详细信息

### 4.2 告警接口

- **GET /api/alerts**：获取所有告警信息
- **GET /api/alerts/recent**：获取最近的告警信息
- **POST /api/alerts/acknowledge**：确认告警

### 4.3 WebSocket 接口

- **WebSocket /ws**：实时推送设备状态和告警信息

## 5. 部署说明

### 5.1 环境要求

- Python 3.8+
- 依赖包：见 requirements.txt

### 5.2 启动命令

```bash
# 启动后端服务
uvicorn app.main:app --host 0.0.0.0 --port 8000

# 启动MQTT监控服务
python mqtt_alert_monitor.py
```

### 5.3 配置文件

主要配置在 .env 文件中：

```
# 数据库配置
DATABASE_URL=sqlite:///app.db

# MQTT配置
MQTT_HOST=127.0.0.1
MQTT_PORT=5710
MQTT_PATH=/mqtt
USE_WEBSOCKET=True

# 服务配置
DEBUG=True
LOG_LEVEL=INFO
```

## 6. 故障排除

### 6.1 MQTT 连接问题

- 检查 MQTT 服务器地址和端口是否正确
- 检查网络连接是否正常
- 查看日志中的连接错误信息

### 6.2 数据解析错误

- 检查 MQTT 消息格式是否符合预期
- 查看日志中的 JSON 解析错误

### 6.3 告警未触发

- 检查阈值设置是否合理
- 确认设备数据是否正确更新
- 检查 WebSocket 连接是否正常
