import os
import sys
import json
import time
import logging
import threading
from typing import Dict, Any, Optional, List, Union
import paho.mqtt.client as mqtt

# 导入MCP库 - 参照example_math_server.py的导入方式
from mcp.server.fastmcp import FastMCP

# 创建MCP服务器
mcp = FastMCP("MQTT")

# 设置日志记录
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# MQTT客户端全局变量
mqtt_client = None
device_data = {}  # 存储最新设备数据
device_map = {}   # 设备名称到主题的映射
connected = False

# 加载配置文件
def load_config() -> Dict[str, Any]:
    """加载配置文件"""
    config_paths = [
        "config.json",  # 当前目录
        os.path.join(os.getcwd(), "config.json"),  # 绝对路径
        os.path.join(os.path.dirname(os.getcwd()), "config.json"),  # 上级目录
        os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.json"),  # 脚本所在目录
    ]
    
    for path in config_paths:
        if os.path.exists(path):
            logging.info(f"从 {path} 加载配置")
            try:
                with open(path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    return config
            except Exception as e:
                logging.error(f"加载配置文件 {path} 失败: {e}")
    
    # 如果找不到配置文件，返回默认配置
    logging.warning("找不到配置文件，使用默认配置")
    return {
        "mqtt": {
            "host": "************",
            "port": 8083,
            "path": "/mqtt"
        },
        "devices": []
    }

# 设置设备映射
def setup_device_map(config):
    """设置设备映射关系"""
    global device_map
    
    if "devices" not in config:
        return
    
    for i, device in enumerate(config["devices"]):
        station = device.get("station", "")
        wellname = device.get("wellname", "")
        display_name = device.get("display_name", f"井{i+1:02d}")
        
        # 主题格式: /{station}/{wellname}
        topic = f"/{station}/{wellname}"
        
        # 存储映射关系
        device_map[display_name] = {
            "topic": topic,
            "station": station,
            "wellname": wellname
        }
        
        # 初始化设备数据
        device_data[topic] = None
        
        logging.info(f"添加设备映射: {display_name} -> {topic}")

# MQTT回调函数
def on_connect(client, userdata, flags, rc):
    """连接回调函数"""
    global connected
    if rc == 0:
        logging.info("已连接到MQTT服务器")
        connected = True
        
        # 订阅所有设备主题
        for device_info in device_map.values():
            topic = device_info["topic"]
            client.subscribe(topic)
            logging.info(f"已订阅主题: {topic}")
    else:
        logging.error(f"连接MQTT服务器失败，返回码: {rc}")
        connected = False

def on_message(client, userdata, msg):
    """消息回调函数"""
    topic = msg.topic
    
    try:
        # 解析JSON数据
        payload = msg.payload.decode('utf-8')
        data = json.loads(payload)
        
        # 更新设备数据
        device_data[topic] = data
        
        logging.debug(f"收到主题 {topic} 的消息: {data}")
    except Exception as e:
        logging.error(f"处理消息失败: {e}")

def on_disconnect(client, userdata, rc):
    """断开连接回调函数"""
    global connected
    logging.warning(f"与MQTT服务器断开连接，返回码: {rc}")
    connected = False
    
    # 如果是意外断开连接，尝试重新连接
    if rc != 0:
        logging.info("尝试重新连接...")
        try:
            client.reconnect()
        except Exception as e:
            logging.error(f"重新连接失败: {e}")

# 在后台线程中连接MQTT服务器
def connect_mqtt_background(config):
    """在后台线程中连接MQTT服务器"""
    # 定义实际的连接函数
    def do_connect():
        global mqtt_client, connected
        
        if "mqtt" not in config:
            logging.error("MQTT配置不存在")
            return
        
        mqtt_config = config["mqtt"]
        host = mqtt_config.get("host", "************")
        port = mqtt_config.get("port", 8083)
        path = mqtt_config.get("path", "/mqtt")
        
        logging.info(f"尝试在后台连接到MQTT服务器: {host}:{port}{path}")
        
        # 创建客户端ID (使用时间戳确保唯一性)
        client_id = f"iot-agent-{int(time.time())}"
        
        try:
            # 创建MQTT客户端 (使用WebSockets传输)
            mqtt_client = mqtt.Client(client_id, transport="websockets")
            mqtt_client.ws_set_options(path=path)
            
            # 设置回调函数
            mqtt_client.on_connect = on_connect
            mqtt_client.on_message = on_message
            mqtt_client.on_disconnect = on_disconnect
            
            # 设置连接超时
            connect_timeout = 5  # 5秒连接超时
            mqtt_client.connect(host, port, connect_timeout)
            mqtt_client.loop_start()
            
            # 等待连接建立，最多等待5秒
            retry_count = 0
            while not connected and retry_count < 5:
                time.sleep(1)
                retry_count += 1
                logging.debug(f"等待MQTT连接... {retry_count}/5秒")
            
            if connected:
                logging.info("已成功连接到MQTT服务器")
            else:
                logging.warning("MQTT连接超时，将使用模拟数据")
                if mqtt_client:
                    mqtt_client.loop_stop()
                    mqtt_client = None
        
        except Exception as e:
            logging.error(f"MQTT连接出错: {e}")
            logging.warning("无法连接到MQTT服务器，将使用模拟数据")
            if mqtt_client:
                try:
                    mqtt_client.loop_stop()
                except:
                    pass
                mqtt_client = None
    
    # 在后台线程中启动连接
    threading.Thread(target=do_connect, daemon=True).start()
    logging.info("后台MQTT连接线程已启动")
    return True

# 获取设备数据的辅助函数
def get_device_data_internal(display_name: str) -> Optional[Dict[str, Any]]:
    """
    获取指定设备的最新数据
    
    Args:
        display_name: 设备显示名称，如 "井01"
        
    Returns:
        设备数据字典或None（如果设备不存在或没有数据）
    """
    if display_name not in device_map:
        logging.warning(f"设备 '{display_name}' 不存在")
        # 显示所有可用设备以便调试
        if device_map:
            logging.info(f"可用设备: {list(device_map.keys())}")
        return None
        
    topic = device_map[display_name]["topic"]
    # 如果没有实时数据，生成一个模拟数据以允许程序继续
    if not connected or device_data.get(topic) is None:
        logging.warning(f"设备 '{display_name}' (主题: {topic}) 无数据可用，使用模拟数据")
        # 返回一个模拟数据
        return {
            "Station": device_map[display_name]["station"],
            "Wellname": device_map[display_name]["wellname"],
            "Status": 0,
            "Check_date": time.strftime("%Y-%m-%d %H:%M:%S"),
            "Dyna_points": 200,
            "Displacement": "0.00|0.05|0.10|0.15|0.20",
            "Disp_load": "10.00|12.50|15.00|17.50|20.00",
            "Disp_current": "50.00|55.00|60.00|65.00|70.00",
            "Values": [
                {"id": "TWT", "Value": 25.0},
                {"id": "WIP", "Value": 0.8},
                {"id": "CPV", "Value": 1.2},
                {"id": "SLV", "Value": 4.0},
                {"id": "CHC", "Value": 5.0}
            ]
        }
    
    return device_data.get(topic)

# MCP工具定义
@mcp.tool()
def get_device_data(display_name: str) -> Dict[str, Any]:
    """
    获取指定设备的最新数据
    
    Args:
        display_name: 设备显示名称，如 "井01"
        
    Returns:
        设备数据字典
    """
    data = get_device_data_internal(display_name)
    if data is None:
        return {"error": f"未找到设备 {display_name} 的数据"}
    return data


@mcp.tool()
def list_devices() -> List[Dict[str, Any]]:
    """
    获取所有设备的列表和状态
    
    Returns:
        设备列表
    """
    result = []
    
    for display_name, device_info in device_map.items():
        topic = device_info["topic"]
        data = device_data.get(topic)
        
        # 如果没有数据，使用模拟数据
        if not connected or data is None:
            data = {
                "Status": 0,
                "Check_date": time.strftime("%Y-%m-%d %H:%M:%S")
            }
        
        device_item = {
            "display_name": display_name,
            "station": device_info["station"],
            "wellname": device_info["wellname"],
            "topic": topic,
            "online": connected,
            "status": data.get("Status", None),
            "last_updated": data.get("Check_date", "未知")
        }
        
        result.append(device_item)
        
    return result


@mcp.tool()
def get_device_parameter(display_name: str, parameter: str) -> Dict[str, Any]:
    """
    获取指定设备的特定参数值
    
    Args:
        display_name: 设备显示名称，如 "井01"
        parameter: 参数名称，如 "TWT"(井口温度), "WIP"(井口压力), "CPV"(套压) 等
        
    Returns:
        包含参数值的字典
    """
    data = get_device_data_internal(display_name)
    if data is None:
        return {"error": f"未找到设备 {display_name} 的数据"}
    
    # 尝试从Values列表中查找参数
    if "Values" in data and isinstance(data["Values"], list):
        for item in data["Values"]:
            # 尝试匹配ID
            if item.get("id") == parameter:
                return {"parameter": parameter, "value": item.get("Value")}
    
    # 直接从数据中查找
    value = data.get(parameter)
    if value is None:
        return {"error": f"未找到设备 {display_name} 的参数 {parameter}"}
    
    return {"parameter": parameter, "value": value}


@mcp.tool()
def get_device_chart_data(display_name: str) -> Dict[str, Any]:
    """
    获取指定设备的功图数据
    
    Args:
        display_name: 设备显示名称，如 "井01"
        
    Returns:
        功图数据
    """
    data = get_device_data_internal(display_name)
    if data is None:
        return {"error": f"未找到设备 {display_name} 的功图数据"}
    
    chart_data = {
        "displacement": data.get("Displacement", "").split("|") if data.get("Displacement") else [],
        "disp_load": data.get("Disp_load", "").split("|") if data.get("Disp_load") else [],
        "disp_current": data.get("Disp_current", "").split("|") if data.get("Disp_current") else [],
        "points": data.get("Dyna_points", 0),
        "check_date": data.get("Check_date", "")
    }
    
    return chart_data


@mcp.tool()
def get_device_status(display_name: str) -> Dict[str, Any]:
    """
    获取指定设备的状态信息
    
    Args:
        display_name: 设备显示名称，如 "井01"
        
    Returns:
        设备状态信息
    """
    data = get_device_data_internal(display_name)
    if data is None:
        return {"error": f"未找到设备 {display_name} 的状态"}
    
    status = data.get("Status", None)
    status_text = "正常" if status == 0 else f"异常 (错误码: {status})"
    
    return {
        "display_name": display_name,
        "status_code": status,
        "status_text": status_text,
        "last_updated": data.get("Check_date", "未知")
    }


@mcp.tool()
def get_parameter_description() -> Dict[str, str]:
    """
    获取参数字段的中文描述和单位说明
    
    Returns:
        参数描述字典
    """
    return {
        "TWT": "井口温度 (℃)",
        "WIP": "井口压力 (MPa)",
        "CPV": "套压 (MPa)",
        "SLV": "冲程 (m)",
        "CHC": "冲次 (次/分)",
        "UWL": "最大载荷 (KN)",
        "DWL": "最小载荷 (KN)",
        "WBR": "平衡度",
        "ADL": "A相电流 (A)",
        "BDL": "B相电流 (A)", 
        "CDL": "C相电流 (A)",
        "ADY": "A相电压 (V)",
        "BDY": "B相电压 (V)",
        "CDY": "C相电压 (V)",
        "GYS": "功率因数",
        "ZYG": "总有功功率 (KW)",
        "ZWG": "总无功功率 (KVar)",
        "YGL": "有功用电量 (KWh)"
    }

# 初始化
logging.info("正在初始化MQTT MCP服务器...")
config = load_config()
setup_device_map(config)

# 在后台线程中连接MQTT服务器，不阻塞主线程
connect_mqtt_background(config)
logging.info("MQTT MCP服务器初始化完成，开始处理请求")

if __name__ == "__main__":
    # 启动MCP服务器
    try:
        logging.info("正在启动MQTT MCP服务器...")
        mcp.run(transport="stdio")
    except KeyboardInterrupt:
        logging.info("用户中断，正在关闭...")
    except Exception as e:
        logging.error(f"启动MQTT MCP服务器时出错: {e}")
    finally:
        # 脚本结束时关闭MQTT连接
        logging.info("正在关闭MQTT连接...")
        if mqtt_client:
            try:
                mqtt_client.loop_stop()
                mqtt_client.disconnect()
            except Exception as e:
                logging.error(f"关闭MQTT连接时出错: {e}") 