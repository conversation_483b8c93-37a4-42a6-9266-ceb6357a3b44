"""
IOT智能体协作流
"""

import os
import json
import functools
import operator
from typing import Annotated, Sequence, TypedDict, Literal, Optional, Dict, Any
import logging

from langchain_core.messages import AIMessage, HumanMessage, BaseMessage, ToolMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_ollama import ChatOllama
from langgraph.graph import END, StateGraph, START
from langchain_core.tools import BaseTool

from app.agents.question_classifier_agent import QuestionClassifierAgent, QuestionType

# 配置日志
logger = logging.getLogger(__name__)

# 定义状态结构
class AgentState(TypedDict):
    """智能体状态数据结构"""
    messages: Annotated[Sequence[BaseMessage], operator.add]  # 消息历史
    sender: str  # 发送者
    classification: Optional[Dict[str, Any]]  # 问题分类结果
    knowledge_data: Optional[str]  # 知识库数据
    mqtt_data: Optional[str]  # MQTT数据
    final_answer: Optional[bool]  # 是否是最终答案
    error: Optional[str]  # 错误信息

# 创建智能体节点函数
def agent_node(state: AgentState, agent, name: str) -> AgentState:
    """创建智能体节点
    
    Args:
        state: 当前状态
        agent: 智能体对象
        name: 智能体名称
        
    Returns:
        更新后的状态
    """
    # 调用智能体
    result = agent.invoke(state)
    
    # 将结果转换为消息
    if isinstance(result, BaseMessage):
        message = result
    else:
        message = AIMessage(content=result, name=name)
    
    # 返回更新后的状态
    return {
        "messages": [message],
        "sender": name,
        # 保留其他字段
        "classification": state.get("classification"),
        "knowledge_data": state.get("knowledge_data"),
        "mqtt_data": state.get("mqtt_data"),
        "final_answer": state.get("final_answer"),
        "error": state.get("error")
    }

# 工具调用节点
def handle_tool(state: AgentState, tools: list[BaseTool]) -> AgentState:
    """处理工具调用
    
    Args:
        state: 当前状态
        tools: 可用的工具列表
        
    Returns:
        更新后的状态
    """
    # 获取最后一条消息
    last_message = state["messages"][-1]
    
    # 检查是否有工具调用
    if not hasattr(last_message, "tool_calls") or not last_message.tool_calls:
        # 没有工具调用，返回原始状态
        return state
    
    # 获取工具调用
    tool_calls = last_message.tool_calls
    
    # 工具映射
    tool_mapping = {tool.name: tool for tool in tools}
    
    # 处理工具调用
    tool_messages = []
    for tool_call in tool_calls:
        # 获取工具
        tool_name = tool_call["name"]
        tool = tool_mapping.get(tool_name)
        
        if not tool:
            # 工具不存在
            tool_result = f"Tool '{tool_name}' not found"
        else:
            # 调用工具
            try:
                args = json.loads(tool_call["arguments"])
                tool_result = tool.invoke(args)
            except Exception as e:
                tool_result = f"Error calling tool '{tool_name}': {str(e)}"
        
        # 创建工具消息
        tool_message = ToolMessage(
            content=str(tool_result),
            name=tool_name,
            tool_call_id=tool_call["id"]
        )
        tool_messages.append(tool_message)
    
    # 返回更新后的状态
    return {
        "messages": tool_messages,
        "sender": "tools",
        # 保留其他字段
        "classification": state.get("classification"),
        "knowledge_data": state.get("knowledge_data"),
        "mqtt_data": state.get("mqtt_data"),
        "final_answer": state.get("final_answer"),
        "error": state.get("error")
    }

# 路由决策函数
def router(state: AgentState) -> Literal["classify", "knowledge_agent", "mqtt_agent", "collaboration_agent", "direct_agent", "call_tool", "__end__"]:
    """根据状态决定下一个节点
    
    Args:
        state: 当前状态
        
    Returns:
        下一个节点名称
    """
    # 获取最后一条消息
    messages = state["messages"]
    last_message = messages[-1]
    
    # 检查是否有工具调用
    if hasattr(last_message, "tool_calls") and last_message.tool_calls:
        return "call_tool"
    
    # 检查是否有分类结果
    if not state.get("classification"):
        # 如果没有分类结果，先进行分类
        return "classify"
    
    # 根据分类结果决定下一步
    classification = state["classification"]
    question_type = classification["question_type"]
    
    # 检查是否已经是最终答案
    if state.get("final_answer"):
        return "__end__"
    
    # 检查是否有错误
    if state.get("error"):
        return "__end__"
    
    # 根据问题类型路由
    if question_type == QuestionType.KNOWLEDGE_BASE:
        # 检查是否已经获取了知识库数据
        if state.get("knowledge_data"):
            return "direct_agent"  # 已有数据，直接回答
        else:
            return "knowledge_agent"  # 获取知识库数据
    
    elif question_type == QuestionType.MQTT_DATA:
        # 检查是否已经获取了MQTT数据
        if state.get("mqtt_data"):
            return "direct_agent"  # 已有数据，直接回答
        else:
            return "mqtt_agent"  # 获取MQTT数据
    
    elif question_type == QuestionType.HYBRID:
        # 检查是否已经获取了所有需要的数据
        if state.get("knowledge_data") and state.get("mqtt_data"):
            return "collaboration_agent"  # 已有所有数据，协作回答
        elif not state.get("knowledge_data"):
            return "knowledge_agent"  # 获取知识库数据
        elif not state.get("mqtt_data"):
            return "mqtt_agent"  # 获取MQTT数据
    
    # 默认使用直接回答
    return "direct_agent"

# 问题分类节点
def classify_node(state: AgentState) -> AgentState:
    """对问题进行分类的节点
    
    Args:
        state: 当前状态
        
    Returns:
        更新后的状态，包含分类结果
    """
    # 获取用户最后一条消息
    messages = state["messages"]
    user_messages = [msg for msg in messages if isinstance(msg, HumanMessage)]
    
    if not user_messages:
        # 如果没有用户消息，返回错误
        return {
            **state,
            "error": "没有找到用户消息"
        }
    
    last_user_message = user_messages[-1].content
    
    # 创建问题分类智能体
    classifier = QuestionClassifierAgent()
    
    # 提取历史消息
    chat_history = []
    for msg in messages:
        role = "user" if isinstance(msg, HumanMessage) else "assistant"
        chat_history.append({"role": role, "content": msg.content})
    
    # 进行分类
    classification_result = classifier.classify_question(last_user_message, chat_history)
    
    # 返回更新后的状态
    return {
        **state,
        "classification": classification_result
    }

# 知识库智能体节点
def knowledge_agent_node(state: AgentState) -> AgentState:
    """知识库智能体节点
    
    Args:
        state: 当前状态
        
    Returns:
        更新后的状态，包含知识库数据
    """
    # 获取分类结果
    classification = state["classification"]
    
    # 获取用户最后一条消息
    messages = state["messages"]
    user_messages = [msg for msg in messages if isinstance(msg, HumanMessage)]
    last_user_message = user_messages[-1].content if user_messages else ""
    
    # 使用知识库工具进行检索
    knowledge_areas = classification.get("knowledge_areas", [])
    search_result = search_knowledge_base(last_user_message, knowledge_areas)
    
    # 返回更新后的状态
    return {
        **state,
        "knowledge_data": search_result,
        "sender": "KnowledgeAgent"
    }

# 模拟知识库搜索函数
def search_knowledge_base(query: str, areas: list = None) -> str:
    """模拟从知识库搜索相关内容
    
    Args:
        query: 搜索查询
        areas: 搜索领域
        
    Returns:
        搜索结果文本
    """
    # 这里应该是实际的知识库查询逻辑
    # 暂时使用模拟数据
    return f"<knowledge-agent>从知识库中找到的关于'{query}'的信息:\n\n" + \
           f"1. 关于该问题的规章制度规定...\n" + \
           f"2. 相关操作手册提到...\n" + \
           f"3. 根据标准流程..."

# MQTT数据查询节点
def mqtt_agent_node(state: AgentState) -> AgentState:
    """MQTT数据查询节点
    
    Args:
        state: 当前状态
        
    Returns:
        更新后的状态，包含MQTT数据
    """
    # 获取分类结果
    classification = state["classification"]
    
    # 获取设备ID和主题
    device_ids = classification.get("device_ids", [])
    topics = classification.get("topics", [])
    
    # 获取用户消息以便更好地格式化响应
    messages = state["messages"]
    user_messages = [msg for msg in messages if isinstance(msg, HumanMessage)]
    last_user_message = user_messages[-1].content if user_messages else ""
    
    try:
        # 使用alert_generator工具获取设备数据
        from app.utils.alert_generator import alert_generator
        from app.tools.mqtt_data_tool import format_mqtt_data_for_response
        
        # 调用工具获取数据
        mqtt_raw_data = {
            "success": True,
            "devices": {}
        }
        
        # 获取所有设备状态
        device_status = alert_generator.get_device_status()
        
        # 将设备状态转换为mqtt_raw_data格式
        for device in device_status.get("devices", []):
            device_id = device.get("id")
            if device_id and (not device_ids or device_id in device_ids):
                # 创建设备数据结构
                device_metrics = device.get("metrics", {})
                mqtt_raw_data["devices"][device_id] = {
                    "device_id": device_id,
                    "status": "正常运行" if device.get("status", 0) == 1 else "异常",
                    "last_update": device.get("last_update", "未知"),
                    "data": {
                        "温度": device_metrics.get("温度", 0),
                        "压力": device_metrics.get("压力", 0),
                        "电流": device_metrics.get("电流", 0),
                        "振动": device_metrics.get("振动", 0)
                    }
                }
        
        # 格式化数据用于响应
        mqtt_data = format_mqtt_data_for_response(mqtt_data=mqtt_raw_data, user_query=last_user_message)
        
        # 记录结果
        logger.info(f"设备数据获取成功: {device_ids}")
        
    except Exception as e:
        # 发生错误时的处理
        logger.error(f"设备数据获取失败: {str(e)}")
        mqtt_data = f"<mqtt-agent>获取设备数据时出错: {str(e)}"
    
    # 返回更新后的状态
    return {
        **state,
        "mqtt_data": mqtt_data,
        "sender": "MQTTAgent"
    }

class IOTChatGraph:
    """IOT聊天图，管理智能体之间的协作"""
    
    def __init__(self, model_name: str = "qwen2.5:32b"):
        """初始化IOT聊天图
        
        Args:
            model_name: 使用的语言模型名称
        """
        self.model_name = model_name
        
        # 创建基础LLM
        self.llm = ChatOllama(
            model=model_name,
            temperature=0.7,
        )
        
        # 创建直接回答智能体
        self.direct_agent = self._create_direct_agent()
        
        # 创建知识库响应智能体
        self.knowledge_response_agent = self._create_knowledge_response_agent()
        
        # 创建MQTT响应智能体
        self.mqtt_response_agent = self._create_mqtt_response_agent()
        
        # 创建协作智能体
        self.collaboration_agent = self._create_collaboration_agent()
        
        # 创建工具
        self.tools = []
        
        # 构建图
        self.graph = self._build_graph()
        
    def _create_direct_agent(self):
        """创建直接回答智能体
        
        Returns:
            直接回答智能体
        """
        # 创建提示模板
        prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个知识丰富的助手，负责回答用户的问题。请提供简洁、准确、有帮助的回答。"""),
            MessagesPlaceholder(variable_name="messages"),
        ])
        
        # 创建智能体
        return prompt | self.llm
    
    def _create_knowledge_response_agent(self):
        """创建知识库响应智能体
        
        Returns:
            知识库响应智能体
        """
        # 创建提示模板
        prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个专注于知识库内容的助手。
            你将根据知识库中检索到的信息回答用户的问题。
            请确保你的回答基于提供的知识库内容，并在回答中引用相关信息。
            如果知识库中的信息不足以回答问题，请明确说明。
            
            知识库内容:
            {knowledge_data}
            """),
            MessagesPlaceholder(variable_name="messages"),
        ])
        
        # 创建智能体
        return prompt | self.llm
    
    def _create_mqtt_response_agent(self):
        """创建MQTT响应智能体
        
        Returns:
            MQTT响应智能体
        """
        # 创建提示模板
        prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个专注于设备数据分析的助手。
            你将根据MQTT设备数据回答用户的问题。
            请分析提供的设备数据，并根据这些数据提供专业、准确的回答。
            如果数据不足以回答问题，请明确说明。
            
            设备数据:
            {mqtt_data}
            """),
            MessagesPlaceholder(variable_name="messages"),
        ])
        
        # 创建智能体
        return prompt | self.llm
    
    def _create_collaboration_agent(self):
        """创建协作智能体
        
        Returns:
            协作智能体
        """
        # 创建提示模板
        prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个综合分析助手，能够结合知识库信息和设备数据进行分析。
            请根据知识库内容和设备数据，对用户问题提供全面的分析和回答。
            确保你的回答既考虑到规范和标准，又结合实时设备数据。
            
            知识库内容:
            {knowledge_data}
            
            设备数据:
            {mqtt_data}
            """),
            MessagesPlaceholder(variable_name="messages"),
        ])
        
        # 创建智能体
        return prompt | self.llm
    
    def tool_node(self, state: AgentState) -> AgentState:
        """处理工具调用
        
        Args:
            state: 当前状态
            
        Returns:
            更新后的状态
        """
        return handle_tool(state, self.tools)
    
    def _build_graph(self) -> StateGraph:
        """构建智能体协作图
        
        Returns:
            构建好的状态图
        """
        # 创建状态图
        workflow = StateGraph(AgentState)
        
        # 添加分类节点
        workflow.add_node("classify", classify_node)
        
        # 添加知识库节点
        workflow.add_node("knowledge_agent", knowledge_agent_node)
        
        # 添加MQTT节点
        workflow.add_node("mqtt_agent", mqtt_agent_node)
        
        # 添加回答节点
        workflow.add_node("direct_agent", 
                          functools.partial(agent_node, agent=self.direct_agent, name="DirectAgent"))
        
        workflow.add_node("knowledge_response",
                          functools.partial(agent_node, agent=self.knowledge_response_agent, name="KnowledgeResponseAgent"))
        
        workflow.add_node("mqtt_response",
                          functools.partial(agent_node, agent=self.mqtt_response_agent, name="MQTTResponseAgent"))
        
        workflow.add_node("collaboration_agent",
                          functools.partial(agent_node, agent=self.collaboration_agent, name="CollaborationAgent"))
        
        # 添加工具节点
        workflow.add_node("call_tool", self.tool_node)
        
        # 添加起始边
        workflow.add_edge(START, "classify")
        
        # 添加分类节点的条件边
        workflow.add_conditional_edges(
            "classify",
            router,
            {
                "knowledge_agent": "knowledge_agent",
                "mqtt_agent": "mqtt_agent",
                "direct_agent": "direct_agent",
                "__end__": END
            }
        )
        
        # 添加知识库节点的条件边
        workflow.add_conditional_edges(
            "knowledge_agent",
            router,
            {
                "direct_agent": "knowledge_response",
                "mqtt_agent": "mqtt_agent",
                "call_tool": "call_tool",
                "__end__": END
            }
        )
        
        # 添加MQTT节点的条件边
        workflow.add_conditional_edges(
            "mqtt_agent",
            router,
            {
                "direct_agent": "mqtt_response",
                "knowledge_agent": "knowledge_agent",
                "collaboration_agent": "collaboration_agent",
                "call_tool": "call_tool",
                "__end__": END
            }
        )
        
        # 添加直接回答节点的边
        workflow.add_edge("direct_agent", END)
        
        # 添加知识库回答节点的边
        workflow.add_edge("knowledge_response", END)
        
        # 添加MQTT回答节点的边
        workflow.add_edge("mqtt_response", END)
        
        # 添加协作节点的边
        workflow.add_edge("collaboration_agent", END)
        
        # 添加工具节点的条件边
        workflow.add_conditional_edges(
            "call_tool",
            router,
            {
                "direct_agent": "direct_agent",
                "knowledge_agent": "knowledge_agent",
                "mqtt_agent": "mqtt_agent",
                "collaboration_agent": "collaboration_agent",
                "call_tool": "call_tool",
                "__end__": END
            }
        )
        
        # 编译图
        return workflow.compile()
    
    def process_message(self, message: str, chat_history: list = None) -> str:
        """处理用户消息
        
        Args:
            message: 用户消息
            chat_history: 聊天历史
            
        Returns:
            助手回复
        """
        # 处理默认值
        if chat_history is None:
            chat_history = []
            
        # 转换聊天历史格式
        messages = []
        for msg in chat_history:
            if msg["role"] == "user":
                messages.append(HumanMessage(content=msg["content"]))
            elif msg["role"] == "assistant":
                messages.append(AIMessage(content=msg["content"]))
                
        # 添加用户消息
        messages.append(HumanMessage(content=message))
            
        # 初始化状态
        state = {
            "messages": messages,
            "sender": "user",
            "classification": None,
            "knowledge_data": None,
            "mqtt_data": None,
            "final_answer": None,
            "error": None
        }
        
        # 执行图
        try:
            # 记录日志
            logger.info(f"开始处理用户消息: {message[:100]}...")
            
            result = self.graph.invoke(state)
            
            # 获取最后一条消息内容
            last_message = result["messages"][-1]
            return last_message.content
        except Exception as e:
            logger.error(f"处理用户消息时出错: {str(e)}")
            return f"处理消息时出错: {str(e)}"
    
    def process_message_stream(self, message: str, chat_history: list = None):
        """流式处理用户消息
        
        Args:
            message: 用户消息
            chat_history: 聊天历史
            
        Yields:
            助手回复的流式内容
        """
        # 暂时简单实现，实际应使用流式LLM接口
        response = self.process_message(message, chat_history)
        
        # 模拟流式输出
        words = response.split(' ')
        for i in range(0, len(words), 3):
            chunk = ' '.join(words[i:i+3])
            yield chunk + ' ' 