import asyncio
import os
import logging
import subprocess
import sys
import importlib.util
from pathlib import Path
import time
import json
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
import uvicorn
import uuid
import traceback
from fastapi import BackgroundTasks
from fastapi.responses import JSONResponse
import contextlib
from concurrent.futures import ThreadPoolExecutor

# 导入必要的依赖
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langchain_mcp_adapters.client import MultiServerMCPClient

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(title="LangGraph MCP API")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应当限制为特定的前端域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加调试终端点
@app.get("/")
async def root():
    return {"message": "MCP API服务正常运行"}

# 添加健康检查终端点
@app.get("/health")
async def health():
    global agent
    return {
        "status": "ok" if agent else "initializing",
        "tools_count": len(tools),
        "timestamp": time.time()
    }

# 全局变量存储MCP客户端和工具
mcp_client = None
agent = None
tools = []

# 检查必要的依赖是否已安装
def check_and_install_dependencies():
    """检查并安装必要的依赖"""
    # 直接通过pip子进程检查包是否已安装
    def check_pip_package(package_name):
        try:
            # 运行pip show命令检查包是否已安装
            result = subprocess.run(
                [sys.executable, "-m", "pip", "show", package_name],
                capture_output=True,
                text=True,
                check=False
            )
            if result.returncode == 0:
                logger.debug(f"依赖包 {package_name} 已安装")
                return True
            else:
                logger.warning(f"缺少依赖包: {package_name}")
                return False
        except Exception as e:
            logger.error(f"检查依赖包 {package_name} 时出错: {str(e)}")
            return False
    
    # 检查redmine相关依赖
    redmine_ready = check_pip_package("python-redmine")
    yaml_ready = check_pip_package("pyyaml") 
    fastmcp_ready = check_pip_package("fastmcp")
    
    # 检查MQTT依赖
    mqtt_ready = check_pip_package("paho-mqtt")
    if not mqtt_ready:
        logger.warning("MQTT依赖未就绪，尝试安装paho-mqtt")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "paho-mqtt"])
            mqtt_ready = True
            logger.info("成功安装MQTT依赖")
        except Exception as e:
            logger.error(f"安装MQTT依赖失败: {str(e)}")
    
    all_ready = redmine_ready and yaml_ready and fastmcp_ready
    
    # 检查是否需要安装依赖
    if not all_ready:
        logger.warning("缺少必要的依赖，redmine功能将被禁用")
        logger.info("您可以运行 python install_redmine_deps.py 安装必要的依赖")
        return False
    
    # 检查配置文件
    config_dir = Path("config")
    config_file = config_dir / "config.yaml"
    
    if not config_file.exists():
        logger.warning(f"缺少配置文件: {config_file}")
        logger.info("您可以运行 python install_redmine_deps.py 创建必要的配置文件")
        return False
    
    return True

# 添加超时控制
async def with_timeout(coro, timeout=15):
    """为异步操作添加超时控制，默认超时时间为15秒"""
    try:
        logger.debug(f"运行异步操作，超时时间: {timeout}秒")
        return await asyncio.wait_for(coro, timeout=timeout)
    except asyncio.TimeoutError:
        logger.warning(f"异步操作超时（{timeout}秒）")
        return None
    except RuntimeError as e:
        if "cancel scope" in str(e):
            logger.warning(f"异步操作取消范围错误: {str(e)}")
            # 返回None而不是抛出异常，以允许程序继续运行
            return None
        logger.error(f"异步操作运行时错误: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"异步操作出错: {str(e)}")
        return None

# 简化MCP客户端设置过程
async def setup_mcp_client():
    """初始化MCP客户端和工具，带有完善的错误处理和日志记录"""
    global mcp_client, tools, agent
    
    logger.info("开始初始化MCP客户端...")
    
    try:
        # 1. 检查依赖
        deps_ready = check_and_install_dependencies()
        logger.info(f"依赖检查完成: {'成功' if deps_ready else '失败'}")
        
        # 检查MQTT依赖，仅检查不安装
        mqtt_ready = False
        try:
            result = subprocess.run(
                [sys.executable, "-m", "pip", "show", "paho-mqtt"],
                capture_output=True,
                text=True,
                check=False
            )
            mqtt_ready = result.returncode == 0
            logger.info(f"MQTT依赖检查: {'已就绪' if mqtt_ready else '未就绪'}")
        except Exception as e:
            logger.error(f"检查MQTT依赖时出错: {str(e)}")
        
        # 2. 准备服务器配置 - 仅保留最基本服务器
        logger.info("准备服务器配置...")
        servers = {
            "math": {
                "command": "python",
                "args": ["example_math_server.py"],
                "transport": "stdio",
            },
            "weather": {
                "command": "python",
                "args": ["example_weather_server.py"],
                "transport": "stdio",
            }
        }
        
        # 逐个添加服务器，确保一个服务器失败不会影响其他服务器
        added_servers = []
        
        # 有条件地添加其他服务器
        if deps_ready:
            try:
                logger.info("添加Redmine服务器")
                servers["redmine"] = {
                    "command": "python",
                    "args": ["redmine_mcp_server.py"],
                    "transport": "stdio",
                }
                added_servers.append("redmine")
            except Exception as e:
                logger.error(f"添加Redmine服务器时出错: {str(e)}")
        
        # 添加MQTT服务器
        if mqtt_ready:
            try:
                mqtt_server_path = os.path.join("mqtt_tools", "mcp_server.py")
                if os.path.exists(mqtt_server_path):
                    logger.info(f"添加MQTT服务器: {mqtt_server_path}")
                    servers["mqtt"] = {
                        "command": "python",
                        "args": [mqtt_server_path],
                        "transport": "stdio",
                    }
                    added_servers.append("mqtt")
                else:
                    logger.warning(f"找不到MQTT服务器文件: {mqtt_server_path}")
            except Exception as e:
                logger.error(f"添加MQTT服务器时出错: {str(e)}")
        
        logger.info(f"服务器配置完成: {list(servers.keys())}")
        
        # 3. 初始化客户端 - 使用超时控制
        logger.info("开始初始化MCP客户端...")
        
        # 创建异步任务并设置超时
        try:
            # 使用单独的函数避免阻塞整个进程
            async def init_client():
                logger.info("正在创建MCP客户端...")
                client = MultiServerMCPClient(servers)
                logger.info("客户端已创建，正在进入上下文...")
                await client.__aenter__()
                logger.info("客户端已进入上下文")
                return client
            
            # 设置超时时间为15秒
            mcp_client = await with_timeout(init_client(), timeout=15)
            
            if mcp_client is None:
                logger.error("MCP客户端初始化超时或失败")
                # 返回False但不直接退出，让应用程序可以使用模拟数据
                if tools:
                    logger.info(f"将继续使用现有的{len(tools)}个工具")
                    return True
                return False
            
            logger.info("MCP客户端初始化成功")
            
            # 4. 获取工具列表
            logger.info("获取MCP工具列表...")
            try:
                new_tools = mcp_client.get_tools()
                logger.info(f"获取到 {len(new_tools)} 个工具")
                tools = new_tools  # 更新全局工具列表
            except Exception as e:
                logger.error(f"获取工具列表时出错: {str(e)}")
                if not tools:
                    logger.warning("没有可用的工具")
                    tools = []  # 确保tools不是None
            
            # 5. 创建大语言模型
            logger.info("创建语言模型...")
            try:
                model = ChatOpenAI(
                    model="qwen2.5:32b",
                    openai_api_base="http://172.16.68.8:11434/v1",
                    openai_api_key="sk-no-key-required",
                    streaming=True
                )
                logger.info("语言模型创建成功")
                
                # 6. 创建代理
                logger.info("创建代理...")
                agent = create_react_agent(model, tools)
                logger.info("代理创建成功")
                
                return True
            except Exception as e:
                logger.error(f"初始化语言模型或代理时出错: {str(e)}")
                return False
        except Exception as e:
            logger.error(f"初始化MCP客户端过程中出错: {str(e)}")
            return False
    except Exception as e:
        logger.error(f"整体初始化过程中出错: {str(e)}")
        return False

@app.on_event("startup")
async def startup_event():
    """应用启动时初始化MCP客户端"""
    logger.info("启动MCP API服务")
    
    # 创建一个后台任务，但不等待它完成
    asyncio.create_task(delayed_setup())

async def delayed_setup():
    """延迟初始化MCP客户端，避免阻塞应用程序启动"""
    logger.info("等待应用程序完全启动...")
    # 先等待2秒，确保应用程序完全启动
    await asyncio.sleep(2)
    logger.info("开始后台初始化MCP客户端...")
    try:
        success = await setup_mcp_client()
        if success:
            logger.info("MCP客户端后台初始化成功")
        else:
            logger.error("MCP客户端后台初始化失败，某些功能可能不可用")
    except Exception as e:
        logger.error(f"后台初始化MCP客户端时出错: {str(e)}")
        # 捕获并记录错误，但不影响应用程序运行

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭时清理资源"""
    global mcp_client
    logger.info("关闭MCP API服务")
    
    if mcp_client:
        try:
            await mcp_client.__aexit__(None, None, None)
            logger.info("MCP客户端已关闭")
        except Exception as e:
            logger.error(f"关闭MCP客户端时出错: {str(e)}")

def format_choice_delta(content):
    """格式化为OpenAI兼容的增量更新格式"""
    return {
        "choices": [
            {
                "delta": {"content": content},
                "finish_reason": None,
                "index": 0
            }
        ]
    }

def format_sse_message(data):
    """格式化为SSE消息格式"""
    if data == "[DONE]":
        return f"data: {data}\n\n"
    else:
        return f"data: {json.dumps(data)}\n\n"

def get_initial_response(chat_id, request_id, response_id, assistant_id):
    """返回初始响应消息"""
    return {
        "id": f"chatcmpl-{response_id}",
        "object": "chat.completion.chunk",
        "created": int(time.time()),
        "model": "local-mcp-model",
        "choices": [
            {
                "index": 0,
                "delta": {
                    "role": "assistant"
                },
                "finish_reason": None
            }
        ]
    }

@app.get("/api/v1/chats_openai/{chat_id}/chat/completions")
@app.post("/api/v1/chats_openai/{chat_id}/chat/completions")
async def chat_completions(request: Request, chat_id: str, background_tasks: BackgroundTasks):
    """
    处理聊天完成请求的API端点。
    """
    try:
        logging.info(f"收到聊天请求: {chat_id}")
        request_id = str(uuid.uuid4())

        # 获取请求体
        json_data = await request.json()
        
        # 打印请求参数，但忽略敏感信息
        sanitized_data = json_data.copy()
        if "messages" in sanitized_data:
            sanitized_data["messages"] = f"[{len(sanitized_data['messages'])} messages]"
        logging.info(f"请求参数: {sanitized_data}")
        
        messages = json_data.get("messages", [])
        tools = json_data.get("tools", None)
        stream = json_data.get("stream", True)
        
        if not tools and not messages:
            return JSONResponse(
                status_code=400,
                content={"error": "请求必须包含消息或工具参数"}
            )
                
        # 创建SSE响应流
        async def event_generator():
            response_id = str(uuid.uuid4())
            assistant_id = str(uuid.uuid4())
            
            try:
                # 发送初始的SSE消息
                yield f"data: {json.dumps(get_initial_response(chat_id, request_id, response_id, assistant_id))}\n\n"
                
                # 创建处理流式响应的函数
                def create_response_chunk(content_text):
                    nonlocal request_id, response_id, assistant_id
                    
                    # 检查是否是工具调用结果
                    is_tool_result = False
                    if content_text and content_text.startswith("[工具"):
                        is_tool_result = True
                        # 给工具调用结果添加标记
                        content_text = "<tool-usage-mark></tool-usage-mark>" + content_text
                    
                    # 创建消息块
                    delta = {}
                    if content_text is not None:
                        delta["content"] = content_text
                    
                    # 创建响应块
                    chunk = {
                        "id": f"chatcmpl-{response_id}",
                        "object": "chat.completion.chunk",
                        "created": int(time.time()),
                        "model": "local-mcp-model",
                        "choices": [
                            {
                                "index": 0,
                                "delta": delta,
                                "finish_reason": None
                            }
                        ]
                    }
                    return f"data: {json.dumps(chunk)}\n\n"
                
                # 处理用户消息并调用AI响应
                try:
                    # 将前端消息格式转换为LangGraph格式
                    formatted_messages = []
                    for msg in messages:
                        if msg.get("role") == "user":
                            formatted_messages.append({"type": "human", "content": msg.get("content", "")})
                        elif msg.get("role") == "assistant":
                            formatted_messages.append({"type": "ai", "content": msg.get("content", "")})
                        elif msg.get("role") == "system":
                            formatted_messages.append({"type": "system", "content": msg.get("content", "")})
                    
                    # 首先要发送一个思考中的消息
                    yield create_response_chunk("思考中...")
                    
                    # 调用代理处理消息获取AI响应
                    if not agent:
                        yield create_response_chunk("抱歉，AI代理尚未初始化。请稍后再试。")
                        return
                    
                    # 调用AI代理获取响应
                    print(f"调用AI代理处理消息: {formatted_messages}")
                    ai_response = await agent.ainvoke({"messages": formatted_messages})
                    print(f"代理返回结果: {ai_response}")
                
                    # 定义数学工具列表 - 仅用于简单标记，不影响处理顺序
                    math_tools = ["add", "subtract", "multiply", "divide"]
                    
                    # 处理工具调用信息 - 保持简单直接
                    message_id = str(uuid.uuid4())
                    tool_messages = []
                    has_tool_call = False
                    
                    # 从AI响应中提取工具调用 - 保持原始顺序
                    for message in ai_response.get("messages", []):
                        message_type = type(message).__name__
                        
                        # 处理工具消息
                        if message_type == "ToolMessage" and hasattr(message, "content") and message.content:
                            has_tool_call = True
                            tool_name = message.name if hasattr(message, "name") else "unknown_tool"
                            tool_result = message.content.strip()
                            
                            # 添加调试信息
                            print(f"工具调用: {tool_name} -> {tool_result}")
                            
                            # 为数学工具添加特殊标记，但不改变顺序
                            is_math = tool_name in math_tools
                            mark_type = 'data-math-operation="true"' if is_math else ''
                            
                            # 构建简单的工具消息格式，确保当mark_type为空时没有空格
                            if mark_type:
                                tool_message = f'<tool-usage-mark {mark_type}></tool-usage-mark>\n\n[工具 {tool_name}]: {tool_result}\n\n'
                            else:
                                tool_message = f'<tool-usage-mark></tool-usage-mark>\n\n[工具 {tool_name}]: {tool_result}\n\n'
                            tool_messages.append(tool_message)
                    
                    # 合并所有工具消息，保持原始顺序
                    if tool_messages:
                        combined_messages = "".join(tool_messages)
                        print(f"合并后的工具消息: {combined_messages}")
                        
                        # 发送工具结果
                        yield create_response_chunk(combined_messages)
                    
                    # 处理AI最终消息 - 找到工具调用之后的AI消息作为结果解释
                    ai_final_message = None
                    for message in ai_response.get("messages", []):
                        message_type = type(message).__name__
                        
                        # 处理AI消息 - 查找最后一条AI消息，通常是对工具结果的解释
                        if message_type == "AIMessage" and hasattr(message, "content") and message.content.strip():
                            # 过滤掉不必要的内容
                            content = message.content.strip()
                            # 过滤掉XML/im标记
                            if "<|im_start|>" in content:
                                content = content.split("<|im_start|>")[0].strip()
                            
                            # 更新最终消息，始终使用找到的最后一条AI消息
                            ai_final_message = content
                    
                    # 发送AI最终消息，这通常是对工具调用结果的解释
                    # 对于工具调用，我们也需要发送AI的解释
                    if ai_final_message:
                        print(f"AI最终消息: {ai_final_message}")
                        # 添加特殊标记，明确标示这是AI的最终消息，便于前端识别
                        yield create_response_chunk(f"<ai-final-message>\n{ai_final_message}\n</ai-final-message>")
                
                except Exception as e:
                    error_message = f"处理AI对话时出错: {str(e)}"
                    print(error_message)
                    traceback.print_exc()
                    yield create_response_chunk(error_message)

                # 发送结束消息
                finish_chunk = {
                    "id": f"chatcmpl-{response_id}",
                    "object": "chat.completion.chunk",
                    "created": int(time.time()),
                    "model": "local-mcp-model",
                    "choices": [
                        {
                            "index": 0,
                            "delta": {},
                            "finish_reason": "stop"
                        }
                    ]
                }
                yield f"data: {json.dumps(finish_chunk)}\n\n"
                yield "data: [DONE]\n\n"
            except Exception as e:
                error_msg = {
                    "error": {
                        "message": str(e),
                        "type": "api_error",
                        "code": "internal_error"
                    }
                }
                yield f"data: {json.dumps(error_msg)}\n\n"
                yield "data: [DONE]\n\n"
                logging.error(f"处理流式响应错误: {str(e)}")
                traceback.print_exc()

        return StreamingResponse(event_generator(), media_type="text/event-stream")

    except Exception as e:
        logger.error(f"处理请求时出错: {str(e)}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": str(e)}
        )

@app.get("/api/tools")
async def list_tools():
    """列出所有可用的工具"""
    global tools
    
    tool_list = []
    for tool in tools:
        tool_list.append({
            "name": tool.name,
            "description": tool.description,
            "parameters": tool.args if hasattr(tool, "args") else {}
        })
    
    return {"tools": tool_list}

if __name__ == "__main__":
    logger.info("正在启动MCP API服务...")
    uvicorn.run(app, host="0.0.0.0", port=8080) 