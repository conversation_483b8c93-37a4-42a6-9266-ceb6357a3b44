<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端TTS配置测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端TTS配置测试</h1>
        
        <div class="test-section">
            <h3>TTS服务状态检查</h3>
            <button onclick="checkTTSService()">检查TTS服务</button>
            <div id="serviceStatus" class="status info">等待检查...</div>
        </div>

        <div class="test-section">
            <h3>固定音色配置测试</h3>
            <p>测试文本包含A2日报，验证字母数字组合的朗读效果</p>
            <button onclick="testFixedVoice()">测试固定音色</button>
            <button onclick="testA2Reading()">测试A2日报朗读</button>
            <button onclick="testConsistency()">测试音色一致性</button>
            <div id="voiceTestStatus" class="status info">等待测试...</div>
        </div>

        <div class="test-section">
            <h3>TTS配置信息</h3>
            <div id="configInfo" class="log">
                固定TTS配置：<br>
                - voice_profile: "serious_male"<br>
                - voice_seed: 42<br>
                - audio_seed: 123<br>
                - temperature: 0.1<br>
                - top_p: 0.5<br>
                - top_k: 10<br>
                - preprocess_text: true
            </div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log">等待测试开始...</div>
        </div>
    </div>

    <script>
        const TTS_SERVICE_URL = 'http://localhost:8003';
        
        // 固定TTS配置 - 与前端组件保持一致
        const TTS_CONFIG = {
            voice_profile: "serious_male",
            voice_seed: 42,
            audio_seed: 123,
            temperature: 0.1,
            top_p: 0.5,
            top_k: 10,
            refine_text: true,
            preprocess_text: true
        };

        function log(message) {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '日志已清空<br>';
        }

        async function checkTTSService() {
            const statusDiv = document.getElementById('serviceStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = '检查中...';
            
            try {
                const response = await fetch(`${TTS_SERVICE_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `
                        TTS服务正常<br>
                        状态: ${data.status}<br>
                        模型已加载: ${data.model_loaded}<br>
                        设备: ${data.device}<br>
                        默认音色: ${data.default_voice || 'N/A'}<br>
                        固定音色种子: ${data.fixed_voice_seed || 'N/A'}
                    `;
                    log('TTS服务检查成功');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `TTS服务连接失败: ${error.message}`;
                log(`TTS服务检查失败: ${error.message}`);
            }
        }

        async function testTTS(text, testName) {
            log(`开始${testName}测试: "${text}"`);
            
            try {
                const response = await fetch(`${TTS_SERVICE_URL}/tts`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: text,
                        ...TTS_CONFIG
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                const result = await response.json();
                
                if (result.success) {
                    // 播放音频
                    const audioUrl = `data:audio/wav;base64,${result.audio_base64}`;
                    const audio = new Audio(audioUrl);
                    
                    audio.addEventListener('loadeddata', () => {
                        log(`${testName}音频加载成功，时长: ${result.duration?.toFixed(2)}秒`);
                    });
                    
                    audio.addEventListener('ended', () => {
                        log(`${testName}音频播放完成`);
                    });
                    
                    audio.addEventListener('error', (e) => {
                        log(`${testName}音频播放错误: ${e.message}`);
                    });
                    
                    await audio.play();
                    log(`${testName}开始播放，使用配置: ${JSON.stringify(TTS_CONFIG)}`);
                    
                    return true;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                log(`${testName}测试失败: ${error.message}`);
                return false;
            }
        }

        async function testFixedVoice() {
            const statusDiv = document.getElementById('voiceTestStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = '测试中...';
            
            const success = await testTTS('这是固定音色测试，使用严肃智慧的男性声音。', '固定音色');
            
            if (success) {
                statusDiv.className = 'status success';
                statusDiv.textContent = '固定音色测试成功';
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '固定音色测试失败';
            }
        }

        async function testA2Reading() {
            const statusDiv = document.getElementById('voiceTestStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = '测试A2朗读中...';
            
            const success = await testTTS('点击查看完整的作业区生产油井A2日报', 'A2朗读');
            
            if (success) {
                statusDiv.className = 'status success';
                statusDiv.textContent = 'A2朗读测试成功 - 请听A2是否被读作"A二"';
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = 'A2朗读测试失败';
            }
        }

        async function testConsistency() {
            const statusDiv = document.getElementById('voiceTestStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = '测试音色一致性中...';
            
            const text = 'A2日报音色一致性测试';
            let successCount = 0;
            
            for (let i = 1; i <= 3; i++) {
                log(`第${i}次一致性测试`);
                const success = await testTTS(text, `一致性测试${i}`);
                if (success) successCount++;
                
                // 等待1秒再进行下一次测试
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            if (successCount === 3) {
                statusDiv.className = 'status success';
                statusDiv.textContent = '音色一致性测试成功 - 请对比3次播放的音色是否完全一致';
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = `音色一致性测试部分失败 - ${successCount}/3次成功`;
            }
        }

        // 页面加载时自动检查TTS服务
        window.addEventListener('load', () => {
            log('页面加载完成，开始自动检查TTS服务');
            checkTTSService();
        });
    </script>
</body>
</html>
