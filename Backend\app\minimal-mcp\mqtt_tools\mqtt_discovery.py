import paho.mqtt.client as mqtt
import json
import time
import os
import logging
import requests
from typing import Dict, List, Set, Any
import re
import threading

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 全局变量
CLIENT_ID = "mqtt-discovery"  # 固定的客户端ID，需与EMQX ACL规则匹配
discovered_topics = set()
mqtt_client = None
connected = False
CONFIG_FILE = "config.json"

# 手动添加的基本主题
BASIC_TOPICS = [
    "/HN3S1/JH001",  # 井01
    "/HN3S1/JH002",  # 井02
    "/HN3S1/JH003",  # 井03
    "/HN3S1/JH004",  # 井04
    "/HN3S1/JH005",  # 井05
    "/HN3S1/HN15V3",  # 井06
    "/HN3S1/HN15V4",  # 井07
    "/HN3S1/HN15V25"  # 井08
]

# EMQX连接配置 - 支持Docker和标准部署
EMQX_CONFIGS = [
    # 当前正常工作的配置
    {
        "host": "************",
        "tcp_port": 1884,
        "ws_port": 8083,
        "api_port": 5712,
        "api_path": "/api/v5"
    }
]

# 系统主题 - 用于监控活跃主题
SYS_TOPICS = [
    "$SYS/brokers/+/stats/topics/count",     # 主题计数
    "$SYS/brokers/+/topics",                 # 主题列表
    "$SYS/brokers/+/stats/pubsub/topics",    # 发布/订阅主题统计
    "$SYS/brokers/+/clients/+/connected",    # 客户端连接事件
    "$SYS/brokers/+/clients/+/disconnected", # 客户端断开连接事件
    "$SYS/brokers/+/clients/+/#",            # 客户端所有信息
    "$SYS/brokers/+/stats/#",                # 所有统计信息
    "$SYS/#"                                 # 所有系统主题
]

# 接收到的系统主题数据
sys_topics_data = {}
topic_discovery_event = threading.Event()

# MQTT回调函数
def on_connect(client, userdata, flags, rc):
    """连接回调函数"""
    global connected
    if rc == 0:
        logger.info("已连接到MQTT服务器")
        connected = True
        
        # 先订阅系统主题以获取主题信息
        for sys_topic in SYS_TOPICS:
            result, mid = client.subscribe(sys_topic)
            if result == mqtt.MQTT_ERR_SUCCESS:
                logger.info(f"已订阅系统主题: {sys_topic}")
            else:
                logger.warning(f"订阅系统主题失败: {sys_topic}, 错误码: {result}")
        
        # 订阅所有主题 (使用通配符)
        result, mid = client.subscribe("#")
        if result == mqtt.MQTT_ERR_SUCCESS:
            logger.info("已订阅所有普通主题")
        else:
            logger.warning(f"订阅所有主题失败, 错误码: {result}")
        
        # 额外订阅特定主题
        for topic in BASIC_TOPICS:
            result, mid = client.subscribe(topic)
            if result == mqtt.MQTT_ERR_SUCCESS:
                logger.info(f"已订阅预定义主题: {topic}")
            else:
                logger.warning(f"订阅预定义主题失败: {topic}, 错误码: {result}")
            
            # 也将这些基本主题添加到发现列表中
            discovered_topics.add(topic)
            
        # 发布一些消息到预定义主题，刺激可能的响应
        for topic in BASIC_TOPICS:
            try:
                client.publish(topic, json.dumps({"action": "discover", "timestamp": time.time()}), qos=0)
                logger.debug(f"向主题 {topic} 发送探测消息")
            except Exception as e:
                logger.warning(f"发送探测消息到 {topic} 失败: {e}")
    else:
        logger.error(f"连接MQTT服务器失败，返回码: {rc}")
        connected = False

def on_message(client, userdata, msg):
    """消息回调函数"""
    topic = msg.topic
    
    # 将主题添加到已发现列表
    discovered_topics.add(topic)
    
    # 如果是系统主题，提取主题信息
    if topic.startswith("$SYS/"):
        try:
            payload = msg.payload.decode('utf-8')
            sys_topics_data[topic] = payload
            logger.debug(f"接收到系统主题 {topic}: {payload}")
            
            # 如果是主题列表相关的主题
            if topic.endswith("/topics") or "topics" in topic:
                try:
                    # 尝试解析JSON
                    data = json.loads(payload)
                    if isinstance(data, list):
                        for t in data:
                            discovered_topics.add(t)
                            logger.debug(f"从系统主题中提取主题: {t}")
                    elif isinstance(data, dict):
                        # 处理字典格式数据
                        if "data" in data and isinstance(data["data"], list):
                            # EMQX API格式
                            for item in data["data"]:
                                if isinstance(item, dict) and "topic" in item:
                                    t = item["topic"]
                                    discovered_topics.add(t)
                                    logger.debug(f"从系统主题中提取主题: {t}")
                                elif isinstance(item, str):
                                    discovered_topics.add(item)
                                    logger.debug(f"从系统主题中提取主题: {item}")
                        else:
                            # 直接遍历字典的键
                            for t in data.keys():
                                discovered_topics.add(t)
                                logger.debug(f"从系统主题中提取主题: {t}")
                except json.JSONDecodeError:
                    # 如果不是JSON，尝试按行分割
                    topics = payload.strip().split("\n")
                    for t in topics:
                        t = t.strip()
                        if t:
                            discovered_topics.add(t)
                            logger.debug(f"从系统主题中提取主题: {t}")
            
            # 系统主题的特殊处理
            if "clients" in topic and ("connected" in topic or "disconnected" in topic):
                logger.info(f"客户端事件: {topic}")
                
                # 可能需要更新连接状态或重新订阅
                if "disconnected" in topic and CLIENT_ID in topic:
                    logger.warning(f"客户端断开连接: {topic}")
                    # 可能的重连逻辑
            
            # 设置发现事件，表示已收到系统主题数据
            topic_discovery_event.set()
                
        except Exception as e:
            logger.warning(f"处理系统主题 {topic} 失败: {e}")
    else:
        logger.info(f"发现主题: {topic}")
        
        # 尝试解析消息内容
        try:
            payload = msg.payload.decode('utf-8')
            # 限制日志输出长度
            log_payload = payload[:100] + "..." if len(payload) > 100 else payload
            logger.debug(f"主题 {topic} 的消息: {log_payload}")
        except:
            logger.debug(f"主题 {topic} 的消息无法解码")

def load_config() -> Dict[str, Any]:
    """加载配置文件"""
    config_path = CONFIG_FILE
    if not os.path.exists(config_path):
        # 创建默认配置
        default_config = {
            "mqtt": {
                "host": "************",
                "port": 8083,
                "path": "/mqtt"
            },
            "devices": []
        }
        return default_config
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
            return config
    except Exception as e:
        logger.error(f"加载配置文件失败: {e}")
        return {
            "mqtt": {
                "host": "************",
                "port": 8083,
                "path": "/mqtt"
            },
            "devices": []
        }

def save_config(config: Dict[str, Any]) -> bool:
    """保存配置文件"""
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        logger.info(f"配置已保存到 {CONFIG_FILE}")
        return True
    except Exception as e:
        logger.error(f"保存配置文件失败: {e}")
        return False

def connect_mqtt(mqtt_config: Dict[str, Any]) -> mqtt.Client:
    """连接到MQTT服务器"""
    global mqtt_client, connected
    
    # 获取配置
    host = mqtt_config.get("host", "************")
    ws_port = mqtt_config.get("port", 8083)
    tcp_port = 1884  # 备用TCP端口
    path = mqtt_config.get("path", "/mqtt")
    
    logger.info(f"尝试连接MQTT服务器: {host}")
    
    # 首先尝试WebSocket连接（优先方式）
    client = try_websocket_connection(host, ws_port, path)
    if client:
        return client
        
    # 如果WebSocket失败，尝试TCP连接
    logger.info("WebSocket连接失败，尝试TCP连接")
    client = try_tcp_connection(host, tcp_port)
    if client:
        return client
    
    logger.error(f"无法连接到MQTT服务器 {host}")
    return None

def try_websocket_connection(host, port, path="/mqtt") -> mqtt.Client:
    """尝试通过WebSocket连接到MQTT服务器"""
    global mqtt_client, connected
    
    client_id = CLIENT_ID  # 使用全局常量
    logger.info(f"尝试WebSocket连接: {host}:{port}{path}")
    
    try:
        connected = False
        client = mqtt.Client(client_id, transport="websockets")
        client.ws_set_options(path=path)
        
        # 设置回调函数
        client.on_connect = on_connect
        client.on_message = on_message
        
        # 设置连接超时
        connect_timeout = 3  # 3秒连接超时
        client.connect(host, port, connect_timeout)
        client.loop_start()
        
        # 等待连接建立
        retry_count = 0
        while not connected and retry_count < 5:
            time.sleep(0.5)
            retry_count += 1
            logger.debug(f"等待WebSocket连接... {retry_count}/5")
        
        if connected:
            logger.info(f"WebSocket连接成功: {host}:{port}")
            mqtt_client = client
            return client
        else:
            logger.debug(f"WebSocket连接超时: {host}:{port}")
            client.loop_stop()
            return None
            
    except Exception as e:
        logger.debug(f"WebSocket连接失败 {host}:{port}: {str(e)}")
        return None

def try_tcp_connection(host, port) -> mqtt.Client:
    """尝试通过TCP连接到MQTT服务器"""
    global mqtt_client, connected
    
    client_id = CLIENT_ID  # 使用全局常量
    logger.info(f"尝试TCP连接: {host}:{port}")
    
    try:
        connected = False
        client = mqtt.Client(client_id)
        
        # 设置回调函数
        client.on_connect = on_connect
        client.on_message = on_message
        
        # 设置连接超时
        connect_timeout = 3  # 3秒连接超时
        client.connect(host, port, connect_timeout)
        client.loop_start()
        
        # 等待连接建立
        retry_count = 0
        while not connected and retry_count < 5:
            time.sleep(0.5)
            retry_count += 1
            logger.debug(f"等待TCP连接... {retry_count}/5")
        
        if connected:
            logger.info(f"TCP连接成功: {host}:{port}")
            mqtt_client = client
            return client
        else:
            logger.debug(f"TCP连接超时: {host}:{port}")
            client.loop_stop()
            return None
            
    except Exception as e:
        logger.debug(f"TCP连接失败 {host}:{port}: {str(e)}")
        return None

def try_emqx_api_discovery(mqtt_config: Dict[str, Any]) -> List[str]:
    """尝试使用EMQX HTTP API获取主题列表"""
    api_topics = []
    
    # 使用配置中的服务器
    host = mqtt_config.get("host", "127.0.0.1")
    api_port = mqtt_config.get("api_port", 5712)
    api_path = "/api/v5"
    
    logger.info(f"尝试API获取主题: http://{host}:{api_port}{api_path}/topics")
    
    topics = try_single_api_endpoint(host, api_port, api_path)
    if topics:
        api_topics.extend(topics)
        logger.info(f"通过API从 {host}:{api_port} 发现了 {len(topics)} 个主题")
    
    return api_topics

def try_single_api_endpoint(host, api_port, api_path) -> List[str]:
    """尝试单个API端点获取主题"""
    # 尝试访问API (默认用户名/密码为admin/public)
    username = "admin"
    password = "public"
    
    logger.info(f"尝试API获取主题: http://{host}:{api_port}{api_path}/topics")
    
    try:
        # 获取认证token
        auth_url = f"http://{host}:{api_port}{api_path}/login"
        auth_data = {"username": username, "password": password}
        
        auth_response = requests.post(auth_url, json=auth_data, timeout=2)
        
        if auth_response.status_code != 200:
            logger.debug(f"API认证失败: HTTP {auth_response.status_code}")
            return []
        
        token = auth_response.json().get("token")
        if not token:
            logger.debug("API认证成功但未获取到token")
            return []
        
        # 获取主题列表
        topics_url = f"http://{host}:{api_port}{api_path}/topics"
        headers = {"Authorization": f"Bearer {token}"}
        
        topics_response = requests.get(topics_url, headers=headers, timeout=2)
        
        if topics_response.status_code != 200:
            logger.debug(f"获取主题列表失败: HTTP {topics_response.status_code}")
            return []
        
        # 解析主题数据
        topics_data = topics_response.json()
        topics = []
        
        if "data" in topics_data and isinstance(topics_data["data"], list):
            for topic_info in topics_data["data"]:
                if "topic" in topic_info and isinstance(topic_info["topic"], str):
                    topics.append(topic_info["topic"])
                    
        logger.info(f"通过API发现了 {len(topics)} 个主题")
        return topics
        
    except requests.RequestException as e:
        logger.debug(f"HTTP API请求失败 {host}:{api_port}: {e}")
        return []
    except Exception as e:
        logger.debug(f"尝试通过API获取主题时出错 {host}:{api_port}: {e}")
        return []

def parse_topic_to_device(topic: str) -> Dict[str, str]:
    """
    从主题解析设备信息
    
    假设主题格式为: /{station}/{wellname}
    """
    parts = topic.strip('/').split('/')
    
    if len(parts) >= 2:
        station = parts[0]
        wellname = parts[1]
        
        # 生成唯一的显示名称
        # 使用当前时间戳和设备名称生成随机数
        import time
        
        # 提取数字部分作为参考
        num_match = re.search(r'\d+', wellname)
        if num_match:
            # 提取数字部分
            display_num = int(num_match.group())
            
            # 如果是HN开头的命名方式，使用更随机的编号
            if wellname.startswith("HN"):
                # 使用哈希值生成五位数(10000-99999)的随机编号
                hash_val = abs(hash(wellname + str(time.time()))) % 90000 + 10000
                display_name = f"井H{hash_val}"
            else:
                # 对于JH系列，在原有编号基础上添加随机三位数
                random_suffix = abs(hash(wellname + str(time.time()))) % 1000
                display_name = f"井{display_num:02d}-{random_suffix:03d}"
        else:
            # 如果没有数字，使用五位数(10000-99999)随机编号
            hash_val = abs(hash(station + wellname + str(time.time()))) % 90000 + 10000
            display_name = f"井S{hash_val}"
        
        return {
            "station": station,
            "wellname": wellname,
            "display_name": display_name
        }
    
    return None

def update_config_with_discovered_topics():
    """使用发现的主题更新配置文件"""
    # 加载当前配置
    config = load_config()
    
    # 提取现有设备列表
    existing_devices = config.get("devices", [])
    existing_topics = set()
    
    # 创建现有主题的集合，用于去重
    for device in existing_devices:
        if "station" in device and "wellname" in device:
            topic = f"/{device['station']}/{device['wellname']}"
            existing_topics.add(topic)
    
    # 处理新发现的主题
    new_devices = []
    for topic in discovered_topics:
        # 跳过系统主题($开头)和已存在的主题
        if topic.startswith('$') or topic in existing_topics:
            continue
        
        # 解析主题为设备信息
        device_info = parse_topic_to_device(topic)
        if device_info:
            new_devices.append(device_info)
            logger.info(f"发现新设备: {device_info['display_name']} ({topic})")
    
    # 更新配置
    if new_devices:
        config["devices"].extend(new_devices)
        save_config(config)
        logger.info(f"已添加 {len(new_devices)} 个新设备到配置")
    else:
        logger.info("没有发现新设备")
    
    return len(new_devices)

def main():
    """主函数"""
    try:
        # 加载配置
        config = load_config()
        mqtt_config = config.get("mqtt", {})
        
        # 尝试连接MQTT服务器
        client = connect_mqtt(mqtt_config)
        if not client:
            logger.error("无法连接到MQTT服务器")
            
            # 即使无法连接，也可以使用内置的基本主题
            logger.info("使用预定义主题继续...")
            for topic in BASIC_TOPICS:
                discovered_topics.add(topic)
            
            # 更新配置
            new_count = update_config_with_discovered_topics()
            
            if new_count > 0:
                logger.info(f"已添加 {new_count} 个基本设备到配置文件")
                logger.info(f"请运行主应用程序: python custom_interactive_client_sse.py")
            else:
                logger.info("没有新设备添加，配置未更改")
            return
        
        # 尝试使用EMQX HTTP API获取主题列表
        api_topics = try_emqx_api_discovery(mqtt_config)
        if api_topics:
            for topic in api_topics:
                discovered_topics.add(topic)
        
        # 等待系统主题数据
        logger.info("等待MQTT系统主题数据...")
        wait_time = 15  # 秒
        if topic_discovery_event.wait(timeout=wait_time):
            logger.info("成功接收到系统主题数据")
        else:
            logger.warning(f"等待系统主题数据超时({wait_time}秒)，将继续发现过程")
        
        # 发布探测消息到预定义主题
        logger.info("发布探测消息到预定义主题...")
        for topic in BASIC_TOPICS:
            try:
                client.publish(topic, json.dumps({"probe": True, "client": CLIENT_ID, "timestamp": time.time()}))
                logger.debug(f"已发送探测消息到: {topic}")
            except Exception as e:
                logger.warning(f"发送探测消息到 {topic} 失败: {str(e)}")
        
        # 监听一段时间以发现主题
        logger.info("正在监听MQTT主题，按Ctrl+C停止...")
        discovery_time = 30  # 监听30秒
        try:
            logger.info(f"将监听 {discovery_time} 秒以发现主题...")
            time.sleep(discovery_time)
        except KeyboardInterrupt:
            logger.info("用户中断监听")
        
        # 停止MQTT客户端
        if client:
            client.loop_stop()
            client.disconnect()
        
        # 打印发现的主题
        logger.info(f"共发现 {len(discovered_topics)} 个主题")
        
        # 分类打印
        system_topics = [t for t in discovered_topics if t.startswith('$')]
        normal_topics = [t for t in discovered_topics if not t.startswith('$')]
        
        if system_topics:
            logger.info(f"系统主题 ({len(system_topics)}):")
            for topic in sorted(system_topics)[:10]:  # 限制输出数量
                logger.info(f"- {topic}")
            if len(system_topics) > 10:
                logger.info(f"... 以及 {len(system_topics) - 10} 个其他系统主题")
                
        if normal_topics:
            logger.info(f"普通主题 ({len(normal_topics)}):")
            for topic in sorted(normal_topics):
                logger.info(f"- {topic}")
        
        # 更新配置
        new_count = update_config_with_discovered_topics()
        
        logger.info("主题发现完成")
        if new_count > 0:
            logger.info(f"已添加 {new_count} 个新设备到配置文件")
            logger.info(f"请运行主应用程序: python custom_interactive_client_sse.py")
        else:
            logger.info("没有发现新设备，配置未更改")
    
    except KeyboardInterrupt:
        logger.info("用户中断程序")
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
    finally:
        # 确保MQTT客户端已关闭
        if mqtt_client:
            try:
                mqtt_client.loop_stop()
                mqtt_client.disconnect()
            except:
                pass

if __name__ == "__main__":
    main() 