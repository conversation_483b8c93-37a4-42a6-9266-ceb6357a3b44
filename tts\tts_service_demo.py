#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChatTTS 演示服务
用于在模型下载失败时提供API结构测试
"""

import os
import io
import base64
import wave
import numpy as np
from typing import Optional
from pathlib import Path

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
import uvicorn
from loguru import logger

# 配置日志
logger.add("logs/tts_demo.log", rotation="1 day", retention="7 days", level="INFO")

# 创建FastAPI应用
app = FastAPI(
    title="ChatTTS 演示服务",
    description="ChatTTS演示版本，用于API测试",
    version="1.0.0-demo"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class TTSRequest(BaseModel):
    """TTS请求模型"""
    text: str
    voice_seed: Optional[int] = None
    temperature: Optional[float] = 0.3
    top_p: Optional[float] = 0.7
    top_k: Optional[int] = 20
    audio_seed: Optional[int] = None
    text_seed: Optional[int] = None
    refine_text: Optional[bool] = True

class TTSResponse(BaseModel):
    """TTS响应模型"""
    success: bool
    message: str
    audio_base64: Optional[str] = None
    duration: Optional[float] = None
    sample_rate: Optional[int] = None

def generate_demo_audio(text: str, duration: float = 2.0, sample_rate: int = 24000) -> bytes:
    """
    生成演示音频（简单的正弦波）
    """
    # 生成正弦波音频
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    
    # 根据文本长度调整频率
    base_freq = 440  # A4音符
    freq = base_freq + (len(text) % 200)  # 根据文本长度微调频率
    
    # 生成正弦波
    audio_data = np.sin(2 * np.pi * freq * t) * 0.3
    
    # 添加简单的包络以避免爆音
    fade_samples = int(0.1 * sample_rate)  # 0.1秒淡入淡出
    audio_data[:fade_samples] *= np.linspace(0, 1, fade_samples)
    audio_data[-fade_samples:] *= np.linspace(1, 0, fade_samples)
    
    # 转换为16位整数
    audio_int16 = (audio_data * 32767).astype(np.int16)
    
    # 创建WAV文件
    buffer = io.BytesIO()
    with wave.open(buffer, 'wb') as wav_file:
        wav_file.setnchannels(1)  # 单声道
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_int16.tobytes())
    
    return buffer.getvalue()

@app.get("/")
async def root():
    """根路径，返回服务信息"""
    return {
        "service": "ChatTTS 演示服务",
        "version": "1.0.0-demo",
        "status": "running",
        "mode": "demo",
        "note": "这是演示版本，生成的是测试音频而非真实语音"
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    return {
        "status": "healthy",
        "model_loaded": False,
        "mode": "demo",
        "message": "演示模式运行中"
    }

@app.post("/tts", response_model=TTSResponse)
async def text_to_speech(request: TTSRequest):
    """
    文本转语音接口（演示版）
    """
    try:
        logger.info(f"处理演示TTS请求: {request.text[:50]}...")
        
        # 计算音频时长（基于文本长度）
        duration = max(1.0, min(10.0, len(request.text) * 0.1))  # 1-10秒
        sample_rate = 24000
        
        # 生成演示音频
        audio_bytes = generate_demo_audio(request.text, duration, sample_rate)
        
        # 转换为base64编码
        audio_base64 = base64.b64encode(audio_bytes).decode('utf-8')
        
        logger.info(f"演示TTS处理完成，音频时长: {duration:.2f}秒")
        
        return TTSResponse(
            success=True,
            message=f"演示音频生成成功（文本: {request.text[:20]}...）",
            audio_base64=audio_base64,
            duration=duration,
            sample_rate=sample_rate
        )
        
    except Exception as e:
        logger.error(f"演示TTS处理出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"演示TTS处理失败: {str(e)}")

@app.post("/tts/stream")
async def text_to_speech_stream(request: TTSRequest):
    """
    流式文本转语音接口（演示版）
    """
    try:
        logger.info(f"处理演示流式TTS请求: {request.text[:50]}...")
        
        # 计算音频时长
        duration = max(1.0, min(10.0, len(request.text) * 0.1))
        sample_rate = 24000
        
        # 生成演示音频
        audio_bytes = generate_demo_audio(request.text, duration, sample_rate)
        
        return StreamingResponse(
            io.BytesIO(audio_bytes),
            media_type="audio/wav",
            headers={
                "Content-Disposition": "attachment; filename=demo_tts_output.wav",
                "Content-Length": str(len(audio_bytes))
            }
        )
        
    except Exception as e:
        logger.error(f"演示流式TTS处理出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"演示流式TTS处理失败: {str(e)}")

@app.get("/demo/info")
async def demo_info():
    """演示信息接口"""
    return {
        "mode": "demo",
        "description": "这是ChatTTS的演示版本",
        "features": [
            "API结构测试",
            "前端集成测试", 
            "音频格式验证",
            "网络连接测试"
        ],
        "limitations": [
            "生成的是测试音频，不是真实语音",
            "音频内容为简单正弦波",
            "不支持真实的文本转语音"
        ],
        "next_steps": [
            "下载ChatTTS模型文件",
            "配置网络连接",
            "启动完整版TTS服务"
        ]
    }

if __name__ == "__main__":
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)
    
    print("=" * 60)
    print("ChatTTS 演示服务")
    print("=" * 60)
    print("注意: 这是演示版本，生成的是测试音频而非真实语音")
    print("用途: API结构测试、前端集成测试")
    print("完整功能需要下载ChatTTS模型文件")
    print("=" * 60)
    
    # 启动服务
    uvicorn.run(
        "tts_service_demo:app",
        host="0.0.0.0",
        port=8002,
        reload=False,
        log_level="info"
    )
