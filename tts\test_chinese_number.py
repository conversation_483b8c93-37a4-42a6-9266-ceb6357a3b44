#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中文数字转换
"""

import re

def convert_letter_number_to_chinese(text: str) -> str:
    """
    将字母数字组合转换为中文表达
    A2 -> A二, B1 -> B一
    """
    number_map = {
        '0': '零', '1': '一', '2': '二', '3': '三', '4': '四',
        '5': '五', '6': '六', '7': '七', '8': '八', '9': '九'
    }
    
    def replace_letter_number(match):
        letter = match.group(1)
        number = match.group(2)
        chinese_number = ''.join(number_map.get(digit, digit) for digit in number)
        return f'{letter}{chinese_number}'
    
    text = re.sub(r'([A-Z])(\d+)', replace_letter_number, text)
    return text

def test_conversion():
    """测试转换效果"""
    test_texts = [
        "A2日报",
        "点击查看完整的作业区生产油井A2日报",
        "B1报告",
        "C3数据",
        "A2、B1、C3报告",
        "第A2版"
    ]
    
    for text in test_texts:
        converted = convert_letter_number_to_chinese(text)
        print(f"原始: {text}")
        print(f"转换: {converted}")
        print()

if __name__ == "__main__":
    test_conversion()
