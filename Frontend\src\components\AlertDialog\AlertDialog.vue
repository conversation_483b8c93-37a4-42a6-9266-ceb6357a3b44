<template>
  <div class="alert-dialog-container">
    <!-- 主折叠面板：默认只显示一个按钮和警报数量 -->
    <div class="alert-summary" @click="toggleExpanded" v-if="!isExpanded">
      <div class="alert-badge" v-if="wsStore.totalAlerts > 0">{{ filteredAlertsCount }}</div>
      <button class="expand-btn">
        <span v-if="wsStore.totalAlerts > 0">{{ filteredAlertsCount }}条警报</span>
        <span v-else>警报系统</span>
      </button>
    </div>

    <!-- 展开后的警报列表 -->
    <div v-if="isExpanded" class="expanded-container">
      <div class="alert-header-bar">
        <span>警报中心 ({{ filteredAlerts.length }})</span>
        <button class="collapse-btn" @click="toggleExpanded">收起</button>
      </div>

      <div class="alerts-list">
        <div v-for="alert in paginatedAlerts" :key="alert.id" 
             :class="['alert', `alert-${alert.type}`]">
          <div class="alert-header">
            <strong>{{ alert.title }}</strong>
            <span class="alert-close" @click="dismissAlert(alert.id)">×</span>
          </div>
          <div class="alert-content" v-html="formatAlertContent(alert.content)"></div>
          
          <!-- 如果是合并警报，显示子警报列表 -->
          <div v-if="alert.type === 'merged_alert' && alert.subAlerts && alert.subAlerts.length > 0" 
               class="sub-alerts-container">
            <div class="sub-alerts-header">
              <span>详细异常项 ({{ alert.subAlerts.length }})</span>
            </div>
            <div v-for="subAlert in alert.subAlerts" :key="subAlert.id" 
                 :class="['sub-alert', `sub-alert-${subAlert.alertType}`]">
              <div class="sub-alert-title">{{ subAlert.title }}</div>
              <div class="sub-alert-content" v-html="formatAlertContent(subAlert.content)"></div>
            </div>
          </div>
          
          <div class="alert-meta-info">
            <div v-if="alert.device && alert.device.check_date" class="alert-check-date">数据时间: <strong class="time-highlight">{{ alert.device.check_date }}</strong></div>
            <div v-if="alert.deviceId" class="alert-device">设备: {{ alert.deviceId }}</div>
            <div v-if="alert.updateCount > 0" class="alert-update-info">更新次数: <span class="update-count">{{ alert.updateCount }}</span> | 最后更新: <span class="update-time">{{ formatTime(alert.lastUpdated) }}</span></div>
            <div class="alert-timestamp">首次接收: {{ formatTime(alert.originalTimestamp || alert.timestamp) }}</div>
          </div>
        </div>
      </div>
      
      <!-- 分页控制 -->
      <div class="pagination-controls" v-if="filteredAlerts.length > itemsPerPage">
        <button 
          :disabled="currentPage === 1" 
          @click="currentPage--"
          class="pagination-btn"
        >
          上一页
        </button>
        <span class="pagination-info">{{ currentPage }} / {{ totalPages }}</span>
        <button 
          :disabled="currentPage === totalPages" 
          @click="currentPage++"
          class="pagination-btn"
        >
          下一页
        </button>
      </div>
      
      <!-- 清空所有警报的按钮 -->
      <div v-if="filteredAlerts.length > 0" class="control-panel">
        <button class="clear-btn" @click="clearAlerts">
          清空警报
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import { useWebsocketStore } from '@/stores/websocketStore';
import dayjs from 'dayjs';
import mqtt from 'mqtt';

const wsStore = useWebsocketStore();
let mqttClient = null;

// 分页相关变量
const itemsPerPage = 10; // 每页显示的警报数量
const currentPage = ref(1); // 当前页码

// 计算分页数据
const totalPages = computed(() => {
  return Math.max(1, Math.ceil(filteredAlerts.value.length / itemsPerPage));
});

// 获取过滤后的警报数量，用于折叠状态显示
const filteredAlertsCount = computed(() => {
  return filteredAlerts.value.length;
});

// 直接返回所有警报，不再按类型过滤
const filteredAlerts = computed(() => {
  return wsStore.displayAlerts;
});

// 获取当前页的警报列表
const paginatedAlerts = computed(() => {
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  return filteredAlerts.value.slice(startIndex, startIndex + itemsPerPage);
});

// 心跳相关变量
let heartbeatInterval = null;
let reconnectTimeout = null;
let lastHeartbeatResponse = Date.now();
const HEARTBEAT_INTERVAL = 30000; // 30秒发送一次心跳
const HEARTBEAT_TIMEOUT = 60000; // 60秒没有响应则认为连接断开

// 清理所有定时器
function clearTimers() {
  if (heartbeatInterval) {
    clearInterval(heartbeatInterval);
    heartbeatInterval = null;
  }
  
  if (reconnectTimeout) {
    clearTimeout(reconnectTimeout);
    reconnectTimeout = null;
  }
}

// 开始心跳检测
function startHeartbeat() {
  // 清除之前的定时器
  clearTimers();
  
  // 设置心跳定时器
  heartbeatInterval = setInterval(() => {
    // 检查上次心跳响应时间，如果超过超时时间，则认为连接已断开
    if (Date.now() - lastHeartbeatResponse > HEARTBEAT_TIMEOUT) {
      console.log(`[心跳超时] 超过${HEARTBEAT_TIMEOUT / 1000}秒未收到响应，尝试重新连接`);
      
      // 断开MQTT连接
      if (mqttClient) {
        try {
          mqttClient.end();
        } catch (err) {
          console.error(`[MQTT错误] 关闭连接失败: ${err.message}`);
        }
      }
      
      // 清除心跳定时器
      clearTimers();
      
      // 重新连接
      reconnectTimeout = setTimeout(() => {
        console.log(`[MQTT] 心跳超时后尝试重新连接`);
        setupMQTTClient();
      }, 5000);
      
      return;
    }
    
    // 发送心跳消息 - MQTT客户端会自动处理ping/pong
    lastHeartbeatResponse = Date.now(); // 更新响应时间
  }, HEARTBEAT_INTERVAL);
  
  console.log(`[心跳] 心跳检测已启动，间隔: ${HEARTBEAT_INTERVAL / 1000}秒`);
}

// 折叠状态 - 默认折叠
const isExpanded = ref(false);

// 切换折叠状态
function toggleExpanded() {
  isExpanded.value = !isExpanded.value;
  localStorage.setItem('alertPanelExpanded', isExpanded.value.toString());
  console.log(`[UI状态] 警报面板${isExpanded.value ? '已展开' : '已折叠'}`);
}

// 加载设置
function loadSettings() {
  // 加载面板展开/折叠状态
  const storedExpanded = localStorage.getItem('alertPanelExpanded');
  if (storedExpanded !== null) {
    isExpanded.value = storedExpanded === 'true';
    console.log(`[设置加载] 警报面板初始状态: ${isExpanded.value ? '展开' : '折叠'}`);
  } else {
    // 默认为折叠状态
    isExpanded.value = false;
    localStorage.setItem('alertPanelExpanded', 'false');
    console.log(`[设置加载] 警报面板初始状态: 折叠 (默认)`);
  }
}

// 添加警报 - 确保所有警报都有正确的类型标记
function addAlert(alert) {
  console.log(`[警报接收] 收到新警报: ${alert.title}`);
  console.log(`[警报内容] 类型:${alert.type}, 内容:${alert.content}`);
  
  // 处理设备状态显示，确保状态为未定义时显示为"离线"
  if (alert.content && alert.content.includes('设备状态异常: undefined')) {
    alert.content = alert.content.replace('设备状态异常: undefined', '设备状态异常: 离线');
  }
  
  // 确保警报类型是有效值
  if (!['warning', 'danger', 'info', 'success', 'merged_alert'].includes(alert.type)) {
    // 将alert类型转换为样式类型
    if (alert.type === 'device_alert' || alert.type === 'connection_alert') {
      alert.type = 'warning';
    } else if (alert.type === 'metric_alert') {
      alert.type = 'danger';
    } else if (alert.type === 'camera') {
      alert.type = 'info';
    } else {
      alert.type = 'warning'; // 默认使用warning类型
    }
  }
  
  // 处理合并警报中的子警报
  if (alert.type === 'merged_alert' && alert.subAlerts && alert.subAlerts.length > 0) {
    // 记录合并警报详情，便于调试
    console.log(`[合并警报] 处理合并警报，ID: ${alert.id}, 子警报数: ${alert.subAlerts.length}`);
    
    // 确保子警报也有正确的类型
    alert.subAlerts.forEach(subAlert => {
      if (!['warning', 'danger', 'info', 'success'].includes(subAlert.alertType)) {
        // 转换子警报类型
        if (subAlert.type === 'device_alert' || subAlert.type === 'connection_alert') {
          subAlert.alertType = 'warning';
        } else if (subAlert.type === 'metric_alert') {
          subAlert.alertType = 'danger';
        } else if (subAlert.type === 'camera') {
          subAlert.alertType = 'info';
        } else {
          subAlert.alertType = 'warning'; // 默认使用warning类型
        }
      }
    });
  }
  
  // 设置deviceId便于前端显示，如果device对象中有id但没有设置deviceId
  if (!alert.deviceId && alert.device && alert.device.id) {
    alert.deviceId = alert.device.id;
  }
  
  // 存储到store
  wsStore.addAlertMessage(alert);
  
  // 重置到第一页，确保显示最新警报
  currentPage.value = 1;
}

// 移除警报
function dismissAlert(id) {
  console.log(`[警报关闭] 关闭警报ID: ${id}`);
  // 从store中移除警报
  wsStore.removeAlert(id);
}

// 格式化时间
function formatTime(timestamp) {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss');
}

// 格式化警报内容，高亮显示定时巡检和数据时间
function formatAlertContent(content) {
  if (!content) return '';
  
  // 高亮定时巡检标记
  let formattedContent = content.replace(
    /\[(\d{4}-\d{2}-\d{2}\s\d{2}:\d{2}:\d{2})\s定时巡检\]/g, 
    '<span class="inspection-tag">[$1 定时巡检]</span>'
  );
  
  // 高亮接收警报标记
  formattedContent = formattedContent.replace(
    /^接收警报\s/g,
    '<span class="received-tag">接收警报</span> '
  );
  
  // 高亮数据时间
  formattedContent = formattedContent.replace(
    /数据时间\:\s([\d\-\s\:]+)/g,
    '数据时间: <span class="time-highlight">$1</span>'
  );
  
  return formattedContent;
}

function setupMQTTClient() {
  // 如果已有连接，先关闭
  if (mqttClient) {
    try {
      console.log(`[MQTT] 关闭现有连接`);
      mqttClient.end();
    } catch (err) {
      console.error(`[MQTT错误] 关闭连接失败: ${err.message}`);
    }
  }

  // 等待一段时间再创建新连接，避免频繁连接
  setTimeout(() => {
    wsStore.incrementConnectionAttempts();
    console.log(`[连接尝试] 第${wsStore.connectionAttempts}次尝试建立MQTT连接`);
    
    // MQTT服务器地址 - 使用WebSocket作为传输层
    const host = "************";
    const port = "8083";
    const clientId = `alert-ui-${Date.now()}`;
    const protocol = 'ws'; // 使用WebSocket协议
    const path = '/mqtt';  // MQTT WebSocket路径
    
    // 构建WebSocket URL
    const wsUrl = `${protocol}://${host}:${port}${path}`;
    console.log(`[MQTT] 通过WebSocket连接: ${wsUrl}`);
    
    try {
      // 创建MQTT客户端，使用WebSocket作为传输层
      mqttClient = mqtt.connect(wsUrl, {
        clientId,
        clean: true,
        connectTimeout: 30000, // 30秒
        reconnectPeriod: 5000, // 5秒尝试重连
        // 指定WebSocket作为传输层
        protocol: 'ws',
        protocolVersion: 4, // MQTT v3.1.1
      });
      
      const connectionStartTime = Date.now();
      
      // 连接成功回调
      mqttClient.on('connect', () => {
        wsStore.setIsConnected(true);
        console.log(`[MQTT] 连接已建立，耗时: ${Date.now() - connectionStartTime}ms`);
        
        // 订阅设备主题
        const topics = [
          '/HN3S1/JH001',
          '/HN3S1/JH002',
          '/HN3S1/JH006',
          '/HN3S1/JH007',
          '/HN3S1/JH008',
          '/HN3S1/JH009',
          '/HN3S1/HN15V3',
          '/HN3S1/HN15V4',
          '/HN3S1/HN15V5',
          '/HN3S1/HN15V6',
          '/HN3S1/HN15V67',
          '/HN3S1/HN15V7',
          '/HN3S1/HN15V8',
          '/HN3S1/HN15V9',
          '/HN3S1/HN15V10',
          '/HN3S1/HN15V25',
          '/HN3S1/HN15V26'
        ];
        
        topics.forEach(topic => {
          mqttClient.subscribe(topic, (err) => {
            if (err) {
              console.error(`[MQTT订阅错误] 订阅主题${topic}失败: ${err.message}`);
            } else {
              console.log(`[MQTT订阅] 已订阅主题: ${topic}`);
            }
          });
        });
        
        // 发送心跳消息
        startHeartbeat();
      });

      // 消息接收回调
      mqttClient.on('message', (topic, payload) => {
        // 更新心跳响应时间
        lastHeartbeatResponse = Date.now();
        
        try {
          // 尝试解析JSON数据
          const payloadStr = payload.toString();
          const data = JSON.parse(payloadStr);
          
          // 提取设备信息用于日志记录
          const deviceId = data.Wellname || topic.split('/').pop();
          
          // 不再在前端进行警报判断，只接收后端通过WebSocket发送的警报
          // 前端仅记录收到的设备状态信息，不做判断
          const status = data.Status !== undefined ? data.Status : data.status;
          if (status === 0) {
          } else if (status !== undefined) {
            // 异常状态仍然记录，便于调试
            console.log(`[设备状态] 设备 ${deviceId} 状态值：${status}`);
          }
          
        } catch (error) {
          console.error(`[解析错误] 解析MQTT消息失败: ${error.message}`);
        }
      });

      // 断开连接回调
      mqttClient.on('close', () => {
        // 清除定时器
        clearTimers();
        
        wsStore.setIsConnected(false);
        console.log(`[MQTT] 连接已关闭`);
        
        // 设置重连定时器
        reconnectTimeout = setTimeout(() => {
          console.log(`[MQTT] 尝试重新连接`);
          setupMQTTClient();
        }, 5000);
      });

      // 错误回调
      mqttClient.on('error', (error) => {
        console.error(`[MQTT错误] 连接出错: ${error.message || '未知错误'}`);
        wsStore.setIsConnected(false);
      });
    } catch (err) {
      wsStore.setIsConnected(false);
      console.error(`[MQTT错误] 创建连接失败: ${err.message}`);
      
      // 如果创建失败，尝试延迟重连
      console.log(`[MQTT] 创建失败，将在10秒后重试`);
      setTimeout(() => {
        setupMQTTClient();
      }, 10000);
    }
  }, 1000); // 等待1秒再创建新连接
}

// 清空所有警报
function clearAlerts() {
  console.log(`[警报管理] 清空所有警报`);
  wsStore.clearAlerts();
}

// 组件挂载时建立连接
onMounted(() => {
  // 加载设置
  console.log(`[组件生命周期] AlertDialog组件已挂载`);
  loadSettings();
  
  // 建立MQTT连接
  console.log(`[初始化] 警报系统初始化，将在2秒后建立连接`);
  console.log(`[全局警报] 启用全局警报接收，将显示所有井组警报`);
  setTimeout(setupMQTTClient, 2000);
  
  // 建立WebSocket连接接收后端警报
  setupWebSocket();
});

// 组件卸载前关闭连接
onBeforeUnmount(() => {
  console.log(`[组件生命周期] AlertDialog组件即将卸载，清理资源`);
  
  // 清理所有计时器
  clearTimers();
  
  // 关闭MQTT连接
  if (mqttClient) {
    try {
      mqttClient.end();
      mqttClient = null;
      console.log(`[MQTT] 连接已关闭`);
    } catch (err) {
      console.error(`[MQTT错误] 关闭连接失败: ${err.message}`);
    }
  }
  
  // 关闭WebSocket连接
  closeWebSocket();
});

// WebSocket变量
let ws = null;
let wsReconnectTimeout = null;

// 建立WebSocket连接以接收后端警报
function setupWebSocket() {
  // 如果已有连接，先关闭
  if (ws) {
    try {
      ws.close();
    } catch (err) {
      console.error(`[WebSocket错误] 关闭连接失败: ${err.message}`);
    }
  }
  
  // 构建WebSocket URL - 使用与当前页面相同的主机
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
  // 使用确切的后端地址和端口
  const host = window.location.hostname; // 使用当前页面的主机名
  const port = "8000"; // 后端端口
  const wsUrl = `${protocol}//${host}:${port}/alerts`;
  
  console.log(`[WebSocket] 尝试建立连接: ${wsUrl}`);
  console.log(`[WebSocket] 当前页面地址: ${window.location.href}`);
  
  try {
    ws = new WebSocket(wsUrl);
    console.log('[WebSocket] 连接对象已创建，等待连接建立...');
    
    // 连接建立时的回调
    ws.onopen = () => {
      console.log('[WebSocket] 连接已成功建立');
      wsStore.setIsConnected(true);
      
      // 清除重连定时器
      if (wsReconnectTimeout) {
        clearTimeout(wsReconnectTimeout);
        wsReconnectTimeout = null;
      }
      
      // 发送心跳消息以验证连接
      ws.send(JSON.stringify({
        type: "heartbeat",
        timestamp: Date.now()
      }));
      console.log('[WebSocket] 发送心跳消息以验证连接');
    };
    
    // 接收消息时的回调
    ws.onmessage = (event) => {
      try {
        // 记录原始消息
        console.log(`[WebSocket] 收到原始消息:`, event.data);
        
        const data = JSON.parse(event.data);
        // 详细记录解析后的消息
        console.log(`[WebSocket] 解析后消息:`, data);
        console.log(`[WebSocket] 消息类型: ${data.type || 'unknown'}, 标题: ${data.title || 'n/a'}`);
        
        // 处理心跳响应
        if (data.type === 'pong') {
          lastHeartbeatResponse = Date.now();
          console.log(`[WebSocket] 收到心跳响应`);
          return;
        }
        
        // 处理连接状态消息
        if (data.type === 'connection_status') {
          console.log(`[WebSocket] 收到连接状态消息: ${data.status}`);
          return;
        }
        
        // 如果收到警报，直接添加到警报列表 - 处理所有警报类型
        if (data.type === 'connection_alert' || 
            data.type === 'device_alert' || 
            data.type === 'metric_alert' || 
            data.type === 'alert' ||
            data.type === 'camera' ||
            data.type === 'merged_alert') {
          console.log(`[警报接收] 收到后端警报: ${data.title}, 类型: ${data.type}, alertType: ${data.alertType || data.type}`);
          
          // 构建警报对象，确保包含所有需要的字段
          const alertObject = {
            title: data.title || `设备警报`,
            content: data.content || `未知警报内容`,
            type: data.type, 
            alertType: data.alertType,
            timestamp: data.timestamp || new Date().toISOString(),
            id: data.id || `ws-alert-${Date.now()}`,
            deviceId: data.device?.id || data.deviceId || 'unknown',
            persistent: true,  // 从后端收到的警报设为持久，需手动关闭
            device: data.device,
            subAlerts: data.subAlerts, // 添加子警报字段
            
            // 保留相关元数据
            inspection: data.inspection,
            realtime: data.realtime
          };
          
          // 记录警报类型
          console.log(`[警报处理] 准备添加警报对象:`, alertObject);
          addAlert(alertObject);
          
        } else {
          console.log(`[WebSocket] 收到非警报消息，类型: ${data.type || 'unknown'}`);
        }
      } catch (error) {
        console.error(`[WebSocket错误] 解析消息失败: ${error.message}`);
        console.error(`[WebSocket错误] 原始消息: ${event.data}`);
      }
    };
    
    // 连接关闭时的回调
    ws.onclose = (event) => {
      console.log(`[WebSocket] 连接已关闭: code=${event.code}, reason=${event.reason}`);
      wsStore.setIsConnected(false);
      
      // 自动重连
      wsReconnectTimeout = setTimeout(() => {
        console.log('[WebSocket] 尝试重新连接');
        setupWebSocket();
      }, 5000);
    };
    
    // 连接错误时的回调
    ws.onerror = (error) => {
      console.error(`[WebSocket错误] ${error.message || '未知错误'}`);
      console.error(`[WebSocket错误] 详细错误信息:`, error);
      console.error(`[WebSocket连接] 尝试连接的URL: ws://localhost:8000/alerts`);
      wsStore.setIsConnected(false);
    };
    
  } catch (error) {
    console.error(`[WebSocket错误] 创建连接失败: ${error.message}`);
    console.error(`[WebSocket创建] 详细错误:`, error);
    wsStore.setIsConnected(false);
    
    // 自动重连
    wsReconnectTimeout = setTimeout(() => {
      console.log('[WebSocket] 尝试重新连接');
      setupWebSocket();
    }, 5000);
  }
}

// 关闭WebSocket连接
function closeWebSocket() {
  if (ws) {
    try {
      ws.close();
      ws = null;
      console.log('[WebSocket] 连接已关闭');
    } catch (error) {
      console.error(`[WebSocket错误] 关闭连接失败: ${error.message}`);
    }
  }
  
  // 清除重连定时器
  if (wsReconnectTimeout) {
    clearTimeout(wsReconnectTimeout);
    wsReconnectTimeout = null;
  }
}
</script>

<style scoped>
/* 添加全局样式清除，确保没有额外元素显示 */
.alert-dialog-container * {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.alert-dialog-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9998;
  display: flex;
  flex-direction: column;
  max-width: 320px;
}

/* 移除可能造成白点的样式 */
.alert-dialog-container::after,
.alert-dialog-container::before {
  display: none;
  content: none;
}

/* 主要折叠面板样式 */
.alert-summary {
  position: relative;
  cursor: pointer;
  display: flex;
  justify-content: flex-end;
}

.expand-btn {
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  font-weight: bold;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.alert-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #dc3545;
  color: white;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  z-index: 9999;
}

/* 展开后的容器样式 */
.expanded-container {
  background-color: rgba(245, 245, 245, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  max-height: calc(80vh - 40px); /* 减去底部的空间 */
  width: 320px;
}

.alert-header-bar {
  background-color: #343a40;
  color: white;
  padding: 10px 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.collapse-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 0.9em;
  padding: 3px 8px;
  border-radius: 3px;
}

.collapse-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 15px;
  overflow-y: auto;
  max-height: calc(70vh - 80px); /* 限制最大高度，确保有足够空间给控制面板 */
  scrollbar-width: thin; /* Firefox */
}

/* 定制滚动条样式 */
.alerts-list::-webkit-scrollbar {
  width: 6px;
}

.alerts-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.alerts-list::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.alerts-list::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.control-panel {
  display: flex;
  justify-content: space-between;
  padding: 10px 15px;
  border-top: 1px solid #ddd;
  gap: 10px;
}

.filter-options {
  display: flex;
  gap: 8px;
  align-items: center;
  padding: 5px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
}

.filter-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 0.9em;
  color: #555;
  padding: 3px 10px;
  border-radius: 3px;
  transition: background-color 0.2s;
  user-select: none;
}

.filter-label:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.filter-label input {
  margin-right: 6px;
}

.filter-label.realtime {
  border-left: 2px solid #28a745;
}

.filter-label.inspection {
  border-left: 2px solid #007bff;
}

.alert {
  padding: 15px;
  border-radius: 5px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  position: relative;
  background-color: white;
  border-left: 4px solid transparent;
}

.alert-warning {
  border-left-color: #ffc107;
  color: #856404;
}

.alert-danger {
  border-left-color: #dc3545;
  color: #721c24;
}

.alert-info {
  border-left-color: #17a2b8;
  color: #0c5460;
}

.alert-success {
  border-left-color: #28a745;
  color: #155724;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.alert-close {
  cursor: pointer;
  font-size: 1.2em;
  line-height: 1;
  opacity: 0.7;
  padding: 0 5px;
}

.alert-close:hover {
  opacity: 1;
}

.alert-content {
  margin-bottom: 10px;
  white-space: pre-line;
  font-size: 0.95em;
}

.alert-timestamp {
  font-size: 0.75em;
  text-align: right;
  opacity: 0.7;
}

.alert-device {
  font-size: 1em;
  text-align: right;
  opacity: 1;
}

.clear-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
  font-weight: bold;
  color: white;
  background-color: #dc3545;
  transition: background-color 0.2s, transform 0.1s;
  min-width: 100px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.clear-btn:hover {
  background-color: #c82333;
}

.clear-btn:active {
  transform: scale(0.98);
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

@keyframes slideIn {
  from {
    transform: translateY(50%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 分页控制样式 */
.pagination-controls {
  display: flex;
  justify-content: center;
  padding: 10px;
}

.pagination-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1em;
  font-weight: bold;
  color: white;
  background-color: #007bff;
  transition: background-color 0.2s;
  margin: 0 5px;
}

.pagination-btn:hover {
  background-color: #0056b3;
}

.pagination-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.pagination-info {
  padding: 0 10px;
  font-size: 0.9em;
  color: #6c757d;
}

/* 子警报容器样式 */
.sub-alerts-container {
  margin-top: 8px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
  padding: 5px;
}

.sub-alerts-header {
  font-size: 0.9em;
  font-weight: bold;
  color: #444;
  margin-bottom: 5px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 3px;
}

.sub-alert {
  margin-bottom: 5px;
  padding: 4px 8px;
  border-radius: 3px;
  font-size: 0.9em;
  border-left: 3px solid #ccc;
}

.sub-alert:last-child {
  margin-bottom: 0;
}

.sub-alert-title {
  font-weight: bold;
  margin-bottom: 2px;
}

.sub-alert-content {
  font-size: 0.85em;
}

.sub-alert-warning {
  border-left-color: #ff9800;
  background-color: rgba(255, 152, 0, 0.05);
}

.sub-alert-danger {
  border-left-color: #f44336;
  background-color: rgba(244, 67, 54, 0.05);
}

.sub-alert-info {
  border-left-color: #2196f3;
  background-color: rgba(33, 150, 243, 0.05);
}

.alert-meta-info {
  display: flex;
  flex-direction: column;
  font-size: 0.75em;
  text-align: right;
  opacity: 0.7;
  margin-top: 5px;
  gap: 3px;
}

.alert-check-date {
  font-weight: bold;
  color: #28a745;
  font-size: 0.9em;
  border-left: 3px solid #28a745;
  padding-left: 5px;
  margin-bottom: 4px;
}

.alert-inspection-time {
  color: #007bff;
}

.time-highlight {
  color: #28a745;
  font-weight: bold;
}

.inspection-time-highlight {
  color: #007bff;
  font-weight: bold;
}

.inspection-tag {
  color: #007bff;
  font-weight: bold;
  background-color: rgba(0, 123, 255, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  margin-right: 4px;
}

.received-tag {
  color: #28a745;
  font-weight: bold;
  background-color: rgba(40, 167, 69, 0.1);
  padding: 2px 4px;
  border-radius: 3px;
  margin-right: 4px;
}

/* 为警报添加区分样式 */
.alert-meta-info .alert-source {
  font-size: 0.8em;
  text-align: right;
  margin-top: 3px;
}

.alert-source-realtime {
  color: #28a745;
  font-weight: bold;
  background-color: rgba(40, 167, 69, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  margin-left: 4px;
}

.alert-source-inspection {
  color: #007bff;
  font-weight: bold;
  background-color: rgba(0, 123, 255, 0.1);
  padding: 2px 6px;
  border-radius: 3px;
  margin-left: 4px;
}

.alert-realtime-time {
  color: #28a745;
}

.realtime-time-highlight {
  color: #28a745;
  font-weight: bold;
}

.alert-update-info {
  font-size: 0.75em;
  opacity: 0.8;
  margin-top: 3px;
}

.update-count {
  font-weight: bold;
  color: #007bff;
}

.update-time {
  color: #6c757d;
}
</style> 