```mermaid
flowchart TD
    A[开始] --> B{是否打开聊天}
    B -->|是| C[禁用键盘控制]
    B -->|否| D[关闭聊天]
    C --> E[等待用户输入]
    
    E --> F{用户发送消息}
    F -->|是| G{是否正在生成回复}
    G -->|是| E
    G -->|否| H[添加用户消息到对话]
    
    H --> I[添加AI思考中状态消息]
    I --> J[设置流式状态为true]
    J --> K[创建AbortController]
    K --> L[构建消息历史]
    
    L --> M[准备请求参数]
    M --> N[调用聊天API]
    N --> O{响应是否正常}
    
    O -->|否| P[处理错误]
    O -->|是| Q[处理流式响应]
    
    Q --> R[读取数据块]
    R --> S{数据读取完成}
    S -->|是| T[完成响应]
    S -->|否| U[解码数据块]
    
    U --> V[处理缓冲区]
    V --> W{是否找到完整数据}
    W -->|否| R
    W -->|是| X[提取数据行]
    
    X --> Y{数据是否为DONE}
    Y -->|是| T
    Y -->|否| Z[解析JSON]
    
    Z --> AA[更新消息内容]
    AA --> AB{缓冲区还有数据}
    AB -->|是| V
    AB -->|否| R
    
    T --> AC[重置流式状态]
    P --> AD{是否用户中止}
    AD -->|是| AE[显示已停止生成]
    AD -->|否| AF[显示错误消息]
    
    AC --> AG[滚动到底部]
    AE --> AG
    AF --> AG
    
    AG --> E
    
    %% 停止消息生成功能
    AH[用户点击停止按钮] --> AI{是否正在生成}
    AI -->|否| AJ[不执行操作]
    AI -->|是| AK[中止请求]
    
    %% 重置聊天功能
    AL[重置聊天] --> AM[重置前端状态]
    AM --> AN[重置后端状态]
    
    %% Langgraph多智能体处理流程
    subgraph 后端Langgraph处理
        BA[接收用户消息] --> BB[问题分类智能体]
        BB --> BC{需要查询数据?}
        BC -->|是| BD[调用MQTT工具]
        BC -->|否| BG
        BD --> BE[获取设备数据]
        BE --> BF[返回数据给智能体]
        BF --> BG[生成回复内容]
        
        BC -->|是| BH{需要知识库?}
        BH -->|是| BI[调用知识库工具]
        BH -->|否| BG
        BI --> BJ[查询相关文档]
        BJ --> BK[返回知识给智能体]
        BK --> BG
    end
    
    N -.-> BA
    BG -.-> Q
``` 