import random
import time
import json
from datetime import datetime
from loguru import logger
from typing import Dict, List, Optional
import paho.mqtt.client as mqtt
import threading
import asyncio
from app.websockets.manager import manager

class AlertGenerator:
    """告警和控制消息生成器"""
    
    def __init__(self):
        # 存储MQTT设备数据
        self.mqtt_devices: Dict[str, dict] = {}
        self.device_thresholds = {
            # 原来的阈值设置:
            # "温度": {"min": 20, "max": 80},   # 温度阈值(℃)
            # "压力": {"min": 0.1, "max": 1.0}, # 压力阈值(MPa)
            # "电流": {"min": 0.5, "max": 2.0}, # 电流阈值(A)
            # "振动": {"min": 0, "max": 10}     # 振动阈值(mm)
            
            # 新的阈值设置，范围更宽，几乎不会触发警报
            "温度": {"min": -50, "max": 500},   # 温度阈值(℃)，几乎不可能达到的极端范围
            "压力": {"min": -10, "max": 100},   # 压力阈值(MPa)，设置为极宽范围
            "电流": {"min": -100, "max": 1000}, # 电流阈值(A)，设置为极宽范围
            "振动": {"min": -100, "max": 1000}  # 振动阈值(mm)，设置为极宽范围
        }
        
        # 全局监控标志和设备ID列表
        self.global_monitoring = True
        
        self.all_device_ids = [
            "HN15V1",  
            "HN15V2",  
            "HN15V3",  
            "HN15V4",  
            "HN15V5",  
            "HN15V6",  
            "HN15V7",  
            "HN15V8",  
            "HN15V9",  
            "HN15V10",  
            "HN15V24",  
            "HN15V25",  
            "HN15V26",  
            "HN15V67",  
            "JH001",  
            "JH002",  
            "JH003",  
            "JH004",  
            "JH005",  
            "JH006",  
            "JH007",  
            "JH008",  
            "JH009"  
        ]
        
        # MQTT客户端
        self.mqtt_client = None
        self.mqtt_connected = False
        
        # 添加警报缓存，用于跟踪每个设备的警报状态
        self.alert_cache = {}
    
    def connect_to_mqtt(self, host="************", port=8083, path="/mqtt", use_websocket=True):
        """
        连接到MQTT服务器，接收实时设备数据
        
        Args:
            host: MQTT服务器地址
            port: MQTT服务器端口
            path: WebSocket路径
            use_websocket: 是否使用WebSocket连接
        """
        # 创建MQTT客户端
        client_id = f"alert-monitor-{int(time.time())}"
        
        if use_websocket:
            self.mqtt_client = mqtt.Client(client_id, transport="websockets")
            self.mqtt_client.ws_set_options(path=path)
        else:
            self.mqtt_client = mqtt.Client(client_id)
        
        # 设置回调函数
        self.mqtt_client.on_connect = self._on_connect
        self.mqtt_client.on_message = self._on_message
        self.mqtt_client.on_disconnect = self._on_disconnect
        
        # 连接服务器
        try:
            self.mqtt_client.connect(host, port, 60)
            
            # 在单独的线程中启动MQTT循环
            mqtt_thread = threading.Thread(target=self._start_mqtt_loop)
            mqtt_thread.daemon = True
            mqtt_thread.start()
            
            return True
        except Exception as e:
            return False
    
    def _start_mqtt_loop(self):
        """在单独的线程中启动MQTT循环"""
        try:
            self.mqtt_client.loop_forever()
        except Exception as e:
            pass
    
    def _on_connect(self, client, userdata, flags, rc):
        """MQTT连接成功回调"""
        if rc == 0:
            self.mqtt_connected = True
            
            # 订阅所有主题
            topics = [
                "/HN3S1/JH001",  
                "/HN3S1/JH002",  
                "/HN3S1/JH003",  
                "/HN3S1/JH004",  
                "/HN3S1/JH005",  
                "/HN3S1/JH006",  
                "/HN3S1/JH007",  
                "/HN3S1/JH008",  
                "/HN3S1/JH009",  
                "/HN3S1/HN15V1",  
                "/HN3S1/HN15V2",  
                "/HN3S1/HN15V3",  
                "/HN3S1/HN15V4",  
                "/HN3S1/HN15V5",  
                "/HN3S1/HN15V6",  
                "/HN3S1/HN15V7",  
                "/HN3S1/HN15V8",  
                "/HN3S1/HN15V9",  
                "/HN3S1/HN15V10",  
                "/HN3S1/HN15V24",  
                "/HN3S1/HN15V25",  
                "/HN3S1/HN15V26",  
                "/HN3S1/HN15V67"
            ]
            
            for topic in topics:
                client.subscribe(topic)
        else:
            self.mqtt_connected = False
    
    def _on_message(self, client, userdata, msg):
        """MQTT消息接收回调"""
        topic = msg.topic
        current_time = datetime.now()
        
        try:
            # 解析JSON数据
            payload = msg.payload.decode('utf-8')
            data = json.loads(payload)
            
            # 从主题中提取设备ID - 处理/HN3S1/JH001格式
            device_id = topic.split('/')[-1]  # 获取最后一段作为设备ID
            
            # 在MQTT消息处理线程中不能直接执行异步操作，需要将数据处理移至主线程
            # 改用同步方式更新设备数据，实时警报稍后通过定时任务广播
            self.update_device_data_sync(device_id, data)
            
        except json.JSONDecodeError as e:
            pass
        except Exception as e:
            pass
    
    def _on_disconnect(self, client, userdata, rc):
        """MQTT断开连接回调"""
        self.mqtt_connected = False
        if rc != 0:
            try:
                client.reconnect()
            except Exception as e:
                pass
    
    # 添加同步数据处理方法，避免在MQTT线程中执行异步操作
    def update_device_data_sync(self, device_id: str, data: dict) -> None:
        """
        同步更新设备数据（在MQTT回调线程中调用）
        
        Args:
            device_id (str): 设备ID
            data (dict): 设备数据
        """
        
        # 如果设备ID格式不正确，尝试格式化（例如H001转为JH001）
        if device_id.startswith('H') and not device_id.startswith('HN'):
            device_id = 'J' + device_id
        
        # 更新设备数据，确保状态字段正确映射
        processed_data = {**data}
        
        # 添加状态字段（确保大小写一致性）
        if "Status" in data:
            processed_data["status"] = data["Status"]
        
        # 添加数据接收时间
        received_time = datetime.now().isoformat()
        
        # 更新设备数据
        self.mqtt_devices[device_id] = {
            **processed_data,
            "last_update": received_time
        }
        
        # 检查是否需要生成警报（保存到临时队列）
        alerts = self.check_device_alerts(device_id)
        if alerts:
            # 获取当前时间作为警报接收时间
            alert_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            # 添加标记表明这是实时生成的警报，而非定时巡检
            for alert in alerts:
                # 确保is_realtime标记为True
                alert['is_realtime'] = True
                
                # 在内容前添加接收警报标记（保持与巡检警报格式一致）
                if not alert['content'].startswith("接收警报"):
                    alert['content'] = f"接收警报 {alert['content']}"
                
                # 添加接收警报元数据
                alert['realtime'] = {
                    'is_realtime': True,
                    'alert_time': alert_time
                }
                
                # 确保ID的唯一性，避免与巡检警报重复
                if not alert['id'].startswith("realtime-"):
                    alert['id'] = f"realtime-{alert['id']}"
                
                # 确保接收时间正确记录
                alert['timestamp'] = datetime.now().isoformat()
                
            # 将警报添加到设备的实时警报队列，稍后由主线程广播
            if not hasattr(self, 'realtime_alerts'):
                self.realtime_alerts = []
            
            self.realtime_alerts.extend(alerts)

    # 保留原始更新方法，用于全局监控和在主事件循环中调用
    def update_device_data(self, device_id: str, data: dict) -> None:
        """
        更新设备数据并检查是否需要生成警报（主线程中调用）
        
        Args:
            device_id (str): 设备ID
            data (dict): 设备数据
        """
        
        # 如果设备ID格式不正确，尝试格式化（例如H001转为JH001）
        if device_id.startswith('H') and not device_id.startswith('HN'):
            device_id = 'J' + device_id
        
        # 更新设备数据，确保状态字段正确映射
        processed_data = {**data}
        
        # 添加状态字段（确保大小写一致性）
        if "Status" in data:
            processed_data["status"] = data["Status"]
        
        # 更新设备数据
        self.mqtt_devices[device_id] = {
            **processed_data,
            "last_update": datetime.now().isoformat()
        }
        
        # 检查是否需要生成警报
        alerts = self.check_device_alerts(device_id)
        if alerts:
            # 添加标记表明这是实时生成的警报，而非定时巡检
            for alert in alerts:
                alert['is_realtime'] = True
                
            # 创建异步任务来广播警报
            async def broadcast_alerts():
                try:
                    await manager.broadcast_alerts(alerts)
                except Exception as e:
                    pass
            
            # 使用asyncio创建任务并执行
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # 如果当前有事件循环在运行，创建任务
                loop.create_task(broadcast_alerts())
        
        return alerts
    
    def check_device_alerts(self, device_id: str) -> List[dict]:
        """
        检查设备数据是否需要生成警报
        
        Args:
            device_id (str): 设备ID
            
        Returns:
            List[dict]: 需要生成的警报列表
        """
        alerts = []
        device_data = self.mqtt_devices.get(device_id)
        
        if not device_data:
            return alerts
        
        # 检查设备状态 - 同时检查Status和status字段
        status = device_data.get("status")
        if status is None:
            status = device_data.get("Status")
        
        # 修改为更严格的条件，只有在状态值大于1000时才触发警报
        # 这个值设置得非常高，实际上几乎不可能达到，因此不会触发警报
        if status is not None and status > 1000:
            alerts.append(self.generate_device_alert(device_id, device_data))
        
        # 检查直接在数据中的各项指标
        for metric, thresholds in self.device_thresholds.items():
            value = device_data.get(metric)
            if value is not None:
                if value < thresholds["min"] or value > thresholds["max"]:
                    alerts.append(self.generate_metric_alert(device_id, metric, value, thresholds))
                    
        # 检查Values数组中的指标
        if "Values" in device_data and isinstance(device_data["Values"], list):
            # 指标映射关系
            metric_map = {
                "TWT": "温度",
                "WIP": "压力",
                "CPV": "压力",
                "ADL": "电流",
                "BDL": "电流",
                "CDL": "电流"
            }
            
            for item in device_data["Values"]:
                if isinstance(item, dict) and "id" in item and "Value" in item:
                    item_id = item["id"]
                    item_value = item["Value"]
                    
                    # 查找对应的指标类型
                    metric_type = metric_map.get(item_id)
                    if metric_type and metric_type in self.device_thresholds:
                        thresholds = self.device_thresholds[metric_type]
                        
                        if item_value < thresholds["min"] or item_value > thresholds["max"]:
                            alerts.append(self.generate_metric_alert(device_id, f"{item_id}({metric_type})", item_value, thresholds))
        
        return alerts
    
    def generate_metric_alert(self, device_id: str, metric: str, value: float, thresholds: dict) -> dict:
        """
        生成指标异常警报
        
        Args:
            device_id (str): 设备ID
            metric (str): 指标名称
            value (float): 当前值
            thresholds (dict): 阈值配置
            
        Returns:
            dict: 警报消息
        """
        # 从指标名中提取基础指标类型
        base_metric = metric
        if "(" in metric and ")" in metric:
            # 处理形如 "ADL(电流)" 的指标名
            base_metric = metric.split("(")[1].replace(")", "")
            
        unit = {
            "温度": "℃",
            "压力": "MPa",
            "电流": "A",
            "振动": "mm"
        }.get(base_metric, "")
        
        status = "过高" if value > thresholds["max"] else "过低"
        
        # 获取设备数据中的时间戳，如果存在的话
        check_date = ""
        device_data = self.mqtt_devices.get(device_id, {})
        if "Check_date" in device_data:
            check_date = f"，数据时间: {device_data['Check_date']}"
        
        content = f"{device_id}设备{metric}{status}，当前值{value:.1f}{unit}，阈值范围[{thresholds['min']}-{thresholds['max']}]{unit}{check_date}"
        
        # 创建唯一的警报键，用于缓存识别
        alert_key = f"metric-{device_id}-{metric}"
        timestamp = datetime.now().isoformat()
        
        # 创建警报对象
        alert = {
            "type": "metric_alert",
            "title": f"{device_id}设备{metric}异常",
            "content": content,
            "alertType": "warning",
            "timestamp": timestamp,
            "id": f"alert-{alert_key}-{int(time.time() * 1000)}",
            "device": {
                "id": device_id,
                "metric": metric,
                "value": value,
                "thresholds": thresholds,
                "check_date": device_data.get("Check_date")  # 添加数据时间戳到设备信息
            }
        }
        
        return alert
    
    def get_device_status(self) -> dict:
        """
        获取所有设备状态
        
        Returns:
            dict: 设备状态信息
        """
        status = {
            "total_devices": len(self.mqtt_devices),
            "devices": []
        }
        
        for device_id, data in self.mqtt_devices.items():
            device_status = {
                "id": device_id,
                "status": data.get("status", -1),
                "last_update": data.get("last_update"),
                "metrics": {}
            }
            
            # 添加各项指标
            for metric in self.device_thresholds.keys():
                if metric in data:
                    device_status["metrics"][metric] = data[metric]
            
            status["devices"].append(device_status)
        
        return status
    
    def generate_device_alert(self, device_id: str, device_data: dict) -> dict:
        """
        生成设备告警消息
        
        Args:
            device_id (str): 设备ID
            device_data (dict): 设备数据
            
        Returns:
            dict: 设备告警消息字典
        """
        values = []
        for metric in self.device_thresholds.keys():
            if metric in device_data:
                values.append({
                    "id": metric,
                    "Value": device_data[metric]
                })
        
        # 获取设备状态，同时检查Status和status字段
        device_status = device_data.get("Status")
        if device_status is None:
            device_status = device_data.get("status", 0)
        
        # 确保状态不为None
        if device_status is None:
            device_status = 0
            
        # 获取设备数据中的时间戳，如果存在的话
        check_date = ""
        if "Check_date" in device_data:
            check_date = f"，数据时间: {device_data['Check_date']}"
        
        # 创建唯一的警报键，用于缓存识别
        alert_key = f"device-{device_id}"
        timestamp = datetime.now().isoformat()
            
        return {
            "type": "device_alert",
            "title": f"{device_id}设备状态告警",
            "content": f"{device_id}设备当前状态异常，状态值: {device_status}{check_date}",
            "alertType": "warning",
            "timestamp": timestamp,
            "id": f"alert-{alert_key}-{int(time.time() * 1000)}",
            "device": {
                "id": device_id,
                "status": device_status,  # 确保设备状态字段正确传递
                "values": values,
                "check_date": device_data.get("Check_date")  # 添加数据时间戳到设备信息
            }
        }
    
    async def monitor_all_devices(self) -> List[dict]:
        """
        全局监控所有设备并生成警报
        独立于前端MQTT订阅，确保所有设备都被监控
        
        Returns:
            List[dict]: 需要发送的警报列表
        """
        new_alerts = []
        updated_devices = set()  # 用于跟踪本次巡检中哪些设备已更新警报
        
        # 当前巡检时间戳
        inspection_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 记录异常设备，用于汇总报告
        abnormal_devices = []
        
        for device_id in self.all_device_ids:
            try:
                # 检查设备是否有数据
                if device_id in self.mqtt_devices:
                    # 获取设备数据
                    device_data = self.mqtt_devices[device_id]
                    
                    # 检查设备状态并生成警报
                    device_alerts = self.check_device_alerts(device_id)
                    
                    if device_alerts:
                        for alert in device_alerts:
                            # 提取设备ID和警报类型，创建唯一键
                            device_id = alert['device']['id']
                            alert_type = alert['type']
                            
                            if alert_type == "metric_alert":
                                metric = alert['device']['metric']
                                value = alert['device']['value']
                                alert_key = f"{alert_type}-{device_id}-{metric}"
                                
                                # 检查是否有此警报的缓存
                                if alert_key in self.alert_cache:
                                    cached_alert = self.alert_cache[alert_key]
                                    
                                    # 只有当值发生变化时才发送新警报
                                    if abs(cached_alert['device']['value'] - value) > 0.01:  # 允许微小浮动
                                        self.alert_cache[alert_key] = alert
                                        new_alerts.append(alert)
                                        updated_devices.add(device_id)
                                else:
                                    # 首次发现的警报，直接添加到缓存和发送列表
                                    self.alert_cache[alert_key] = alert
                                    new_alerts.append(alert)
                                    updated_devices.add(device_id)
                                    
                            elif alert_type == "device_alert":
                                status = alert['device']['status']
                                alert_key = f"{alert_type}-{device_id}"
                                
                                # 检查是否有此警报的缓存
                                if alert_key in self.alert_cache:
                                    cached_alert = self.alert_cache[alert_key]
                                    
                                    # 只有当状态发生变化时才发送新警报
                                    if cached_alert['device']['status'] != status:
                                        self.alert_cache[alert_key] = alert
                                        new_alerts.append(alert)
                                        updated_devices.add(device_id)
                                else:
                                    # 首次发现的警报，直接添加到缓存和发送列表
                                    self.alert_cache[alert_key] = alert
                                    new_alerts.append(alert)
                                    updated_devices.add(device_id)
                        
                        if device_id not in updated_devices:
                            abnormal_devices.append(device_id)
                # 原来的离线设备检查代码:
                # else:
                #     # 如果没有收到设备数据且全局监控启用，生成连接状态警报
                #     if self.global_monitoring:
                #         connection_alert = self.generate_connection_alert(device_id)
                #         
                #         # 检查是否已有此设备的连接警报
                #         alert_key = f"connection_alert-{device_id}"
                #         
                #         if alert_key not in self.alert_cache:
                #             # 首次发现设备离线，添加警报
                #             self.alert_cache[alert_key] = connection_alert
                #             new_alerts.append(connection_alert)
                #             abnormal_devices.append(device_id)
                        
            except Exception as e:
                logger.error(f"监控设备 {device_id} 时出错: {str(e)}")
        
        # 添加时间戳和消息信息
        for alert in new_alerts:
            # 更新ID确保唯一性
            alert['id'] = f"alert-{alert['id']}-{int(time.time() * 1000)}"
            
            # 更新消息内容，添加时间戳
            if not alert['content'].startswith(f"[{inspection_time}]"):
                alert['content'] = f"[{inspection_time}] {alert['content']}"
                
            # 更新时间戳
            alert['timestamp'] = datetime.now().isoformat()
        
        return new_alerts

    def generate_connection_alert(self, device_id: str) -> dict:
        """
        根据设备在线/离线状态生成连接警报
        
        Args:
            device_id: 设备ID
            
        Returns:
            生成的连接警报字典
        """
        # 返回一个不会触发警报UI的空记录
        now = datetime.datetime.now()
        device_name = self.device_info.get(device_id, {}).get("name", device_id)
        alert_id = f"{device_id}-{now.timestamp()}"
        
        logger.info(f"检测到设备 {device_name} 离线，但不生成警报")
        
        # 返回一个空警报（不会在前端显示）
        return {
            "id": alert_id,
            "type": "system_message",  # 更改类型，前端不会显示此类型
            "device_id": device_id,
            "timestamp": now.timestamp(),
            "processed": True  # 标记为已处理，不会显示
        }

    # 更新处理实时警报的方法，移除区分实时和巡检警报的标记
    async def process_realtime_alerts(self):
        """
        处理并广播MQTT回调线程中收集的实时警报
        在主事件循环的定时任务中调用此方法
        """
        # 检查是否有实时警报需要处理
        if hasattr(self, 'realtime_alerts') and self.realtime_alerts:
            # 清空警报列表但不广播任何警报
            self.realtime_alerts = []
            
            # 禁用原来的警报处理逻辑
            """
            alerts = self.realtime_alerts.copy()
            self.realtime_alerts = []
            
            # 更新警报与缓存，只发送状态变化的警报
            new_alerts = []
            for alert in alerts:
                device_id = alert['device']['id']
                alert_type = alert['type']
                
                if alert_type == "metric_alert":
                    metric = alert['device']['metric']
                    value = alert['device']['value']
                    alert_key = f"{alert_type}-{device_id}-{metric}"
                    
                    # 检查是否有此警报的缓存
                    if alert_key in self.alert_cache:
                        cached_alert = self.alert_cache[alert_key]
                        
                        # 只有当值发生变化时才发送新警报
                        if abs(cached_alert['device']['value'] - value) > 0.01:  # 允许微小浮动
                            self.alert_cache[alert_key] = alert
                            new_alerts.append(alert)
                    else:
                        # 首次发现的警报，直接添加到缓存和发送列表
                        self.alert_cache[alert_key] = alert
                        new_alerts.append(alert)
                        
                elif alert_type == "device_alert":
                    status = alert['device']['status']
                    alert_key = f"{alert_type}-{device_id}"
                    
                    # 检查是否有此警报的缓存
                    if alert_key in self.alert_cache:
                        cached_alert = self.alert_cache[alert_key]
                        
                        # 只有当状态发生变化时才发送新警报
                        if cached_alert['device']['status'] != status:
                            self.alert_cache[alert_key] = alert
                            new_alerts.append(alert)
                    else:
                        # 首次发现的警报，直接添加到缓存和发送列表
                        self.alert_cache[alert_key] = alert
                        new_alerts.append(alert)
            
            # 广播新警报
            if new_alerts:
                try:
                    await manager.broadcast_alerts(new_alerts)
                except Exception as e:
                    logger.error(f"广播实时警报时出错: {str(e)}")
            """
            
            # 记录日志，但不执行任何操作
            logger.info("收到实时警报，但已禁用广播功能")

# 创建告警生成器实例
alert_generator = AlertGenerator() 