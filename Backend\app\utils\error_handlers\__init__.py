"""
错误处理模块 - 用于捕获和处理各类异常
"""

import logging
from typing import Any, Dict, Optional, Union

# 配置日志
logger = logging.getLogger(__name__)

def handle_image_processing_error(e: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Union[str, bool]]:
    """
    处理图片处理相关的错误
    
    Args:
        e: 异常对象
        context: 错误上下文信息
        
    Returns:
        Dict: 包含错误信息的字典
    """
    error_msg = str(e)
    error_type = type(e).__name__
    
    # 记录错误日志
    logger.error(f"图片处理错误 [{error_type}]: {error_msg}")
    if context:
        logger.error(f"错误上下文: {context}")
    
    # 特殊处理某些类型的错误
    if "cannot unpack non-iterable Image object" in error_msg:
        return {
            "success": False,
            "error": "图片处理错误",
            "message": "无法处理文档中的某些图片，请检查文档格式",
            "details": error_msg
        }
    
    # 默认错误响应
    return {
        "success": False,
        "error": "图片处理错误",
        "message": "处理图片时发生错误",
        "details": error_msg
    }

def handle_pdf_processing_error(e: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Union[str, bool]]:
    """
    处理PDF文档处理相关的错误
    
    Args:
        e: 异常对象
        context: 错误上下文信息
        
    Returns:
        Dict: 包含错误信息的字典
    """
    error_msg = str(e)
    error_type = type(e).__name__
    
    # 记录错误日志
    logger.error(f"PDF处理错误 [{error_type}]: {error_msg}")
    if context:
        logger.error(f"错误上下文: {context}")
    
    # 处理特定类型的错误
    if "tokenize_table" in error_msg and "Image object" in error_msg:
        return {
            "success": False,
            "error": "表格处理错误",
            "message": "无法处理PDF中的表格或图片，可能是格式不兼容",
            "details": error_msg
        }
    
    # 默认错误响应
    return {
        "success": False,
        "error": "PDF处理错误",
        "message": "处理PDF文档时发生错误",
        "details": error_msg
    }

def handle_general_error(e: Exception, context: Optional[Dict[str, Any]] = None) -> Dict[str, Union[str, bool]]:
    """
    处理通用错误
    
    Args:
        e: 异常对象
        context: 错误上下文信息
        
    Returns:
        Dict: 包含错误信息的字典
    """
    error_msg = str(e)
    error_type = type(e).__name__
    
    # 记录错误日志
    logger.error(f"通用错误 [{error_type}]: {error_msg}")
    if context:
        logger.error(f"错误上下文: {context}")
    
    # 默认错误响应
    return {
        "success": False,
        "error": "处理错误",
        "message": "操作过程中发生错误",
        "details": error_msg
    } 