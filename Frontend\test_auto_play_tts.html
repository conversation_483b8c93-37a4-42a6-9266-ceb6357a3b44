<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TTS自动播放功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .controls {
            display: flex;
            gap: 10px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        button.enabled {
            background: #28a745;
        }
        button.auto-play {
            background: #17a2b8;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .message-simulator {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .message {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .queue-status {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TTS自动播放功能测试</h1>
        
        <div class="test-section">
            <h3>功能状态</h3>
            <div class="controls">
                <button id="ttsToggle" onclick="toggleTTS()">TTS: 禁用</button>
                <button id="autoPlayToggle" onclick="toggleAutoPlay()" disabled>自动播放: 禁用</button>
                <button onclick="checkTTSService()">检查TTS服务</button>
                <button onclick="clearQueue()">清空队列</button>
            </div>
            <div id="serviceStatus" class="status info">等待检查TTS服务...</div>
        </div>

        <div class="test-section">
            <h3>消息模拟器</h3>
            <p>模拟AI助手消息生成完成后的自动播放触发</p>
            <div class="controls">
                <button onclick="simulateMessage('简单测试消息')">简单消息</button>
                <button onclick="simulateMessage('点击查看完整的作业区生产油井A2日报')">A2日报消息</button>
                <button onclick="simulateMessage('这是一条较长的测试消息，包含多个句子。第一句话介绍背景。第二句话提供详细信息。第三句话总结要点。')">长消息</button>
                <button onclick="simulateMultipleMessages()">多条消息</button>
            </div>
            <div class="message-simulator">
                <div id="messageContainer">
                    <div class="message">等待消息生成...</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>播放队列状态</h3>
            <div id="queueStatus" class="queue-status">
                队列长度: 0<br>
                正在处理: 否<br>
                当前播放: 无
            </div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <button onclick="clearLog()">清空日志</button>
            <div id="testLog" class="log">等待测试开始...</div>
        </div>
    </div>

    <script>
        const TTS_SERVICE_URL = 'http://localhost:8003';
        
        // 模拟前端组件的状态
        let isTTSEnabled = false;
        let isTTSReady = false;
        let isAutoPlayEnabled = false;
        let audioQueue = [];
        let isProcessingQueue = false;
        let currentAudio = null;
        let isPlayingAudio = false;
        let currentPlayingMessage = null;
        let messageIdCounter = 1;

        // TTS配置
        const TTS_CONFIG = {
            voice_profile: "serious_male",
            voice_seed: 42,
            audio_seed: 123,
            temperature: 0.1,
            top_p: 0.5,
            top_k: 10,
            refine_text: true,
            preprocess_text: true
        };

        function log(message) {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}<br>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = '日志已清空<br>';
        }

        function updateUI() {
            const ttsBtn = document.getElementById('ttsToggle');
            const autoPlayBtn = document.getElementById('autoPlayToggle');
            
            ttsBtn.textContent = `TTS: ${isTTSEnabled ? '启用' : '禁用'}`;
            ttsBtn.className = isTTSEnabled ? 'enabled' : '';
            
            autoPlayBtn.textContent = `自动播放: ${isAutoPlayEnabled ? '启用' : '禁用'}`;
            autoPlayBtn.className = isAutoPlayEnabled ? 'auto-play' : '';
            autoPlayBtn.disabled = !isTTSEnabled || !isTTSReady;

            updateQueueStatus();
        }

        function updateQueueStatus() {
            const queueDiv = document.getElementById('queueStatus');
            queueDiv.innerHTML = `
                队列长度: ${audioQueue.length}<br>
                正在处理: ${isProcessingQueue ? '是' : '否'}<br>
                当前播放: ${currentPlayingMessage || '无'}
            `;
        }

        async function checkTTSService() {
            const statusDiv = document.getElementById('serviceStatus');
            statusDiv.className = 'status info';
            statusDiv.textContent = '检查中...';
            
            try {
                const response = await fetch(`${TTS_SERVICE_URL}/health`);
                if (response.ok) {
                    const data = await response.json();
                    isTTSReady = data.status === 'healthy' && data.model_loaded;
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `
                        TTS服务正常<br>
                        状态: ${data.status}<br>
                        模型已加载: ${data.model_loaded}<br>
                        设备: ${data.device}
                    `;
                    log('TTS服务检查成功');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                isTTSReady = false;
                statusDiv.className = 'status error';
                statusDiv.textContent = `TTS服务连接失败: ${error.message}`;
                log(`TTS服务检查失败: ${error.message}`);
            }
            updateUI();
        }

        function toggleTTS() {
            isTTSEnabled = !isTTSEnabled;
            if (!isTTSEnabled) {
                isAutoPlayEnabled = false;
                clearQueue();
                stopCurrentAudio();
            }
            log(`TTS功能${isTTSEnabled ? '已启用' : '已禁用'}`);
            updateUI();
        }

        function toggleAutoPlay() {
            if (!isTTSEnabled || !isTTSReady) {
                log('TTS服务未就绪，无法启用自动播放');
                return;
            }
            
            isAutoPlayEnabled = !isAutoPlayEnabled;
            if (!isAutoPlayEnabled) {
                clearQueue();
                stopCurrentAudio();
            }
            log(`TTS自动播放${isAutoPlayEnabled ? '已启用' : '已禁用'}`);
            updateUI();
        }

        function clearQueue() {
            audioQueue = [];
            isProcessingQueue = false;
            log('音频播放队列已清空');
            updateUI();
        }

        function stopCurrentAudio() {
            if (currentAudio) {
                currentAudio.pause();
                currentAudio = null;
            }
            isPlayingAudio = false;
            currentPlayingMessage = null;
            updateUI();
        }

        function simulateMessage(text) {
            const messageId = `msg_${messageIdCounter++}`;
            const message = {
                id: messageId,
                role: 'assistant',
                content: text
            };

            // 显示消息
            const container = document.getElementById('messageContainer');
            container.innerHTML = `<div class="message">消息ID: ${messageId}<br>内容: ${text}</div>`;

            log(`模拟消息生成: ${messageId} - "${text}"`);

            // 如果启用自动播放，添加到队列
            if (isAutoPlayEnabled) {
                addToAutoPlayQueue(message);
            }
        }

        function simulateMultipleMessages() {
            const messages = [
                '第一条消息：系统状态正常',
                '第二条消息：A2日报已生成',
                '第三条消息：所有设备运行良好'
            ];

            messages.forEach((text, index) => {
                setTimeout(() => {
                    simulateMessage(text);
                }, index * 2000);
            });
        }

        function addToAutoPlayQueue(message) {
            if (!isAutoPlayEnabled || !isTTSReady) {
                return;
            }

            log(`添加消息到自动播放队列: ${message.id}`);
            audioQueue.push(message);
            updateUI();

            if (!isProcessingQueue) {
                processAudioQueue();
            }
        }

        async function processAudioQueue() {
            if (isProcessingQueue || audioQueue.length === 0) {
                return;
            }

            isProcessingQueue = true;
            log(`开始处理音频队列，队列长度: ${audioQueue.length}`);
            updateUI();

            while (audioQueue.length > 0 && isAutoPlayEnabled) {
                const message = audioQueue.shift();
                updateUI();

                try {
                    log(`自动播放消息: ${message.id}`);
                    await playMessageAudio(message, true);
                    await waitForAudioComplete();
                } catch (error) {
                    log(`自动播放出错: ${error.message}`);
                }
            }

            isProcessingQueue = false;
            log('音频队列处理完成');
            updateUI();
        }

        async function playMessageAudio(message, isAutoPlay = false) {
            if (!isTTSEnabled || !isTTSReady) {
                return;
            }

            stopCurrentAudio();

            try {
                const playType = isAutoPlay ? '自动播放' : '手动播放';
                log(`开始${playType}TTS: ${message.content.substring(0, 30)}...`);

                isPlayingAudio = true;
                currentPlayingMessage = message.id;
                updateUI();

                const response = await fetch(`${TTS_SERVICE_URL}/tts`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: message.content,
                        ...TTS_CONFIG
                    })
                });

                if (!response.ok) {
                    throw new Error(`TTS请求失败: ${response.status}`);
                }

                const result = await response.json();

                if (!result.success) {
                    throw new Error(result.message);
                }

                const audioUrl = `data:audio/wav;base64,${result.audio_base64}`;
                currentAudio = new Audio(audioUrl);

                currentAudio.addEventListener('ended', () => {
                    isPlayingAudio = false;
                    currentPlayingMessage = null;
                    currentAudio = null;
                    updateUI();
                });

                currentAudio.addEventListener('error', (e) => {
                    log(`音频播放错误: ${e.message}`);
                    isPlayingAudio = false;
                    currentPlayingMessage = null;
                    currentAudio = null;
                    updateUI();
                });

                await currentAudio.play();
                log(`${playType}TTS播放开始，预计时长: ${result.duration?.toFixed(2)}秒`);

            } catch (error) {
                log(`${isAutoPlay ? '自动' : '手动'}TTS播放失败: ${error.message}`);
                isPlayingAudio = false;
                currentPlayingMessage = null;
                currentAudio = null;
                updateUI();
            }
        }

        function waitForAudioComplete() {
            return new Promise((resolve) => {
                if (!currentAudio || currentAudio.ended) {
                    resolve();
                    return;
                }

                const checkComplete = () => {
                    if (!currentAudio || currentAudio.ended || !isPlayingAudio) {
                        resolve();
                    } else {
                        setTimeout(checkComplete, 100);
                    }
                };

                checkComplete();
            });
        }

        // 页面加载时初始化
        window.addEventListener('load', () => {
            log('页面加载完成，开始初始化');
            updateUI();
            checkTTSService();
        });
    </script>
</body>
</html>
