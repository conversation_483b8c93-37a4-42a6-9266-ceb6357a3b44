# 发布说明

整个项目基于Docker Compose进行发布，后续考虑 K8s 进行分布式管理。

## 技术栈

* 后端服务框架: Fastapi、LangChain
* LLM 提供服务: Ollama
* LLM 模型: qwen2.5:32b
* 向量数据库: milvus
* 向量化模型: bge-m3
* MQTT Broker: emqx
* 前端页面: Vue3
* 数字孪生: Threejs
* 关系数据库：MySQL


## 系统运行需要的服务容器包括

1. Nginx: 提供发布后的VUE3项目静态访问，提供 api 接口转发；
1. Python: Fastapi与LangChain服务端运行环境；
1. Milvus: 单机运行环境，按照官网配置，同时启动三个容器，包括etcd、minio、standalone，后续可扩展为高可用模式；
1. Ollama: 提供模型运行环境，包括大语言模型qwen2.5:32b和向量化模型bge-m3；
1. Mysql: 数据库，提供必要的数据存储；
1. Emqx: MQTT Broker基础运行服务器；


## 使用说明

1. 创建基础目录和初始文件

```bash
# 按需要替换/Docker 目标文件夹
mkdir -p /Docker目标文件夹
cd Deploy    # 进入发布文件夹
cp docker-compose.yml /Docker目标文件夹  
cp init.sh /Docker目标文件夹
cd /Docker目标文件夹  
chmod +x Deploy/init.sh && ./Deploy/init.sh

# 下方代码容器化发布
cd IOT_Agent_MVP2           # 退回项目文件夹
cp -R                      # 将后端代码复制到pyagent目录中，后续需要进入容器内安装依赖
cp -R                      # 将前端代码 build 以后，复制 public 文件夹中的内容到 nginx/vue 中
cp -R                      # 将 unity 项目 webgl发布以后，复制发布文件到 nginx/unity 中
```

2. 启动

```bash
docker-compose up -d &
```

3. 停止

```bash
docker-compose down
```

4. 更新

```bash
docker-compose down
docker-compose up -d &
```
