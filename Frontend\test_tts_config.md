# 前端TTS配置修改说明

## 修改内容

### 1. 添加固定TTS配置常量

在ChatPanel.vue中添加了`TTS_CONFIG`常量，确保每次TTS调用都使用相同的参数：

```javascript
const TTS_CONFIG = {
  voice_profile: "serious_male",    // 固定使用严肃智慧男性音色
  voice_seed: 42,                   // 固定音色种子，确保音色一致
  audio_seed: 123,                  // 固定音频种子，确保生成一致
  temperature: 0.1,                 // 降低随机性，获得更稳定的朗读
  top_p: 0.5,                       // 降低采样范围，提高准确性
  top_k: 10,                        // 减少候选词数量，提高一致性
  refine_text: true,                // 启用文本优化
  preprocess_text: true             // 启用Markdown文本预处理
}
```

### 2. 修改playMessageAudio函数

更新了TTS API调用，使用固定配置：

```javascript
body: JSON.stringify({
  text: textContent,
  ...TTS_CONFIG  // 使用固定的TTS配置，确保音色和参数完全一致
})
```

### 3. 增强文本提取和调试

- 在`extractTextFromHTML`函数中添加了调试日志
- 检测A2等字母数字组合并记录日志
- 在TTS播放时显示使用的配置信息

## 预期效果

### 1. 音色固定化
- 每次TTS调用都使用`serious_male`音色配置
- 固定的`voice_seed: 42`确保音色完全一致
- 固定的`audio_seed: 123`确保生成结果一致

### 2. 去除口语化
- `temperature: 0.1` - 大幅降低随机性
- `top_p: 0.5` - 减少采样范围
- `top_k: 10` - 限制候选词数量
- 这些参数组合产生更稳定、正式的朗读风格

### 3. 文本预处理
- `preprocess_text: true` - 启用Markdown格式清理
- A2、B1等字母数字组合将被转换为"A二"、"B一"等中文表达
- 去除格式符号，保留纯文本内容

## 测试方法

1. **启动前端应用**
2. **打开聊天面板**
3. **发送包含A2日报的消息**
4. **点击TTS播放按钮**
5. **检查浏览器控制台日志**：
   - 应显示TTS配置信息
   - 应检测到A2字母数字组合
   - 应显示固定音色配置使用情况

## 控制台日志示例

```
提取的TTS文本内容: 点击查看完整的作业区生产油井A2日报
检测到字母数字组合: ["A2"]
这些组合将通过TTS预处理转换为中文朗读
开始TTS播放: 点击查看完整的作业区生产油井A2日报...
使用TTS配置: {voice_profile: "serious_male", voice_seed: 42, audio_seed: 123, ...}
TTS播放开始，预计时长: 3.45秒
使用固定音色配置 - 音色: serious_male, 种子: 42
```

## 验证要点

1. **音色一致性**：多次播放同一文本，音色应完全相同
2. **A2朗读**：A2应被朗读为"A二"而不是"A"
3. **朗读风格**：应为严肃、正式的朗读，无口语化特征
4. **参数固定**：每次调用都使用相同的voice_seed和audio_seed

## 故障排除

如果TTS效果不符合预期：

1. **检查TTS服务**：确保后端TTS服务已更新并重启
2. **检查网络请求**：在浏览器开发者工具中查看TTS API请求参数
3. **检查控制台日志**：确认配置参数正确传递
4. **测试不同文本**：尝试包含和不包含A2的不同文本内容
