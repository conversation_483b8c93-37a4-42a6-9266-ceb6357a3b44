// Ollama AI 服务通信工具类
class OllamaService {
  constructor() {
    // 默认配置
    this.baseUrl = 'http://127.0.0.1:11434/api'
    this.defaultModel = 'qwen2.5:7b'
    this.defaultOptions = {
      temperature: 0.7,
      top_p: 0.9,
      stream: true
    }
  }

  /**
   * 发起流式对话请求
   * @param {string} prompt 用户输入的问题
   * @param {function} onMessage 处理流式消息的回调函数
   * @param {function} onError 处理错误的回调函数
   * @param {object} options 可选参数，包括 signal 用于取消请求
   * @returns {Promise} 返回一个 Promise 对象
   */
  async streamChat(prompt, onMessage, onError, options = {}) {
    try {
      // 构建请求体
      const requestBody = {
        model: options.model || this.defaultModel,
        prompt: prompt,
        stream: true,
        ...this.defaultOptions,
        ...options
      }

      // 从 options 中提取 signal，不传递给 API
      const { signal } = options;
      delete requestBody.signal;

      console.log('Ollama请求参数:', requestBody)

      const response = await fetch(`${this.baseUrl}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal // 传递 AbortController 的 signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body.getReader()
      const decoder = new TextDecoder()

      while (true) {
        const { value, done } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.trim() === '') continue

          try {
            const data = JSON.parse(line)
            console.log('Ollama响应数据:', data)

            // 调用回调函数处理流式消息
            if (data.response) {
              onMessage(data.response)
            }

            // 如果生成完成，结束流式处理
            if (data.done) {
              return {
                response: data.response,
                context: data.context,
                totalDuration: data.total_duration,
                promptEvalCount: data.prompt_eval_count,
                evalCount: data.eval_count
              }
            }
          } catch (e) {
            console.error('解析响应数据出错:', e)
            console.error('原始数据:', line)
          }
        }
      }
    } catch (error) {
      console.error('Ollama API 调用出错:', error)
      onError(error)
      throw error
    }
  }

  /**
   * 停止生成（通过 AbortController 实现）
   * 注意：此方法不直接调用 Ollama 的停止 API，而是通过中断请求实现
   */
  stopGeneration() {
    // 由于我们使用 AbortController 实现停止，这个方法实际上不需要做任何事情
    // 调用方应该直接使用 AbortController.abort() 方法
    console.log('停止生成请求')
  }

  /**
   * 发起非流式对话请求
   * @param {string} prompt 用户输入的问题
   * @param {object} options 可选参数
   * @returns {Promise} 返回一个 Promise 对象
   */
  async chat(prompt, options = {}) {
    try {
      const requestBody = {
        model: options.model || this.defaultModel,
        prompt: prompt,
        stream: false,
        ...this.defaultOptions,
        ...options
      }

      // 从 options 中提取 signal，不传递给 API
      const { signal } = options;
      delete requestBody.signal;

      const response = await fetch(`${this.baseUrl}/generate`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        signal // 传递 AbortController 的 signal
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      return {
        response: data.response,
        context: data.context,
        totalDuration: data.total_duration,
        promptEvalCount: data.prompt_eval_count,
        evalCount: data.eval_count
      }
    } catch (error) {
      console.error('Ollama API 调用出错:', error)
      throw error
    }
  }
}

export const ollamaService = new OllamaService()