#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from typing import Dict, Any, Optional
from pydantic import BaseModel
from .redmine_config import redmine_config

logger = logging.getLogger(__name__)

class WikiPageData(BaseModel):
    """Wiki页面数据模型"""
    project_id: str
    title: str
    text: str
    comments: Optional[str] = None
    version: Optional[int] = None

async def get_wiki_page(project_id: str, title: str) -> Dict[str, Any]:
    """
    获取Redmine Wiki页面
    
    获取指定项目中的Wiki页面内容。
    """
    try:
        redmine = redmine_config.get_client()
        
        # 获取Wiki页面
        wiki_page = redmine.wiki_page.get(title, project_id=project_id)
        
        # 转换为可序列化的字典
        data = {
            "title": getattr(wiki_page, 'title', ''),
            "text": getattr(wiki_page, 'text', ''),
            "version": getattr(wiki_page, 'version', 0),
            "author": {
                "id": getattr(wiki_page.author, 'id', 0),
                "name": getattr(wiki_page.author, 'name', '未知用户')
            } if hasattr(wiki_page, 'author') else {"id": 0, "name": "未知用户"},
            "created_on": getattr(wiki_page, 'created_on', '').strftime("%Y-%m-%d %H:%M:%S") if hasattr(wiki_page, 'created_on') and wiki_page.created_on else '',
            "updated_on": getattr(wiki_page, 'updated_on', '').strftime("%Y-%m-%d %H:%M:%S") if hasattr(wiki_page, 'updated_on') and wiki_page.updated_on else '',
        }
        
        # 获取上一个版本信息
        if hasattr(wiki_page, 'version') and wiki_page.version > 1:
            try:
                previous_version = redmine.wiki_page.get(
                    title, 
                    project_id=project_id,
                    version=wiki_page.version - 1
                )
                data["previous_version"] = {
                    "version": getattr(previous_version, 'version', 0),
                    "author": {
                        "id": getattr(previous_version.author, 'id', 0),
                        "name": getattr(previous_version.author, 'name', '未知用户')
                    } if hasattr(previous_version, 'author') else {"id": 0, "name": "未知用户"},
                    "updated_on": getattr(previous_version, 'updated_on', '').strftime("%Y-%m-%d %H:%M:%S") if hasattr(previous_version, 'updated_on') and previous_version.updated_on else '',
                }
            except:
                data["previous_version"] = None
        else:
            data["previous_version"] = None
            
        # 获取附件信息
        if hasattr(wiki_page, 'attachments') and wiki_page.attachments:
            try:
                attachments = []
                for attachment in wiki_page.attachments:
                    attachments.append({
                        "id": getattr(attachment, 'id', 0),
                        "filename": getattr(attachment, 'filename', ''),
                        "filesize": getattr(attachment, 'filesize', 0),
                        "content_url": getattr(attachment, 'content_url', ''),
                        "created_on": getattr(attachment, 'created_on', '').strftime("%Y-%m-%d %H:%M:%S") if hasattr(attachment, 'created_on') and attachment.created_on else '',
                    })
                data["attachments"] = attachments
            except:
                data["attachments"] = []
        else:
            data["attachments"] = []
        
        return {
            "wiki_page": data,
            "project_id": project_id,
            "status": "success"
        }
        
    except Exception as e:
        logger.error(f"获取Redmine Wiki页面失败: {str(e)}")
        raise Exception(f"获取Redmine Wiki页面失败: {str(e)}")

async def update_wiki_page(wiki_page: WikiPageData) -> Dict[str, Any]:
    """
    更新Redmine Wiki页面
    
    更新或创建指定项目中的Wiki页面。
    """
    try:
        redmine = redmine_config.get_client()
        
        # 准备更新参数
        update_params = {
            'project_id': wiki_page.project_id,
            'title': wiki_page.title,
            'text': wiki_page.text
        }
        
        if wiki_page.comments:
            update_params['comments'] = wiki_page.comments
            
        if wiki_page.version:
            update_params['version'] = wiki_page.version
        
        # 检查Wiki页面是否存在
        try:
            existing_page = redmine.wiki_page.get(wiki_page.title, project_id=wiki_page.project_id)
            # 如果存在，更新它
            updated_page = redmine.wiki_page.update(
                wiki_page.title,
                **update_params
            )
            action = "updated"
        except:
            # 如果不存在，创建它
            updated_page = redmine.wiki_page.create(
                project_id=wiki_page.project_id,
                title=wiki_page.title,
                text=wiki_page.text
            )
            action = "created"
        
        # 返回更新后的Wiki页面信息
        return {
            "status": "success",
            "action": action,
            "wiki_page": {
                "title": updated_page.title if hasattr(updated_page, 'title') else wiki_page.title,
                "version": updated_page.version if hasattr(updated_page, 'version') else 1,
                "project_id": wiki_page.project_id,
                "url": f"{redmine_config.url}/projects/{wiki_page.project_id}/wiki/{wiki_page.title}"
            }
        }
        
    except Exception as e:
        logger.error(f"更新Redmine Wiki页面失败: {str(e)}")
        raise Exception(f"更新Redmine Wiki页面失败: {str(e)}") 