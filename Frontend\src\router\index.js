import { createRouter, createWebHistory } from 'vue-router'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: () => import('../components/Home.vue')
    },
    {
      path: '/scene',
      name: 'scene',
      component: () => import('../components/Scene/Scene.vue')
    }
  ]
})

export default router 