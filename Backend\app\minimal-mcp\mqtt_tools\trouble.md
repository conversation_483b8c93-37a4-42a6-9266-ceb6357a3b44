# MQTT工具故障排除指南

## EMQX 5.8.5 集成常见问题

本文档用于解决使用MQTT发现工具(`mqtt_discovery.py`)与EMQX 5.8.5集成时可能遇到的问题。

## 连接配置说明

在`mqtt_discovery.py`中，我们使用以下连接配置：

```
主机: 127.0.0.1
WebSocket端口: 5710
TCP端口: 1883
API端口: 5712
WebSocket路径: /mqtt
```

### 端口号说明

各个端口的作用：

1. **WebSocket端口(5710)**: 用于通过WebSocket协议连接MQTT服务器
   - 我们优先使用WebSocket连接，因为它更适合通过防火墙和代理服务器
   - 连接URL: `ws://127.0.0.1:5710/mqtt`

2. **TCP端口(1883)**: 用于通过TCP协议直接连接MQTT服务器
   - 作为WebSocket连接失败时的备选方案
   - 连接URL: `tcp://127.0.0.1:1883`

3. **API端口(5712)**: 用于访问EMQX HTTP API
   - 用来获取主题列表、认证等管理功能
   - API基础URL: `http://127.0.0.1:5712/api/v5/`

### 如何确定使用哪个端口

端口选择逻辑：

1. 代码会先尝试使用WebSocket端口(5710)连接
2. 如果WebSocket连接失败，会尝试TCP端口(1883)
3. 无论使用哪种连接方式，都会尝试通过API端口(5712)获取主题信息

如果您需要修改端口号，请检查并更新以下文件中的配置：
- `mqtt_discovery.py` 中的 `EMQX_CONFIGS` 变量
- `config.json` 中的 MQTT 连接配置

## 常见问题

### 1. 无法发现任何主题

**症状**: 运行`mqtt_discovery.py`后没有发现任何MQTT主题

**可能的原因**:
- EMQX服务器未运行或无法访问
- 连接端口配置错误
- ACL权限设置问题
- 客户端ID不匹配

**解决方法**:

1. **检查EMQX服务器状态**:
   - 访问EMQX管理界面: `http://127.0.0.1:18083`（默认管理端口）
   - 使用默认账号: admin/public

2. **验证端口配置**:
   ```bash
   # 测试WebSocket端口
   curl -v ws://127.0.0.1:5710
   
   # 测试TCP端口
   telnet 127.0.0.1 1883
   
   # 测试API端口
   curl http://127.0.0.1:5712/api/v5/brokers
   ```

3. **检查ACL权限**:
   - 在EMQX管理界面中，进入"访问控制" > "ACL"
   - 确保有一条规则允许客户端ID为"mqtt-discovery"的客户端订阅所有主题
   - 添加ACL规则:
     - 客户端ID: mqtt-discovery
     - 操作: 允许
     - 权限: 发布和订阅
     - 主题: # 和 $SYS/#
     - 优先级: 1（比默认规则优先级高）

4. **在EMQX中查看活跃主题**:
   - 在EMQX管理界面中，进入"监控" > "主题"
   - 这里会列出所有活跃的主题
   - 如果这里没有显示任何主题，可能是因为没有客户端正在发布消息

### 2. 只能发现预定义主题

**症状**: 只能发现BASIC_TOPICS中预定义的主题，无法发现其他主题

**解决方法**:

1. **检查是否有客户端正在发布消息**:
   - 在EMQX管理界面中，查看"客户端"页面
   - 检查是否有除mqtt-discovery外的其他客户端连接

2. **手动触发设备发送消息**:
   - 如果可能，触发您的设备发送MQTT消息
   - 或使用MQTT客户端工具(如MQTTX)手动发布消息到相关主题

3. **增加发现时间**:
   - 修改`mqtt_discovery.py`中的`discovery_time`值(默认30秒)
   - ```python
     discovery_time = 60  # 增加到60秒
     ```

### 3. 无法访问系统主题($SYS/...)

**症状**: 无法订阅或接收系统主题($SYS开头的主题)

**解决方法**:

1. **检查EMQX 5.8.5的系统主题访问控制**:
   - EMQX 5.8.5默认限制客户端访问系统主题
   - 管理界面: "访问控制" > "系统主题" 

2. **修改系统主题访问权限**:
   - 如果启用了authorz插件，添加允许订阅$SYS/#的规则
   - 或在配置文件中修改`allow_anonymous=true`

3. **使用EMQX API获取系统信息**:
   - 如直接订阅系统主题失败，可通过API获取系统信息
   - 例如: `http://127.0.0.1:5712/api/v5/brokers`

### 4. EMQX API认证失败

**症状**: 通过API获取主题列表时返回认证错误

**解决方法**:

1. **检查API凭据**:
   - 脚本中使用的默认凭据是`admin/public`
   - 如果已修改，请更新`try_single_api_endpoint`函数中的用户名和密码

2. **确认API端口**:
   - EMQX 5.8.5可能使用不同的API端口
   - 检查EMQX配置文件中的`management.listener.http.port`

3. **检查API路径**:
   - EMQX 5.8.5的API路径为`/api/v5/`
   - 确认`api_path`参数正确

### 5. 设备显示名称重复或不合理

**症状**: 发现的设备显示名称重复或不符合预期

**解决方法**:

1. **了解自动命名规则**:
   - JH系列设备: 使用数字部分，如JH001 → 井01
   - HN系列设备: 使用哈希值，如HN15V3 → 井H42
   - 其他设备: 使用站点+设备名哈希值，如 → 井S78

2. **手动修改设备名称**:
   - 直接编辑`config.json`文件
   - 修改`display_name`字段为您期望的名称

3. **修改命名逻辑**:
   - 如需定制，修改`parse_topic_to_device`函数中的命名逻辑

## 调试技巧

### 启用详细日志

为了获取更多调试信息，修改代码开头的日志级别：

```python
# 配置日志
logging.basicConfig(
    level=logging.DEBUG,  # 改为DEBUG级别
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
```

### 使用MQTTX测试连接

MQTTX是一个优秀的MQTT客户端工具，可用于测试连接和发布/订阅主题：

1. 配置连接:
   - 名称: EMQX-Test
   - 主机: 127.0.0.1
   - 端口: 5710 (WebSocket)或1883 (TCP)
   - 客户端ID: 任意唯一ID
   - 如使用WebSocket，需指定路径: /mqtt

2. 测试主题:
   - 订阅: # (所有主题)
   - 发布消息到一个测试主题

如果MQTTX能够成功连接并收发消息，但`mqtt_discovery.py`失败，则可能是代码中的连接配置或权限问题。

## 如何检查MQTT主题和消息

在EMQX 5.8.5中，您可以通过以下方式检查主题和消息：

1. **通过EMQX管理界面**:
   - 访问: http://127.0.0.1:18083
   - 导航到: "监控" > "主题"
   - 查看: "客户端" > "订阅"

2. **使用EMQX CLI**:
   ```bash
   # 列出所有主题
   ./bin/emqx ctl topics list
   
   # 查看指定主题的订阅者
   ./bin/emqx ctl topics show "topic/name"
   ```

3. **使用EMQX API**:
   ```bash
   # 获取所有主题
   curl -u admin:public http://127.0.0.1:5712/api/v5/topics
   
   # 获取特定主题
   curl -u admin:public http://127.0.0.1:5712/api/v5/topics/{topic_name}
   ```

## 结论

如果按照上述故障排除步骤仍然无法解决问题，可能需要:

1. 检查网络连接和防火墙设置
2. 查看EMQX服务器日志(通常在`/var/log/emqx/`目录)
3. 更新MQTT客户端库(paho-mqtt)到最新版本
4. 咨询EMQX社区或支持团队

希望本指南能帮助您解决MQTT发现工具与EMQX 5.8.5集成过程中遇到的问题。
