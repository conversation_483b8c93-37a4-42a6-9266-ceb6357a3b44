# RTSP代理服务器

这是一个基于Python的RTSP代理服务器，可以将RTSP视频流转换为Web浏览器可访问的MJPEG格式。

## 功能特点

- 将RTSP流转换为MJPEG流，在浏览器中直接查看
- 支持多路RTSP流同时转换
- 提供Web界面管理流媒体
- 支持自定义分辨率和帧率
- RESTful API支持流的创建、查询和删除
- 自动查找FFmpeg路径

## 安装要求

- Python 3.7+
- FFmpeg (系统安装或本地bin目录下)
- Flask
- Flask-CORS

## 安装步骤

1. 安装Python依赖:

```bash
pip install -r requirements.txt
```

2. 确保安装了FFmpeg:

Windows:
```
# 下载FFmpeg: https://ffmpeg.org/download.html
# 将FFmpeg添加到系统路径或放置在bin目录中
```

Linux:
```
sudo apt update
sudo apt install ffmpeg
```

## 使用方法

### 启动服务器

```bash
python rtsp_proxy.py --host 0.0.0.0 --port 8000
```

参数说明:
- `--host`: 监听地址 (默认: 0.0.0.0)
- `--port`: 监听端口 (默认: 8000)
- `--debug`: 启用调试模式

### Web界面使用

1. 访问 `http://localhost:8000/` 查看Web管理界面
2. 使用表单添加新的RTSP流
3. 点击流链接查看视频内容

### API使用

#### 创建新流

```
POST /api/streams/create
Content-Type: application/json

{
  "url": "rtsp://user:<EMAIL>/stream",
  "name": "camera1",
  "fps": 15,
  "width": 640,
  "height": 480
}
```

#### 查看所有流

```
GET /api/streams
```

#### 停止流

```
POST /api/streams/{stream_name}/stop
```

#### 停止所有流

```
POST /api/streams/stopAll
```

#### 查看服务状态

```
GET /api/status
```

## 前端集成

在前端应用中，可以通过以下方式显示MJPEG流:

```html
<img src="http://localhost:8000/api/stream/{stream_name}/mjpeg" alt="RTSP Stream">
```

## 故障排除

1. 确保RTSP URL格式正确且可访问
2. 检查FFmpeg是否正确安装
3. 查看服务器日志了解详细错误信息

## 许可

MIT 