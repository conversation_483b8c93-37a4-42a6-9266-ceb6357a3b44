#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试A2日报朗读效果
"""

import requests
import base64

# TTS服务地址
TTS_URL = "http://localhost:8003"

def quick_test():
    """快速测试A2朗读"""
    print("快速测试A2日报朗读...")
    
    # 测试文本
    text = "点击查看完整的作业区生产油井A2日报"
    
    print(f"测试文本: {text}")
    
    try:
        # 1. 先测试文本预处理
        print("\n1. 测试文本预处理...")
        response = requests.post(
            f"{TTS_URL}/preprocess",
            json={"text": text}
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"原始文本: {result['original_text']}")
            print(f"处理后文本: {result['processed_text']}")
        else:
            print(f"预处理失败: {response.status_code}")
            return
        
        # 2. 测试TTS生成
        print("\n2. 测试TTS生成...")
        response = requests.post(
            f"{TTS_URL}/tts",
            json={
                "text": text,
                "voice_profile": "serious_male",
                "preprocess_text": True
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            
            if result["success"]:
                # 保存音频
                audio_data = base64.b64decode(result["audio_base64"])
                output_file = "quick_test_a2.wav"
                
                with open(output_file, "wb") as f:
                    f.write(audio_data)
                
                print(f"音频已保存到: {output_file}")
                print(f"音频时长: {result['duration']:.2f}秒")
                print(f"消息: {result['message']}")
                print("\n请播放音频文件检查A2是否被正确朗读")
            else:
                print(f"TTS生成失败: {result['message']}")
        else:
            print(f"TTS请求失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"测试出错: {e}")

def test_service_status():
    """测试服务状态"""
    print("检查服务状态...")
    
    try:
        response = requests.get(f"{TTS_URL}/health")
        if response.status_code == 200:
            result = response.json()
            print(f"服务状态: {result['status']}")
            print(f"使用设备: {result.get('device', 'unknown')}")
            print(f"默认音色: {result.get('default_voice', 'N/A')}")
            print(f"音色种子: {result.get('fixed_voice_seed', 'N/A')}")
            return True
        else:
            print(f"服务检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"无法连接到服务: {e}")
        return False

if __name__ == "__main__":
    if test_service_status():
        print("\n" + "="*50)
        quick_test()
    else:
        print("请先启动TTS服务")
