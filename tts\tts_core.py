#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChatTTS核心功能模块
封装ChatTTS的核心功能，提供更好的接口和错误处理
"""

import os
import io
import torch
import torchaudio
import numpy as np
from typing import List, Optional, Dict, Any, Tuple
from loguru import logger
import ChatTTS

class TTSCore:
    """ChatTTS核心类"""
    
    def __init__(self):
        self.chat_tts = None
        self.device = None
        self.is_initialized = False
        self.default_sample_rate = 24000
        
    def initialize(self, device: Optional[str] = None, compile_model: bool = False, force_download: bool = False) -> bool:
        """
        初始化ChatTTS模型

        Args:
            device: 指定设备 ('cpu', 'cuda', 'auto')
            compile_model: 是否编译模型以提高性能
            force_download: 是否强制重新下载模型

        Returns:
            bool: 初始化是否成功
        """
        try:
            logger.info("开始初始化ChatTTS模型...")

            # 确定设备
            if device == "auto" or device is None:
                if torch.cuda.is_available():
                    self.device = torch.device("cuda:0")
                    logger.info(f"检测到CUDA设备: {torch.cuda.get_device_name(0)}")
                else:
                    self.device = torch.device("cpu")
                    logger.warning("未检测到CUDA设备，使用CPU")
            else:
                self.device = torch.device(device)

            logger.info(f"使用设备: {self.device}")

            # 初始化ChatTTS
            self.chat_tts = ChatTTS.Chat()

            # 尝试加载模型
            try:
                logger.info("正在加载ChatTTS模型...")
                success = self.chat_tts.load(
                    compile=compile_model,
                    device=str(self.device)  # 明确指定设备
                )

                if success:
                    # 确保模型在正确的设备上
                    if hasattr(self.chat_tts, 'gpt') and self.chat_tts.gpt is not None:
                        self.chat_tts.gpt = self.chat_tts.gpt.to(self.device)
                        logger.info(f"GPT模型已移动到设备: {self.device}")

                    if hasattr(self.chat_tts, 'dvae') and self.chat_tts.dvae is not None:
                        self.chat_tts.dvae = self.chat_tts.dvae.to(self.device)
                        logger.info(f"DVAE模型已移动到设备: {self.device}")

                    if hasattr(self.chat_tts, 'vocos') and self.chat_tts.vocos is not None:
                        self.chat_tts.vocos = self.chat_tts.vocos.to(self.device)
                        logger.info(f"Vocos模型已移动到设备: {self.device}")

                    self.is_initialized = True
                    logger.info("ChatTTS模型初始化成功")

                    # 显示GPU内存使用情况
                    if self.device.type == 'cuda':
                        memory_allocated = torch.cuda.memory_allocated(self.device) / 1024**3
                        memory_reserved = torch.cuda.memory_reserved(self.device) / 1024**3
                        logger.info(f"GPU内存使用: {memory_allocated:.2f}GB / {memory_reserved:.2f}GB")

                    return True
                else:
                    logger.error("ChatTTS模型加载失败")
                    return False

            except Exception as load_error:
                logger.error(f"模型加载出错: {str(load_error)}")

                # 如果是网络问题，提供解决方案提示
                if "connectex" in str(load_error) or "network" in str(load_error).lower():
                    logger.error("检测到网络连接问题，无法下载模型文件")
                    logger.error("解决方案：")
                    logger.error("1. 检查网络连接")
                    logger.error("2. 使用VPN或代理")
                    logger.error("3. 手动下载模型文件")
                    logger.error("4. 配置镜像源")

                return False

        except Exception as e:
            logger.error(f"初始化ChatTTS时出错: {str(e)}")
            return False
    
    def is_ready(self) -> bool:
        """检查模型是否已准备就绪"""
        return self.is_initialized and self.chat_tts is not None
    
    def refine_text(self, text: str, **params) -> str:
        """
        优化文本，添加适当的停顿和语调标记

        Args:
            text: 原始文本
            **params: 优化参数

        Returns:
            str: 优化后的文本
        """
        if not self.is_ready():
            raise RuntimeError("TTS模型未初始化")

        try:
            # 创建参数对象
            refine_params = self.chat_tts.RefineTextParams()
            refine_params.temperature = params.get('temperature', 0.3)
            refine_params.top_p = params.get('top_p', 0.7)
            refine_params.top_k = params.get('top_k', 20)

            # 使用ChatTTS优化文本
            refined_texts = self.chat_tts.infer(
                text=[text],
                skip_refine_text=False,
                refine_text_only=True,
                params_refine_text=refine_params
            )

            if refined_texts and len(refined_texts) > 0:
                return refined_texts[0]
            else:
                logger.warning("文本优化失败，返回原始文本")
                return text

        except Exception as e:
            logger.error(f"文本优化出错: {str(e)}")
            return text
    
    def generate_speech(
        self,
        text: str,
        voice_seed: Optional[int] = None,
        audio_seed: Optional[int] = None,
        refine_text: bool = True,
        **params
    ) -> Tuple[np.ndarray, int]:
        """
        生成语音

        Args:
            text: 输入文本
            voice_seed: 声音种子，用于控制音色
            audio_seed: 音频种子，用于控制生成的随机性
            refine_text: 是否优化文本
            **params: 其他生成参数

        Returns:
            Tuple[np.ndarray, int]: (音频数据, 采样率)
        """
        if not self.is_ready():
            raise RuntimeError("TTS模型未初始化")

        try:
            logger.info(f"开始生成语音，使用设备: {self.device}")

            # 设置随机种子 - 确保完全固定，消除所有随机性
            # 强制设置所有可能的随机种子源
            import random
            import numpy as np

            # 使用固定种子确保绝对一致性
            fixed_seed = voice_seed if voice_seed is not None else 42

            # 设置Python内置random模块
            random.seed(fixed_seed)

            # 设置numpy随机种子
            np.random.seed(fixed_seed)

            # 设置PyTorch随机种子
            torch.manual_seed(fixed_seed)
            torch.random.manual_seed(fixed_seed)

            # 设置CUDA随机种子（如果使用GPU）
            if self.device.type == 'cuda':
                torch.cuda.manual_seed(fixed_seed)
                torch.cuda.manual_seed_all(fixed_seed)
                # 确保CUDA操作的确定性
                torch.backends.cudnn.deterministic = True
                torch.backends.cudnn.benchmark = False

            # 如果有audio_seed，也使用相同的种子确保一致性
            if audio_seed is not None:
                # 所有种子都使用相同的值，确保完全一致
                torch.manual_seed(fixed_seed)
                if self.device.type == 'cuda':
                    torch.cuda.manual_seed(fixed_seed)
                    torch.cuda.manual_seed_all(fixed_seed)

            # 创建参数对象 - 使用极低的随机性参数
            infer_params = self.chat_tts.InferCodeParams()
            infer_params.temperature = 0.01  # 极低温度，几乎无随机性
            infer_params.top_p = 0.1         # 极低top_p，最小采样范围
            infer_params.top_k = 1           # 只选择最可能的词

            refine_params = self.chat_tts.RefineTextParams()
            refine_params.temperature = 0.01  # 极低温度
            refine_params.top_p = 0.1         # 极低top_p
            refine_params.top_k = 1           # 只选择最可能的词

            # 记录GPU内存使用情况（如果使用GPU）
            if self.device.type == 'cuda':
                memory_before = torch.cuda.memory_allocated(self.device) / 1024**3
                logger.info(f"生成前GPU内存使用: {memory_before:.2f}GB")

            # 在生成前再次设置随机种子，确保绝对一致性
            torch.manual_seed(fixed_seed)
            if self.device.type == 'cuda':
                torch.cuda.manual_seed(fixed_seed)
                torch.cuda.manual_seed_all(fixed_seed)

            with torch.no_grad():
                # 生成音频
                wavs = self.chat_tts.infer(
                    text=[text],
                    skip_refine_text=not refine_text,
                    params_infer_code=infer_params,
                    params_refine_text=refine_params if refine_text else None
                )

            # 记录GPU内存使用情况（如果使用GPU）
            if self.device.type == 'cuda':
                memory_after = torch.cuda.memory_allocated(self.device) / 1024**3
                logger.info(f"生成后GPU内存使用: {memory_after:.2f}GB")

            if not wavs or len(wavs) == 0:
                raise RuntimeError("音频生成失败，返回空结果")

            # 获取音频数据
            audio_data = wavs[0]

            # 转换为numpy数组
            if isinstance(audio_data, torch.Tensor):
                # 确保张量在CPU上再转换为numpy
                audio_data = audio_data.cpu().numpy()

            # 确保数据类型正确
            if audio_data.dtype != np.float32:
                audio_data = audio_data.astype(np.float32)

            # 标准化音频数据
            audio_data = np.clip(audio_data, -1.0, 1.0)

            logger.info(f"语音生成完成，音频长度: {len(audio_data)} 样本")

            return audio_data, self.default_sample_rate

        except Exception as e:
            logger.error(f"语音生成出错: {str(e)}")
            raise e
    
    def save_audio(
        self,
        audio_data: np.ndarray,
        sample_rate: int,
        output_path: str,
        format: str = "wav"
    ) -> bool:
        """
        保存音频到文件
        
        Args:
            audio_data: 音频数据
            sample_rate: 采样率
            output_path: 输出路径
            format: 音频格式
            
        Returns:
            bool: 保存是否成功
        """
        try:
            # 转换为torch tensor
            if isinstance(audio_data, np.ndarray):
                audio_tensor = torch.from_numpy(audio_data)
            else:
                audio_tensor = audio_data
            
            # 确保有正确的维度
            if audio_tensor.dim() == 1:
                audio_tensor = audio_tensor.unsqueeze(0)  # 添加channel维度
            
            # 保存音频
            torchaudio.save(
                output_path,
                audio_tensor,
                sample_rate,
                format=format
            )
            
            logger.info(f"音频已保存到: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存音频失败: {str(e)}")
            return False
    
    def audio_to_bytes(
        self,
        audio_data: np.ndarray,
        sample_rate: int,
        format: str = "wav"
    ) -> bytes:
        """
        将音频数据转换为字节流
        
        Args:
            audio_data: 音频数据
            sample_rate: 采样率
            format: 音频格式
            
        Returns:
            bytes: 音频字节数据
        """
        try:
            # 创建内存缓冲区
            buffer = io.BytesIO()
            
            # 转换为torch tensor
            if isinstance(audio_data, np.ndarray):
                audio_tensor = torch.from_numpy(audio_data)
            else:
                audio_tensor = audio_data
            
            # 确保有正确的维度
            if audio_tensor.dim() == 1:
                audio_tensor = audio_tensor.unsqueeze(0)
            
            # 保存到缓冲区
            torchaudio.save(
                buffer,
                audio_tensor,
                sample_rate,
                format=format
            )
            
            # 获取字节数据
            audio_bytes = buffer.getvalue()
            buffer.close()
            
            return audio_bytes
            
        except Exception as e:
            logger.error(f"音频转换为字节流失败: {str(e)}")
            raise e
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        获取模型信息
        
        Returns:
            Dict[str, Any]: 模型信息
        """
        return {
            "initialized": self.is_initialized,
            "device": str(self.device) if self.device else None,
            "sample_rate": self.default_sample_rate,
            "model_loaded": self.chat_tts is not None
        }

# 全局TTS实例
tts_core = TTSCore()
