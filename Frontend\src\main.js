import '@fortawesome/fontawesome-free/css/all.min.css'
import './assets/styles/variables.css'

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import mqttClient from './utils/mqtt'
import emitter from './utils/emitter'

const app = createApp(App)

app.use(createPinia())
app.use(router)

// 注册全局事件总线
app.provide('emitter', emitter)

app.mount('#app')

// 初始化MQTT连接
mqttClient.connect()

// 在页面关闭时断开MQTT连接
window.addEventListener('beforeunload', () => {
    mqttClient.disconnect()
})
