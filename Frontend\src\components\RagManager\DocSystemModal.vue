<template>
  <div class="doc-system-modal">
    <div class="modal-panel" style="background: white; color: #333;">
      <div class="panel-header" style="background: #f5f5f5; color: #333; border-bottom: 1px solid #e5e5e5;">
        <h3 style="color: #333;">文档管理</h3>
        <button class="close-btn" @click="$emit('close')" title="关闭" style="color: #333;">
          <i class="fas fa-xmark"></i>
        </button>
      </div>

      <div class="panel-content">
        <nav class="doc-nav" style="background: #f0f2f5; border-bottom: 1px solid #e5e5e5;">
          <a href="#" @click.prevent="currentView = 'documents'" :class="{ active: currentView === 'documents' }" style="color: #333;">文档上传</a>
          <a href="#" @click.prevent="handleViewChange('documentList')" :class="{ active: currentView === 'documentList' }" style="color: #333;">文档列表</a>
          <a href="#" @click.prevent="currentView = 'search'" :class="{ active: currentView === 'search' }" style="color: #333;">文档检索</a>
        </nav>

        <!-- 使用 keep-alive 包裹动态组件 -->
        <keep-alive>
          <component 
            :is="currentComponent" 
            @changeView="handleViewChange"
            :ref="(el) => { if (el?.$el?.className === 'document-list-view') documentListRef = el }"
          />
        </keep-alive>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import DocumentsView from '@/components/RagManager/DocumentsView.vue'
import DocumentListView from '@/components/RagManager/DocumentListView.vue'
import SearchView from '@/components/RagManager/SearchView.vue'

const currentView = ref('documents')
const documentListRef = ref(null)

// 打印当前CSS变量，用于调试
onMounted(() => {
  console.log('DocSystemModal mounted, CSS variables:')
  console.log('--bg:', getComputedStyle(document.documentElement).getPropertyValue('--bg'))
  console.log('--text:', getComputedStyle(document.documentElement).getPropertyValue('--text'))
  console.log('--primary:', getComputedStyle(document.documentElement).getPropertyValue('--primary'))
  
  // 添加自定义CSS变量到文档根元素
  document.documentElement.style.setProperty('--bg', '#ffffff')
  document.documentElement.style.setProperty('--bg-light', '#f6f8fa')
  document.documentElement.style.setProperty('--bg-lighter', '#f3f4f6')
  document.documentElement.style.setProperty('--bg-darker', '#eaeaea')
  document.documentElement.style.setProperty('--text', '#24292e')
  document.documentElement.style.setProperty('--text-secondary', '#586069')
  document.documentElement.style.setProperty('--border', 'rgba(0, 0, 0, 0.1)')
  document.documentElement.style.setProperty('--border-strong', 'rgba(0, 0, 0, 0.15)')
})

const currentComponent = computed(() => {
  switch (currentView.value) {
    case 'documents':
      return DocumentsView
    case 'documentList':
      return DocumentListView
    case 'search':
      return SearchView
    default:
      return DocumentsView
  }
})

// 处理视图切换
const handleViewChange = (view) => {
  currentView.value = view
  // 如果切换到文档列表视图，调用刷新方法
  if (view === 'documentList' && documentListRef.value) {
    documentListRef.value.refreshDocuments()
  }
}

// 定义 emit 事件
defineEmits(['close'])
</script>

<style scoped>
.doc-system-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.3);
  z-index: 2000;
  color: var(--text);
  pointer-events: all;
}

.modal-panel {
  background: var(--bg);
  border-radius: 16px;
  box-shadow: var(--shadow-lg);
  width: 95%;
  height: 95%;
  max-width: 1600px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid var(--border);
}

.panel-header {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--border);
  background: var(--bg-light);
}

.panel-header h3 {
  margin: 0;
  font-size: 1.5em;
  color: var(--text);
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: var(--bg-light);
  color: var(--text);
}

.panel-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.doc-nav {
  padding: 16px 20px;
  display: flex;
  gap: 16px;
  border-bottom: 1px solid var(--border);
  background: var(--bg-light);
}

.doc-nav a {
  color: var(--text-secondary);
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.doc-nav a:hover {
  color: var(--text);
  background: var(--bg-lighter);
}

.doc-nav a.active {
  color: var(--primary);
  background: var(--bg);
  border: 1px solid var(--border);
}

/* 确保组件容器可以滚动 */
:deep(.component-container) {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

@media (max-width: 768px) {
  .modal-panel {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }

  .doc-nav {
    padding: 12px;
    gap: 8px;
  }

  .doc-nav a {
    padding: 6px 12px;
    font-size: 0.9em;
  }
}
</style> 