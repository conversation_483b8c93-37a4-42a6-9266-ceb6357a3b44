import asyncio
import json
from datetime import datetime
from typing import Dict, List, Set
from fastapi import WebSocket, WebSocketDisconnect
from loguru import logger
import time

class WebSocketManager:
    def __init__(self):
        """初始化WebSocket管理器"""
        # 通用连接管理
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_times: Dict[str, float] = {}
        self.message_history: Dict[str, dict] = {}  # 用于存储已处理的消息
        self.last_cleanup = time.time()  # 上次清理时间
        self.cleanup_interval = 60  # 清理间隔（秒）
        self.verbose_logging = False  # 控制是否输出详细日志的标志，默认关闭
        
        # 文档进度更新管理
        self.document_connections: Dict[str, Set[WebSocket]] = {}  # 按文档ID存储连接
        
        logger.info("WebSocket管理器已初始化")
        
    def set_verbose_logging(self, verbose: bool):
        """设置是否输出详细日志"""
        self.verbose_logging = verbose
        
    async def connect(self, websocket: WebSocket):
        """添加新的WebSocket连接"""
        client_host = websocket.client.host
        client_port = websocket.client.port
        connection_id = f"{client_host}:{client_port}"
        
        self.active_connections[connection_id] = websocket
        self.connection_times[connection_id] = time.time()
        
        logger.info(f"[WebSocket连接] 新连接 {connection_id}")
        logger.info(f"[连接总数] 当前活跃连接数: {len(self.active_connections)}")
        
    async def disconnect(self, websocket: WebSocket):
        """移除WebSocket连接"""
        try:
            client_host = websocket.client.host
            client_port = websocket.client.port
            connection_id = f"{client_host}:{client_port}"
            
            if connection_id in self.active_connections:
                del self.active_connections[connection_id]
                if connection_id in self.connection_times:
                    del self.connection_times[connection_id]
                logger.info(f"WebSocket连接已断开，客户端: {connection_id}，剩余连接数: {len(self.active_connections)}")
            else:
                logger.warning(f"尝试断开不存在的WebSocket连接: {connection_id}")
        except Exception as e:
            # 如果参数是connection_id而不是websocket对象
            if isinstance(websocket, str):
                connection_id = websocket
                if connection_id in self.active_connections:
                    del self.active_connections[connection_id]
                    if connection_id in self.connection_times:
                        del self.connection_times[connection_id]
                    logger.info(f"WebSocket连接已断开，客户端ID: {connection_id}，剩余连接数: {len(self.active_connections)}")
                else:
                    logger.warning(f"尝试断开不存在的WebSocket连接ID: {connection_id}")
            else:
                logger.error(f"断开WebSocket连接时出错: {str(e)}")
    
    async def _cleanup_old_connections(self):
        """清理超过30分钟的旧连接"""
        now = time.time()
        
        # 每60秒执行一次清理
        if now - self.last_cleanup < self.cleanup_interval:
            return
        
        self.last_cleanup = now
        stale_connections = []
        connection_age_limit = 1800  # 30分钟
        
        for conn_id, connect_time in list(self.connection_times.items()):
            # 找出超过30分钟的连接
            if now - connect_time > connection_age_limit:
                stale_connections.append(conn_id)
        
        if stale_connections and self.verbose_logging:
            logger.info(f"清理 {len(stale_connections)} 个超时连接（超过30分钟未活动）")
            
        for conn_id in stale_connections:
            if conn_id in self.active_connections:
                try:
                    # 尝试关闭连接
                    websocket = self.active_connections[conn_id]
                    await websocket.close(code=1000, reason="Connection timeout")
                except Exception as e:
                    logger.error(f"关闭超时连接时出错: {str(e)}")
                
                await self.disconnect(conn_id)
        
        # 日志记录当前连接状态
        if self.verbose_logging:
            logger.info(f"定期清理完成，当前活跃连接数: {len(self.active_connections)}")
        
    async def broadcast(self, message: dict):
        """广播消息到所有连接的客户端"""
        if self.verbose_logging:
            logger.info(f"准备广播消息类型: {message.get('type', 'unknown')}")
        
        # 广播消息给所有连接的客户端
        dead_connections = []
        active_count = len(self.active_connections)
        
        if self.verbose_logging:
            logger.info(f"开始向 {active_count} 个连接广播消息")
        
        if active_count == 0:
            if self.verbose_logging:
                logger.warning("没有活跃的WebSocket连接，消息将不会被发送")
            return
        
        for connection_id, websocket in list(self.active_connections.items()):
            try:
                await websocket.send_json(message)
                
                if self.verbose_logging:
                    logger.info(f"消息已发送到客户端: {connection_id}")
                
                # 更新连接最后活动时间
                self.connection_times[connection_id] = time.time()
                
            except Exception as e:
                if self.verbose_logging:
                    logger.error(f"发送消息失败: {str(e)}")
                dead_connections.append(connection_id)
                
        # 移除失效的连接
        for connection_id in dead_connections:
            if connection_id in self.active_connections:
                await self.disconnect(connection_id)
                
        if dead_connections and self.verbose_logging:
            logger.warning(f"已移除 {len(dead_connections)} 个失效连接，剩余连接数: {len(self.active_connections)}")
        
        if self.verbose_logging:
            logger.info(f"消息广播完成")
            
    async def broadcast_progress(self, document_id: str, data: dict):
        """广播进度信息到所有订阅该文档的连接以及所有活跃连接
        
        Args:
            document_id: 文档ID
            data: 要广播的数据
        """
        dead_connections = []
        # 无论是否有订阅，都发送到所有活跃连接
        for connection_id, websocket in list(self.active_connections.items()):
            try:
                await websocket.send_json(data)
                # 即使verbose_logging关闭也记录关键进度更新
                if data.get('progress', 0) < 0:
                    logger.error(f"进度更新错误: {data.get('progress_msg', '未知错误')}")
                elif data.get('progress', 0) >= 0.95:
                    logger.info(f"处理接近完成: {data.get('progress', 0):.2f}, {data.get('progress_msg', '')}")
            except Exception as e:
                logger.error(f"发送进度更新失败: {str(e)}")
                dead_connections.append(connection_id)
        
        # 清理已断开的连接
        for connection_id in dead_connections:
            if connection_id in self.active_connections:
                await self.disconnect(connection_id)
                if document_id in self.document_connections:
                    self.document_connections[document_id].discard(connection_id)
            
        # 如果文档没有订阅者，清理文档连接
        if document_id in self.document_connections and not self.document_connections[document_id]:
            del self.document_connections[document_id]
            if self.verbose_logging:
                logger.info(f"文档 {document_id} 不再有活跃订阅者，已移除")
            
    async def handle_confirmation(self, message: dict):
        """处理确认消息"""
        if self.verbose_logging:
            logger.info(f"收到确认消息: {message}")
        # 这里可以添加确认消息的处理逻辑，比如更新数据库等

    async def broadcast_alerts(self, alerts: List[dict]):
        """广播警报消息给所有连接的客户端"""
        if not alerts:
            return
            
        # 如果启用了详细日志，才记录详细信息
        if self.verbose_logging:
            # logger.info(f"[警报广播] 开始广播 {len(alerts)} 个警报")
            # 记录警报类型分布
            alert_types = {}
            for alert in alerts:
                alert_type = alert.get('type', 'unknown')
                if alert_type in alert_types:
                    alert_types[alert_type] += 1
                else:
                    alert_types[alert_type] = 1
            # logger.info(f"[警报分布] 警报类型统计: {alert_types}")
        
        # 如果没有活跃连接，记录一下并直接返回
        active_count = len(self.active_connections)
        if active_count == 0:
            logger.warning("[警报广播] 没有活跃的WebSocket连接，警报将不会被发送")
            return
        
        # 合并同一设备的警报
        merged_alerts = self._merge_device_alerts(alerts)
        
        if self.verbose_logging:
            # logger.info(f"[警报合并] 原始警报数 {len(alerts)}, 合并后警报数 {len(merged_alerts)}")
            pass
            
        # 广播警报
        disconnected_clients = []
        successful_clients = 0
        
        for connection_id, websocket in list(self.active_connections.items()):
            try:
                for alert in merged_alerts:
                    await websocket.send_json(alert)
                successful_clients += 1
                # 更新连接时间
                self.connection_times[connection_id] = time.time()
            except Exception as e:
                logger.error(f"[发送失败] 向客户端 {connection_id} 发送警报时出错: {str(e)}")
                disconnected_clients.append(connection_id)
        
        # 清理断开的连接
        for connection_id in disconnected_clients:
            if connection_id in self.active_connections:
                await self.disconnect(connection_id)
        
        # 只记录一次汇总信息
        if disconnected_clients:
            logger.warning(f"[连接失败] {len(disconnected_clients)} 个客户端连接失败并已移除")
        # logger.info(f"[广播完成] 警报成功发送到 {successful_clients} 个客户端")
    
    def _merge_device_alerts(self, alerts: List[dict]) -> List[dict]:
        """合并同一设备的多个警报，同时考虑警报类型
        
        Args:
            alerts: 原始警报列表
            
        Returns:
            合并后的警报列表
        """
        if not alerts:
            return []
            
        # 按设备ID和警报类型分组警报
        devices_alerts = {}
        
        for alert in alerts:
            device = alert.get('device', {})
            device_id = device.get('id')
            
            if not device_id:
                # 如果没有设备ID，保持不变
                continue
            
            # 确定警报类型 - 首先明确判断is_realtime标记
            is_realtime = None
            if alert.get('is_realtime') is not None:
                is_realtime = bool(alert.get('is_realtime'))
            else:
                # 如果没有明确标记，通过其他信息推断
                if alert.get('id', '').startswith('inspection-') or 'inspection' in alert or alert.get('content', '').find('[定时巡检') >= 0:
                    is_realtime = False
                elif alert.get('id', '').startswith('realtime-') or alert.get('content', '').startswith('接收警报'):
                    is_realtime = True
                else:
                    # 默认为接收警报
                    is_realtime = True
                    
                # 记录类型推断，方便调试
                if self.verbose_logging:
                    logger.info(f"[警报合并] 警报ID:{alert.get('id')}没有明确标记，推断为: {'接收警报' if is_realtime else '巡检警报'}")
            
            # 使用复合键(设备ID, 警报类型)作为分组依据
            group_key = (device_id, is_realtime)
            
            if group_key not in devices_alerts:
                # 创建设备的警报组
                devices_alerts[group_key] = {
                    'device': device,
                    'alerts': [],
                    'timestamp': alert.get('timestamp'),
                    'check_date': device.get('check_date'),
                    'is_realtime': is_realtime,
                    'inspection': alert.get('inspection'),
                    'realtime': alert.get('realtime')
                }
                
            # 添加到该设备的警报集合
            devices_alerts[group_key]['alerts'].append({
                'type': alert.get('type'),
                'title': alert.get('title'),
                'content': alert.get('content'),
                'alertType': alert.get('alertType', 'warning'),
                'id': alert.get('id')
            })
            
        # 创建合并后的警报列表
        merged_list = []
        
        # 处理没有设备ID的警报
        for alert in alerts:
            device = alert.get('device', {})
            device_id = device.get('id')
            
            if not device_id:
                merged_list.append(alert)
        
        # 创建每个设备的合并警报
        for (device_id, is_realtime), device_data in devices_alerts.items():
            if len(device_data['alerts']) == 1:
                # 如果该设备只有一个指定类型的警报，不需要合并
                for alert in alerts:
                    device = alert.get('device', {})
                    alert_is_realtime = alert.get('is_realtime')
                    if device.get('id') == device_id and alert_is_realtime == is_realtime:
                        merged_list.append(alert)
                        break
            else:
                # 创建合并后的警报标题和内容
                title = f"{device_id}设备多项警报"
                
                # 确保警报ID格式正确，避免前端误判
                alert_id_prefix = 'realtime' if is_realtime else 'inspection'
                
                # 根据类型设置内容前缀
                if is_realtime:
                    content = f"接收警报 {device_id}设备存在{len(device_data['alerts'])}项异常"
                else:
                    content = f"[定时巡检] {device_id}设备存在{len(device_data['alerts'])}项异常"
                
                # 创建合并后的警报
                merged_alert = {
                    'type': 'merged_alert',
                    'title': title,
                    'content': content,
                    'alertType': 'warning',  # 使用警告级别，后续根据子警报调整
                    'timestamp': device_data['timestamp'],
                    'id': f"{alert_id_prefix}-merged-{device_id}-{int(time.time() * 1000)}",
                    'device': {
                        **device_data['device'],
                        'check_date': device_data['check_date']  # 确保数据时间被保留
                    },
                    'subAlerts': device_data['alerts'],
                    'is_realtime': is_realtime
                }
                
                # 添加相应的元数据
                if not is_realtime and device_data['inspection']:
                    merged_alert['inspection'] = device_data['inspection']
                
                if is_realtime and device_data['realtime']:
                    merged_alert['realtime'] = device_data['realtime']
                
                # 确定合并警报的严重级别
                for alert_item in device_data['alerts']:
                    if alert_item.get('alertType') == 'danger':
                        merged_alert['alertType'] = 'danger'
                        break
                
                merged_list.append(merged_alert)
        
        return merged_list
    
    def get_active_connections(self) -> List[str]:
        """获取所有活跃连接的客户端ID列表"""
        return list(self.active_connections.keys())
    
    def get_connection_count(self) -> int:
        """获取当前活跃连接数"""
        return len(self.active_connections)

# 创建全局WebSocket管理器实例
manager = WebSocketManager() 