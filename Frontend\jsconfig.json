{"compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": false, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom"], "types": ["vite/client"]}, "include": ["src/**/*.js", "src/**/*.jsx", "src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules", "dist"]}