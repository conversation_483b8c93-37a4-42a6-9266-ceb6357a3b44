version: "3.8"

services:
  be-server:
    container_name: ag-python
    image: python:3.12
    ports:
      - "8010:8000"  # FastAPI 默认使用 8000 端口
    privileged: true  # 如需访问硬件资源（如GPU）可保留，否则建议移除
    volumes:
      - ./pyagent:/app  # 挂载本地代码到容器
    environment:
      - PYTHONUNBUFFERED=1       # 实时日志输出
      - DEBUG=1                  # 开发环境启用调试模式
    command: 
      - uvicorn                   # 使用 ASGI 服务器运行 FastAPI
      - main:app
      - --host
      - 0.0.0.0
      - --port
      - "8000"
      - --reload                 # 开发时启用代码热重载
    networks:
      agent-network:
        ipv4_address: ***********

  ag-mysql:
    image: mysql:8
    container_name: ag-mysql
    ports:
      - 8011:3306
    privileged: true
    volumes:
      - ./mysql/data:/var/lib/mysql
      - ./mysql/mysql.cnf:/etc/mysql/conf.d/mysql.cnf
      - ./mysql/initdb:/docker-entrypoint-initdb.d
    environment:
      MYSQL_DATABASE: knowledge_base
      MYSQL_ROOT_PASSWORD: 1qazXSW@
    command:
      [
        'mysqld',
        '--character-set-server=utf8',
        '--collation-server=utf8_unicode_ci',
        '--default-time-zone=+8:00',
        '--lower-case-table-names=1'
      ]
    networks:
      agent-network:
        ipv4_address: ***********

  ag-emqx:
    image: emqx:5.1
    container_name: ag-emqx
    ports:
      - 8012:1884
      - 8013:8083
      - 8014:8084
      - 8015:18083
    privileged: true
    volumes:
      - /etc/localtime:/etc/localtime
      - ./emqx/data:/opt/emqx/data
      - ./emqx/log:/opt/emqx/log
    environment:
      SET_CONTAINER_TIMEZONE: "true"
      CONTAINER_TIMEZONE: Asia/Shanghai
    networks:
      agent-network:
        ipv4_address: ***********

  ag-m-etcd:
    container_name: ag-m-etcd
    image: quay.io/coreos/etcd:v3.5.16
    environment:
      - ETCD_AUTO_COMPACTION_MODE=revision
      - ETCD_AUTO_COMPACTION_RETENTION=1000
      - ETCD_QUOTA_BACKEND_BYTES=**********
      - ETCD_SNAPSHOT_COUNT=50000
    volumes:
      - ./milvus/etcd:/etcd
    command: etcd -advertise-client-urls=http://127.0.0.1:2379 -listen-client-urls http://0.0.0.0:2379 --data-dir /etcd
    healthcheck:
      test: ["CMD", "etcdctl", "endpoint", "health"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      agent-network:
        ipv4_address: ***********

  ag-m-minio:
    container_name: ag-m-minio
    image: minio/minio:RELEASE.2023-03-20T20-16-18Z
    environment:
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin
    ports:
      - "8016:9001"
      - "8017:9000"
    volumes:
      - ./milvus/minio:/minio_data
    command: minio server /minio_data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      agent-network:
        ipv4_address: ***********

  ag-m-milvus-s:
    container_name: ag-milvus-s
    image: milvusdb/milvus:v2.5.4
    command: ["milvus", "run", "standalone"]
    security_opt:
    - seccomp:unconfined
    environment:
      ETCD_ENDPOINTS: ***********:2379
      MINIO_ADDRESS: ***********:9000
    volumes:
      - ./milvus/data:/var/lib/milvus
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9091/healthz"]
      interval: 30s
      start_period: 90s
      timeout: 20s
      retries: 3
    ports:
      - "8018:19530"
      - "8019:9091"
    depends_on:
      - "ag-m-etcd"
      - "ag-m-minio"
    networks:
      agent-network:
        ipv4_address: ***********

  ag-ollama:
    restart: always
    container_name: ag-ollama
    image: ollama/ollama
    privileged: true  # 如需访问硬件资源（如GPU）可保留，否则建议移除
    environment:
    - OLLAMA_HOST=0.0.0.0:8000
    - OLLAMA_MODELS=/data/models
    volumes:
    - ./ollama/models/:/data/models
    command: serve
    networks:
      agent-network:
        ipv4_address: ***********

  ag-nginx:
    image: nginx:stable
    container_name: ag-nginx
    ports:
      - 8020:80
    privileged: true
    depends_on:
      - be-server
    volumes:
      - ./nginx/unity:/usr/share/nginx/unity
      - ./nginx/vue:/usr/share/nginx/vue
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/log:/var/log/nginx
    networks:
      agent-network:
        ipv4_address: ***********

networks:
  agent-network:
    ipam:
      driver: default
      config:
        - subnet: '**********/16'
