# LangGraph MCP SSE API服务

这是一个基于FastAPI的API服务，将LangGraph与MCP工具结合，提供兼容OpenAI格式的聊天接口，支持SSE（Server-Sent Events）流式响应。

## 功能特点

- **兼容OpenAI接口**：API接口与OpenAI格式兼容，便于与现有前端集成
- **流式响应**：支持SSE格式的流式响应，提供实时交互体验
- **MCP工具集成**：集成数学、天气和Redmine工具服务
- **LangGraph代理**：使用LangGraph的ReAct代理进行决策和工具调用

## 安装与依赖

1. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. Redmine功能依赖（可选）：
   ```bash
   python install_redmine_deps.py
   ```

## 使用方法

1. 启动服务：
   ```bash
   python custom_interactive_client_sse.py
   ```

2. 服务将在 http://localhost:8000 上运行

## API接口

### 聊天完成接口

- **URL**: `/api/v1/chats_openai/{chat_id}/chat/completions`
- **方法**: POST
- **请求体**:
  ```json
  {
    "model": "qwen2.5:32b",
    "messages": [
      {"role": "user", "content": "用户消息"},
      {"role": "assistant", "content": "助手回复"}
    ],
    "stream": true
  }
  ```
- **响应**: 
  - 流式响应格式为SSE（text/event-stream）
  - 每个事件格式为：`data: {"choices":[{"delta":{"content":"内容片段"},"finish_reason":null,"index":0}]}\n\n`
  - 结束标记为：`data: [DONE]\n\n`

### 工具列表接口

- **URL**: `/api/tools`
- **方法**: GET
- **响应**: 返回所有可用工具的列表及其描述

## 与前端集成

该API服务完全兼容IOT Agent前端的聊天接口，可以直接替换后端API：

1. 前端聊天系统配置:
   ```javascript
   const apiUrl = 'http://localhost:8000/api/v1/chats_openai'
   const chatId = 'c1a83f6f475c11f0bbc3345a603cb29c' // 可以使用任意chat_id
   const apiKey = 'sk-no-key-required' // 本地服务不需要验证
   ```

2. 请求示例:
   ```javascript
   const response = await fetch(`${apiUrl}/${chatId}/chat/completions`, {
     method: 'POST',
     headers: {
       'Content-Type': 'application/json',
       'Authorization': `Bearer ${apiKey}`
     },
     body: JSON.stringify({
       model: 'qwen2.5-7b-instruct-1m',
       messages: messageHistory,
       stream: true
     })
   })
   ```

3. 处理流式响应:
   ```javascript
   const reader = response.body.getReader()
   const decoder = new TextDecoder('utf-8')
   
   while (true) {
     const { done, value } = await reader.read()
     if (done) break
     
     const chunk = decoder.decode(value, { stream: true })
     // 处理接收到的数据块，解析SSE格式
   }
   ```

## 模型与工具

- **模型**: qwen2.5-7b-instruct-1m（通过LM Studio本地访问）
- **工具**:
  - 数学工具: add, multiply
  - 天气工具: get_weather
  - Redmine工具: 查询、创建问题等（需安装依赖） 