# ChatTTS 文本转语音服务

## 概述

本项目为IOT_Agent_MVP2系统集成了基于ChatTTS的中文文本转语音功能。该服务提供了完整的后台API和前端集成，支持实时语音合成和播放。

## 项目结构

```
tts/
├── venv/                    # Python虚拟环境
├── logs/                    # 日志文件目录
├── tts_service.py          # 主服务文件
├── tts_core.py             # TTS核心功能模块
├── requirements.txt        # Python依赖包
├── .env                    # 环境配置文件
├── start_tts_service.bat   # Windows启动脚本
├── test_tts.py            # 测试脚本
├── frontend_integration.js # 前端集成示例
├── API_DOCUMENTATION.md   # API文档
└── README.md              # 本文件
```

## 功能特性

### 后台服务
- ✅ 基于FastAPI的RESTful API服务
- ✅ 支持中文文本转语音
- ✅ 提供JSON和流式两种响应格式
- ✅ 支持音色控制和生成参数调节
- ✅ 完整的错误处理和日志记录
- ✅ 健康检查和服务状态监控

### 前端集成
- ✅ Vue.js组件集成
- ✅ ChatPanel语音播放功能
- ✅ TTS开关控制
- ✅ 音频播放状态管理
- ✅ 自动文本清理和优化

### API接口
- `GET /` - 服务信息
- `GET /health` - 健康检查
- `POST /tts` - 文本转语音（JSON响应）
- `POST /tts/stream` - 文本转语音（音频流响应）

## 安装和部署

### 1. 环境准备

确保系统已安装：
- Python 3.8+
- Git

### 2. 创建虚拟环境

```bash
cd C:\AI\IOT_Agent_MVP2\tts
python -m venv venv
```

### 3. 激活虚拟环境

Windows:
```bash
venv\Scripts\activate
```

Linux/Mac:
```bash
source venv/bin/activate
```

### 4. 安装依赖

```bash
# 安装PyTorch (CPU版本)
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu

# 安装其他依赖
pip install fastapi uvicorn numpy scipy librosa soundfile pydub requests aiofiles python-dotenv loguru python-multipart

# 安装ChatTTS
pip install git+https://github.com/2noise/ChatTTS.git
```

### 5. 启动服务

Windows:
```bash
start_tts_service.bat
```

手动启动:
```bash
python tts_service.py
```

服务将在 `http://localhost:8001` 启动。

## 使用方法

### 1. 测试服务

运行测试脚本：
```bash
python test_tts.py
```

### 2. API调用示例

#### Python示例
```python
import requests
import base64

# 文本转语音
response = requests.post('http://localhost:8001/tts', json={
    'text': '你好，这是一个测试语音。',
    'voice_seed': 42,
    'temperature': 0.3
})

if response.status_code == 200:
    result = response.json()
    audio_bytes = base64.b64decode(result['audio_base64'])
    with open('output.wav', 'wb') as f:
        f.write(audio_bytes)
```

#### JavaScript示例
```javascript
// 在前端调用TTS
async function playText(text) {
    const response = await fetch('http://localhost:8001/tts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            text: text,
            voice_seed: 42,
            temperature: 0.3
        })
    });
    
    const result = await response.json();
    const audio = new Audio(`data:audio/wav;base64,${result.audio_base64}`);
    await audio.play();
}
```

### 3. 前端集成

在ChatPanel.vue中已集成TTS功能：
- 点击头部的音量图标可开启/关闭TTS
- 助手消息旁会显示播放按钮
- 点击播放按钮可听取语音

## 配置说明

### 环境变量 (.env)

```env
# 服务配置
TTS_HOST=0.0.0.0
TTS_PORT=8001
TTS_DEBUG=False

# 模型配置
TTS_DEVICE=auto  # auto, cpu, cuda
TTS_COMPILE=False

# 音频配置
TTS_SAMPLE_RATE=24000
TTS_DEFAULT_TEMPERATURE=0.3
TTS_DEFAULT_TOP_P=0.7
TTS_DEFAULT_TOP_K=20
```

### API参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| text | string | 必需 | 要转换的文本 |
| voice_seed | int | null | 声音种子，控制音色 |
| temperature | float | 0.3 | 生成温度，控制随机性 |
| top_p | float | 0.7 | 核采样参数 |
| top_k | int | 20 | Top-K采样参数 |
| refine_text | bool | true | 是否优化文本 |

## 性能优化

### 1. GPU加速

如果有NVIDIA GPU，可以安装CUDA版本的PyTorch：
```bash
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cu118
```

### 2. 模型编译

在生产环境中可以启用模型编译以提高性能：
```python
# 在tts_core.py中设置
success = tts_core.initialize(device="cuda", compile_model=True)
```

### 3. 并发处理

服务支持多个并发请求，但受硬件性能限制。建议：
- CPU: 2-4个并发请求
- GPU: 4-8个并发请求

## 故障排除

### 常见问题

1. **模型加载失败**
   - 检查网络连接，首次运行需要下载模型
   - 确保有足够的磁盘空间（约2-4GB）

2. **内存不足**
   - 模型需要2-4GB内存
   - 考虑使用更小的模型或增加系统内存

3. **音频播放问题**
   - 检查浏览器是否支持Web Audio API
   - 确保音频格式兼容性

4. **CORS错误**
   - 确保TTS服务的CORS配置正确
   - 检查前端请求的域名和端口

### 日志查看

服务日志保存在 `logs/tts_service.log`：
```bash
tail -f logs/tts_service.log
```

## 开发说明

### 添加新功能

1. **扩展API接口**：在 `tts_service.py` 中添加新的路由
2. **修改核心功能**：在 `tts_core.py` 中扩展TTS功能
3. **前端集成**：在 `ChatPanel.vue` 中添加新的UI组件

### 测试

运行完整测试：
```bash
python test_tts.py
```

### 部署

生产环境部署建议：
1. 使用Gunicorn或uWSGI作为WSGI服务器
2. 配置Nginx作为反向代理
3. 使用Docker容器化部署
4. 配置监控和日志收集

## 许可证

本项目基于原IOT_Agent_MVP2项目的许可证。ChatTTS模型遵循其原始许可证。

## 贡献

欢迎提交Issue和Pull Request来改进本项目。

## 更新日志

### v1.0.0 (2025-01-14)
- ✅ 初始版本发布
- ✅ 基础TTS功能实现
- ✅ 前端集成完成
- ✅ API文档编写
- ✅ 测试脚本完成
