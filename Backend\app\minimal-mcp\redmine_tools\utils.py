#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from typing import Dict, Any
from datetime import datetime
import requests
import re  # 添加re库的导入，因为需要使用正则表达式
from .redmine_config import redmine_config

logger = logging.getLogger(__name__)

async def get_version() -> Dict[str, Any]:
    """
    获取Redmine版本信息
    
    返回当前连接的Redmine服务器版本信息。
    """
    try:
        redmine = redmine_config.get_client()
        
        # 尝试获取Redmine版本信息
        version_info = {
            "version": "未知",
            "api_version": "根据功能推断",
            "ruby_version": "未知",
            "plugins": []
        }
        
        # 1. 尝试通过API响应头获取版本信息
        try:
            response = redmine._requests.get(
                f"{redmine.url}/",
                headers=redmine._create_headers(),
                **redmine._requests_args
            )
            
            # 从响应头中获取版本信息
            if 'X-Redmine-Version' in response.headers:
                version_info["version"] = response.headers.get('X-Redmine-Version')
                logger.info(f"从响应头获取到Redmine版本: {version_info['version']}")
            if 'X-Runtime' in response.headers:
                version_info["ruby_version"] = response.headers.get('X-Runtime')
        except Exception as e:
            logger.warning(f"通过API头获取版本信息失败: {str(e)}")
        
        # 2. 如果版本仍未知，尝试通过管理员API获取版本信息（如果有管理员权限）
        if version_info["version"] == "未知":
            try:
                headers = {}
                if hasattr(redmine, 'key'):
                    headers['X-Redmine-API-Key'] = redmine.key
                
                response = requests.get(
                    f"{redmine.url}/admin/info",
                    headers=headers,
                    verify=redmine._requests_args.get('verify', False),
                    allow_redirects=True
                )
                if response.status_code == 200:
                    # 这个页面包含版本信息，但需要解析HTML
                    version_pattern = r'Redmine\s+version\s*[:<].*?(\d+\.\d+\.\d+)'
                    match = re.search(version_pattern, response.text)
                    if match:
                        version_info["version"] = match.group(1)
                        logger.info(f"从管理员界面获取到Redmine版本: {version_info['version']}")
            except Exception as e:
                logger.warning(f"通过管理员API获取版本信息失败: {str(e)}")
        
        # 3. 如果版本仍未知，尝试通过HTML页面解析获取版本信息
        if version_info["version"] == "未知":
            try:
                # 直接获取Redmine首页
                response = requests.get(
                    redmine.url,
                    verify=redmine._requests_args.get('verify', False),
                    allow_redirects=True
                )
                
                if response.status_code == 200:
                    # 搜索版本信息的几种模式
                    
                    # 在页脚查找版本信息
                    footer_pattern = r'Powered by <a[^>]*>Redmine</a> &copy;[^,]*, ([\d\.]+)'
                    match = re.search(footer_pattern, response.text)
                    if match:
                        version_info["version"] = match.group(1)
                        logger.info(f"通过HTML页面找到Redmine版本: {version_info['version']}")
                    
                    # 查找Ruby版本信息(在某些安装中可见)
                    ruby_pattern = r'Ruby[^\d]*([\d\.p]+)'
                    match = re.search(ruby_pattern, response.text)
                    if match:
                        version_info["ruby_version"] = match.group(1)
                    
                    # 查找安装的插件
                    plugin_pattern = r'<a[^>]*>([^<]+)</a>\s*plugin'
                    plugins = re.findall(plugin_pattern, response.text)
                    if plugins:
                        version_info["plugins"] = plugins
                    
                    # 在最新版本区域查找版本信息
                    latest_releases_pattern = r'Latest releases.*?(\d+\.\d+\.\d+)\s*\((.*?)\)'
                    latest_releases = re.findall(latest_releases_pattern, response.text, re.DOTALL)
                    if latest_releases:
                        version_info["latest_releases"] = [
                            {"version": release[0], "date": release[1].strip()} 
                            for release in latest_releases
                        ]
            except Exception as e:
                logger.warning(f"通过HTML解析获取版本信息失败: {str(e)}")
        
        # 4. 如果版本仍未知，尝试通过my/account页面获取信息
        if version_info["version"] == "未知":
            try:
                headers = {}
                if hasattr(redmine, 'key'):
                    headers['X-Redmine-API-Key'] = redmine.key
                
                response = requests.get(
                    f"{redmine.url}/my/account",
                    headers=headers,
                    verify=redmine._requests_args.get('verify', False),
                    allow_redirects=True
                )
                
                if response.status_code == 200:
                    # 在页脚查找版本信息
                    footer_pattern = r'Powered by <a[^>]*>Redmine</a> &copy;[^,]*, ([\d\.]+)'
                    match = re.search(footer_pattern, response.text)
                    if match:
                        version_info["version"] = match.group(1)
                        logger.info(f"通过my/account页面找到Redmine版本: {version_info['version']}")
            except Exception as e:
                logger.warning(f"通过my/account获取版本信息失败: {str(e)}")
        
        # 5. 通过检测API功能推断版本
        # 测试功能可用性
        features = {}
        
        # 测试issue relations API (Redmine 1.1+)
        try:
            redmine.issue_relation.all(issue_id=1, limit=1)
            features["issue_relations_api"] = True
        except Exception:
            features["issue_relations_api"] = False
            
        # 测试versions API (Redmine 1.3+)
        try:
            redmine.version.all(project_id=1, limit=1)
            features["versions_api"] = True
        except Exception:
            features["versions_api"] = False
            
        # 测试issue_statuses API (Redmine 1.3+)
        try:
            redmine.issue_status.all(limit=1)
            features["issue_statuses_api"] = True
        except Exception:
            features["issue_statuses_api"] = False
            
        # 测试wiki_pages API (Redmine 2.2+)
        try:
            redmine.wiki_page.all(project_id="test", limit=1)
            features["wiki_pages_api"] = True
        except Exception:
            features["wiki_pages_api"] = False
            
        # 添加功能检测结果
        version_info["detected_features"] = features
        
        # 根据功能推断版本
        if version_info["version"] == "未知":
            if features.get("wiki_pages_api", False):
                version_info["estimated_version"] = "2.2+"
            elif features.get("issue_statuses_api", False):
                version_info["estimated_version"] = "1.3+"
            elif features.get("issue_relations_api", False):
                version_info["estimated_version"] = "1.1+"
            else:
                version_info["estimated_version"] = "1.0+"
        
        return {
            "status": "success",
            "info": version_info,
            "library_version": "python-redmine 2.4.0",
            "url": redmine_config.url
        }
        
    except Exception as e:
        logger.error(f"获取Redmine版本信息失败: {str(e)}")
        return {
            "status": "error", 
            "message": str(e),
            "info": {
                "version": "未知",
                "api_version": "未知",
                "error": str(e)
            }
        }

async def search_redmine(query: str, titles_only: bool = False, limit: int = 10) -> Dict[str, Any]:
    """
    在Redmine中搜索
    
    使用Redmine的搜索API进行全文搜索。
    如果标准搜索API不可用，会使用备选搜索方法。
    """
    try:
        redmine = redmine_config.get_client()
        formatted_results = []
        
        try:
            # 尝试使用标准搜索API
            results = redmine.search.search(
                query,
                titles_only=titles_only,
                limit=limit
            )
            
            # 转换为可序列化的字典列表
            for result in results:
                data = {
                    "id": getattr(result, 'id', 0),
                    "title": getattr(result, 'title', ''),
                    "type": getattr(result, 'type', ''),
                    "url": getattr(result, 'url', ''),
                    "description": getattr(result, 'description', '')
                }
                
                # 根据类型添加额外信息
                if hasattr(result, 'datetime'):
                    data["datetime"] = result.datetime.strftime("%Y-%m-%d %H:%M:%S") if result.datetime else ''
                
                formatted_results.append(data)
                
        except AttributeError as e:
            # 标准搜索API不可用，使用备选搜索方法（直接HTTP请求）
            logger.info(f"标准搜索API不可用，使用备选搜索方法: {str(e)}")
            
            # 构建搜索URL
            search_params = {
                'q': query,
                'titles_only': '1' if titles_only else '0',
                'limit': str(limit),
                'format': 'json'  # 尝试获取JSON格式响应
            }
            
            # 创建请求头
            headers = redmine._create_headers()
            
            # 发送搜索请求
            response = requests.get(
                f"{redmine.url}/search",
                params=search_params,
                headers=headers,
                verify=redmine._requests_args.get('verify', False)
            )
            
            if response.status_code == 200:
                try:
                    # 尝试解析JSON响应
                    json_data = response.json()
                    if 'results' in json_data:
                        for item in json_data['results']:
                            formatted_results.append({
                                "id": item.get('id', 0),
                                "title": item.get('title', ''),
                                "type": item.get('type', ''),
                                "url": item.get('url', ''),
                                "description": item.get('description', ''),
                                "datetime": item.get('datetime', '')
                            })
                except Exception:
                    # JSON解析失败，尝试从HTML响应中提取信息
                    logger.info("JSON解析失败，尝试从HTML响应中提取搜索结果")
                    
                    # 提取搜索结果
                    result_pattern = r'<dt class="([^"]+)"[^>]*>.*?<a href="([^"]+)"[^>]*>([^<]+)</a>.*?</dt>\s*<dd[^>]*>(.*?)</dd>'
                    matches = re.findall(result_pattern, response.text, re.DOTALL)
                    
                    for match in matches[:limit]:
                        result_type, url, title, description = match
                        
                        # 清理HTML标签
                        description = re.sub(r'<[^>]+>', ' ', description).strip()
                        
                        # 提取ID
                        id_match = re.search(r'/(\d+)(?:\?|$)', url)
                        result_id = int(id_match.group(1)) if id_match else 0
                        
                        formatted_results.append({
                            "id": result_id,
                            "title": title,
                            "type": result_type,
                            "url": f"{redmine.url.rstrip('/')}{url}",
                            "description": description
                        })
            else:
                logger.warning(f"搜索请求失败，状态码: {response.status_code}")
                # 返回空结果集但不抛出异常
                
        return {
            "results": formatted_results,
            "count": len(formatted_results),
            "query": query
        }
        
    except Exception as e:
        logger.error(f"Redmine搜索失败: {str(e)}")
        # 返回空结果集而不是抛出异常
        return {
            "results": [],
            "count": 0,
            "query": query,
            "error": str(e)
        }

async def get_enumerations(enumeration_type: str = "issue_priorities") -> Dict[str, Any]:
    """
    获取Redmine枚举类型数据
    
    获取指定枚举类型的所有值。支持的类型包括：issue_priorities, time_entry_activities, document_categories
    """
    try:
        redmine = redmine_config.get_client()
        
        # 验证枚举类型是否有效
        valid_types = ["issue_priorities", "time_entry_activities", "document_categories"]
        if enumeration_type not in valid_types:
            return {
                "status": "error",
                "message": f"无效的枚举类型: {enumeration_type}。有效类型为: {', '.join(valid_types)}"
            }
        
        # 获取枚举数据
        enumerations = list(redmine.enumeration.filter(resource=enumeration_type))
        
        # 转换为可序列化的字典列表
        result = []
        
        for enum in enumerations:
            data = {
                "id": getattr(enum, 'id', 0),
                "name": getattr(enum, 'name', '未知'),
                "is_default": getattr(enum, 'is_default', False),
                "active": getattr(enum, 'active', True)
            }
            
            # 添加其他可能的属性
            if hasattr(enum, 'position'):
                data["position"] = enum.position
                
            result.append(data)
        
        return {
            "enumerations": result,
            "count": len(result),
            "type": enumeration_type
        }
        
    except Exception as e:
        logger.error(f"获取Redmine枚举数据失败: {str(e)}")
        raise Exception(f"获取Redmine枚举数据失败: {str(e)}")

async def get_custom_fields() -> Dict[str, Any]:
    """
    获取Redmine自定义字段
    
    获取系统中定义的所有自定义字段。
    """
    try:
        redmine = redmine_config.get_client()
        
        # 获取所有自定义字段
        custom_fields = list(redmine.custom_field.all())
        
        # 转换为可序列化的字典列表
        result = []
        
        for cf in custom_fields:
            data = {
                "id": getattr(cf, 'id', 0),
                "name": getattr(cf, 'name', '未知'),
                "customized_type": getattr(cf, 'customized_type', ''),
                "field_format": getattr(cf, 'field_format', ''),
                "regexp": getattr(cf, 'regexp', ''),
                "min_length": getattr(cf, 'min_length', None),
                "max_length": getattr(cf, 'max_length', None),
                "is_required": getattr(cf, 'is_required', False),
                "is_filter": getattr(cf, 'is_filter', False),
                "searchable": getattr(cf, 'searchable', False),
                "multiple": getattr(cf, 'multiple', False),
                "default_value": getattr(cf, 'default_value', None),
                "visible": getattr(cf, 'visible', True),
            }
            
            # 处理可能的枚举值
            if hasattr(cf, 'possible_values') and cf.possible_values:
                try:
                    data["possible_values"] = [v for v in cf.possible_values]
                except:
                    # 如果不是可迭代的，直接赋值
                    data["possible_values"] = str(cf.possible_values)
            
            # 处理跟踪器IDs
            if hasattr(cf, 'trackers') and cf.trackers:
                try:
                    data["trackers"] = [{"id": t.id, "name": t.name} for t in cf.trackers]
                except:
                    data["trackers"] = []
                
            result.append(data)
        
        return {
            "custom_fields": result,
            "count": len(result)
        }
        
    except Exception as e:
        logger.error(f"获取Redmine自定义字段失败: {str(e)}")
        raise Exception(f"获取Redmine自定义字段失败: {str(e)}") 