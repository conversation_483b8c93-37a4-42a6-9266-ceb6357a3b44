"""
增强的日志配置模块
"""

import os
import logging
import sys
from logging.handlers import RotatingFileHandler
from pathlib import Path

def setup_logging(level=logging.ERROR, log_file=None, max_size=10*1024*1024, backup_count=5):
    """
    设置应用的日志系统
    
    Args:
        level: 日志级别，默认为ERROR
        log_file: 日志文件路径，默认为None (仅控制台输出)
        max_size: 日志文件最大大小，默认为10MB
        backup_count: 日志文件备份数量，默认为5
    """
    # 创建日志格式
    log_format = logging.Formatter(
        '%(asctime)s %(levelname)-8s %(name)-15s %(message)s',
        '%Y-%m-%d %H:%M:%S'
    )
    
    # 获取根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(level)
    
    # 添加控制台处理器
    console = logging.StreamHandler(sys.stdout)
    console.setFormatter(log_format)
    root_logger.addHandler(console)
    
    # 添加文件处理器(如果指定了日志文件)
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            
        # 创建滚动文件处理器
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=max_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(log_format)
        root_logger.addHandler(file_handler)

def get_logger(name, level=None):
    """
    获取命名的日志记录器
    
    Args:
        name: 日志记录器名称
        level: 可选的日志级别
        
    Returns:
        Logger: 配置的日志记录器
    """
    logger = logging.getLogger(name)
    if level is not None:
        logger.setLevel(level)
    return logger

class LogLevelFilter:
    """过滤特定级别范围的日志的过滤器"""
    
    def __init__(self, min_level=logging.DEBUG, max_level=logging.CRITICAL):
        """
        初始化日志过滤器
        
        Args:
            min_level: 最小日志级别（包含）
            max_level: 最大日志级别（包含）
        """
        self.min_level = min_level
        self.max_level = max_level
        
    def filter(self, record):
        """过滤日志记录"""
        return self.min_level <= record.levelno <= self.max_level 