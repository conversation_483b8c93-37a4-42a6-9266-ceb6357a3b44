<template>
  <div class="scene-dock">
    <div 
      v-for="scene in scenes" 
      :key="scene.id"
      class="dock-item"
      :class="{ active: currentScene === scene.id }"
      @click="$emit('sceneChange', scene.id)"
    >
      <div class="scene-icon">{{ scene.icon }}</div>
      <div class="scene-name">{{ scene.name }}</div>
    </div>
  </div>
  
  <!-- 设备定位通知 -->
  <div v-if="showNotification" class="device-notification" :class="{ 'fade-out': isNotificationFading }">
    <div class="notification-icon">📡</div>
    <div class="notification-content">
      <div class="notification-title">设备定位</div>
      <div class="notification-message">{{ notificationMessage }}</div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineEmits, ref, onMounted, onUnmounted, inject } from 'vue'

const props = defineProps({
  currentScene: {
    type: String,
    required: true
  }
})

defineEmits(['sceneChange'])

// 获取事件总线
const emitter = inject('emitter')

// 通知相关状态
const showNotification = ref(false)
const isNotificationFading = ref(false)
const notificationMessage = ref('')
let notificationTimer = null

// 处理设备定位通知
const handleDeviceLocate = (event) => {
  // 检查事件详情
  if (!event.detail) return
  
  console.log('SceneDock收到设备定位事件:', event.detail)
  
  // 设置通知内容
  notificationMessage.value = event.detail.message || `正在定位到设备${event.detail.deviceId}...`
  
  // 清除可能存在的定时器
  if (notificationTimer) {
    clearTimeout(notificationTimer)
    showNotification.value = false
    isNotificationFading.value = false
  }
  
  // 显示通知
  showNotification.value = true
  isNotificationFading.value = false
  
  // 设置通知消失定时器
  notificationTimer = setTimeout(() => {
    // 开始淡出动画
    isNotificationFading.value = true
    
    // 动画结束后隐藏通知
    setTimeout(() => {
      showNotification.value = false
    }, 1000) // 淡出动画持续时间
  }, 5000) // 显示5秒后开始淡出
}

// 监听设备定位事件
onMounted(() => {
  // 使用window.addEventListener监听全局自定义事件
  window.addEventListener('device-locate', handleDeviceLocate)
})

// 组件卸载时清理事件监听
onUnmounted(() => {
  // 移除全局事件监听
  window.removeEventListener('device-locate', handleDeviceLocate)
  if (notificationTimer) {
    clearTimeout(notificationTimer)
  }
})

const scenes = [
  {
    id: 'oilPump',
    name: '采油管理',
    icon: '🛢️'
  },
  {
    id: 'gasPipeline',
    name: '输油管道',
    icon: '🔧'
  },
  {
    id: 'waterInjection',
    name: '注水管理',
    icon: '💧'
  }
]
</script>

<style scoped>
.scene-dock {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 8px;
  display: flex;
  gap: 16px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.dock-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.dock-item:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: translateY(-2px);
}

.dock-item.active {
  background: rgba(0, 0, 0, 0.1);
}

.scene-icon {
  font-size: 24px;
  margin-bottom: 4px;
}

.scene-name {
  font-size: 12px;
  color: #333;
  text-align: center;
}

/* 设备定位通知样式 */
.device-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background: rgba(0, 0, 0, 0.75);
  color: white;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  animation: slide-in 0.5s ease-out;
  opacity: 1;
  transition: opacity 1s ease-out;
}

.device-notification.fade-out {
  opacity: 0;
}

.notification-icon {
  font-size: 24px;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: bold;
  margin-bottom: 4px;
  font-size: 14px;
  color: #91d5ff;
}

.notification-message {
  font-size: 13px;
  line-height: 1.4;
}

@keyframes slide-in {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}
</style>