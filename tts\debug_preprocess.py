#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试文本预处理函数
"""

import re

def debug_preprocess_text(text: str) -> str:
    """
    调试版本的文本预处理函数
    """
    if not text:
        return ""
    
    print(f"原始文本: '{text}'")
    
    # 1. 去除Markdown标题标记
    text = re.sub(r'^#{1,6}\s+', '', text, flags=re.MULTILINE)
    print(f"步骤1-去除标题: '{text}'")
    
    # 2. 去除粗体和斜体标记
    text = re.sub(r'\*\*\*(.+?)\*\*\*', r'\1', text)
    text = re.sub(r'\*\*(.+?)\*\*', r'\1', text)
    text = re.sub(r'\*(.+?)\*', r'\1', text)
    text = re.sub(r'__(.+?)__', r'\1', text)
    text = re.sub(r'_(.+?)_', r'\1', text)
    print(f"步骤2-去除格式: '{text}'")
    
    # 3-9. 其他Markdown处理
    text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)
    text = re.sub(r'!\[[^\]]*\]\([^)]+\)', '', text)
    text = re.sub(r'```[^`]*```', '', text, flags=re.DOTALL)
    text = re.sub(r'`([^`]+)`', r'\1', text)
    text = re.sub(r'^>\s+', '', text, flags=re.MULTILINE)
    text = re.sub(r'^\s*[-*+]\s+', '', text, flags=re.MULTILINE)
    text = re.sub(r'^\s*\d+\.\s+', '', text, flags=re.MULTILINE)
    text = re.sub(r'\|', ' ', text)
    text = re.sub(r'^[-\s:]+$', '', text, flags=re.MULTILINE)
    text = re.sub(r'<[^>]+>', '', text)
    print(f"步骤3-9-Markdown清理: '{text}'")
    
    # 10. 关键步骤：处理字母数字组合
    text = re.sub(r'([A-Z])(\d)', r'\1 \2', text)
    print(f"步骤10-字母数字分离: '{text}'")
    
    # 11. 处理英文单词
    text = re.sub(r'(?<!\s)([A-Za-z]{2,})(?!\s)', r' \1 ', text)
    print(f"步骤11-英文单词处理: '{text}'")
    
    # 12. 处理数字
    text = re.sub(r'(?<!\s)(\d+)(?!\s)', r' \1 ', text)
    print(f"步骤12-数字处理: '{text}'")
    
    # 13. 处理单个字母
    text = re.sub(r'(?<!\s)([A-Z])(?!\s)(?!\d)', r' \1 ', text)
    print(f"步骤13-单个字母处理: '{text}'")
    
    # 14. 规范化空白字符
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()
    print(f"步骤14-最终结果: '{text}'")
    
    return text

def test_cases():
    """测试各种情况"""
    test_texts = [
        "A2日报",
        "点击查看完整的作业区生产油井A2日报",
        "B1报告",
        "A2、B1、C3",
        "第A2版"
    ]
    
    for text in test_texts:
        print("\n" + "="*50)
        result = debug_preprocess_text(text)
        print(f"最终结果: '{result}'")

if __name__ == "__main__":
    test_cases()
