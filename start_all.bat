@echo off
echo 正在启动所有服务...

:: 后端服务
start cmd /k "cd Backend && venv\Scripts\activate && python -m app.main"

:: 前端服务
start cmd /k "cd frontend && npm run dev"

:: 模拟器
start cmd /k "cd simulator && venv\Scripts\activate && python main.py"

:: RTSP代理
start cmd /k "cd rtsp_proxy && venv\Scripts\activate && python rtsp_proxy.py --host 0.0.0.0 --port 8001"




:: mcp
start cmd /k "cd C:\AI\IOT_Agent_MVP2\Backend\app\minimal-mcp && .venv_new\Scripts\activate && python custom_interactive_client_sse.py"




start cmd /k "cd C:\AI\IOT_Agent_MVP2\rtsp视频流 && .\create_rtsp_server.bat"


start cmd /k "cd C:\AI\IOT_Agent_MVP2\rtsp视频流 && ffmpeg -re -stream_loop -1 -i 1.mp4 -c copy -f rtsp rtsp://admin:admin123@localhost:8554/stream1"

echo 所有服务已启动！ 