#!/bin/bash

# 创建目录结构
mkdir -p emqx/etc
mkdir -p emqx/data
mkdir -p emqx/log
mkdir -p milvus/data
mkdir -p milvus/etcd
mkdir -p milvus/minio
mkdir -p mysql/data
mkdir -p mysql/initdb
mkdir -p nginx/log
mkdir -p nginx/unity
mkdir -p nginx/vue
mkdir -p ollama/models
mkdir -p pyagent

# 写入nginx默认配置
cat > nginx/nginx.conf << EOF
user  nginx;
worker_processes  auto;

error_log  /var/log/nginx/error.log notice;
pid        /var/run/nginx.pid;

events {
    worker_connections  1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile        on;
    keepalive_timeout  65;

    include /etc/nginx/conf.d/*.conf;
}
EOF

# 写入emqx默认配置
cat > emqx/etc/emqx.conf << EOF
## NOTE:
## This config file overrides data/configs/cluster.hocon,
## and is merged with environment variables which start with 'EMQX_' prefix.
##
## Config changes made from EMQX dashboard UI, management HTTP API, or CLI
## are stored in data/configs/cluster.hocon.
## To avoid confusion, please do not store the same configs in both files.
##
## See https://www.emqx.io/docs/en/v5.0/configuration/configuration.html for more details.
## Configuration full example can be found in etc/examples

node {
  name = "emqx@***********"
  cookie = "emqxsecretcookie"
  data_dir = "data"
}

cluster {
  name = emqxcl
  discovery_strategy = manual
}

dashboard {
    listeners.http {
        bind = 18083
    }
    default_username = "admin"
    default_password = "admin123"
}
EOF

# 写入emqx acl默认配置
cat > emqx/etc/acl.conf << EOF
%%--------------------------------------------------------------------
%% -type(ipaddr() :: {ipaddr, string()}).
%%
%% -type(ipaddrs() :: {ipaddrs, [string()]}).
%%
%% -type(username() :: {user | username, string()} | {user | username, {re, regex()}}).
%%
%% -type(clientid() :: {client | clientid, string()} | {client | clientid, {re, regex()}}).
%%
%% -type(who() :: ipaddr() | ipaddrs() | username() | clientid() |
%%                {'and', [ipaddr() | ipaddrs() | username() | clientid()]} |
%%                {'or',  [ipaddr() | ipaddrs() | username() | clientid()]} |
%%                all).
%%
%% -type(action() :: subscribe | publish | all).
%%
%% -type(topic_filters() :: string()).
%%
%% -type(topics() :: [topic_filters() | {eq, topic_filters()}]).
%%
%% -type(permission() :: allow | deny).
%%
%% -type(rule() :: {permission(), who(), action(), topics()} | {permission(), all}).
%%--------------------------------------------------------------------

{allow, {username, {re, "^dashboard$"}}, subscribe, ["$SYS/#"]}.

{allow, {ipaddr, "127.0.0.1"}, all, ["$SYS/#", "#"]}.

{deny, all, subscribe, ["$SYS/#", {eq, "#"}]}.

{allow, all}.

EOF

# 写入mysql默认配置
cat > mysql/mysql.cnf << EOF
[mysqld]
user=mysql
default-storage-engine=INNODB
character-set-server=utf8
collation-server=utf8_general_ci
max_connections=200
innodb_file_per_table=1
skip-name-resolve
lower_case_table_names=1
default-time-zone='+8:00'

[client]
default-character-set=utf8
EOF

echo "初始化完成！"