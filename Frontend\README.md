# 前端展示模块开发指南

## 1. 功能描述

1. 实现三维数字孪生场景可视化（基于Three.js）
    - 场景样式：深蓝色科技感天空盒背景，中心平台布局（长方体平台+红/蓝/绿/橙/紫五个正方体设备）
    - 设备交互：点击触发外发光特效，显示对应井号（井01-井05）信息面板
    - 数据更新：20秒间隔通过MQTT协议获取设备实时数据（温度/压力/产量/流量）
2. 集成聊天交互面板、设备信息展示面板和RAG文档管理面板
3. RAG文档管理功能：
    - 支持拖拽/点击上传多种文档格式（PDF/DOCX/XLSX）
    - 展示已向量化文件列表（文件名/上传时间/索引状态）
    - 实时显示Milvus向量库记录统计信息
    - 上传完成后弹窗提示索引创建结果
4. 设备实时数据监控：
    - 通过MQTT协议直连EMQX Broker订阅设备数据
    - 20秒间隔更新设备状态（温度/压力/产量/流量）
    - 设备点击高亮与信息面板联动显示

## 2. 目录结构

```
frontend/
├── public/                     # 静态资源
│   └── Files/                  # 3D模型和文档资源
├── src/
│   ├── assets/                 # 样式和资源文件
│   │   └── main.css           # 全局样式文件
│   ├── components/            # Vue组件
│   │   ├── Scene/             # 三维场景相关组件
│   │   │   ├── Scene.vue      # 主场景渲染和交互
│   │   │   └── SceneDock.vue  # 场景切换控制
│   │   ├── ChatPanel/         # 智能对话组件
│   │   │   ├── ChatPanel.vue  # 聊天主界面
│   │   │   └── DocSystemModal # 文档系统弹窗
│   │   ├── DevicePanel/       # 设备监控组件
│   │   │   └── DevicePanel.vue # 设备数据展示面板
│   │   └── RagManager/        # 文档管理组件
│   │       ├── HomeView.vue    # 首页视图
│   │       ├── DocumentsView   # 文档列表视图
│   │       ├── SearchView     # 文档搜索视图
│   │       └── RagManager.vue  # RAG管理主组件
│   ├── router/                # 路由配置
│   │   └── index.js          # 路由定义
│   ├── stores/                # 状态管理
│   │   ├── ragStore.js       # 文档系统状态
│   │   └── deviceStore.js    # 设备数据状态
│   ├── utils/                 # 工具类
│   │   └── axios.js          # API请求封装
│   └── main.js               # 应用入口
├── index.html                # HTML模板
├── jsconfig.json             # JS配置
├── package.json              # 项目依赖
├── README.md                 # 项目说明
└── vite.config.js            # Vite配置
```

## 3. 页面样式规范

### 3.1 三维场景（Scene组件）

- 场景配置：
  - 主画布：100vw x 100vh 全屏显示
  - 背景：深蓝色科技感天空盒（hex:#0a1a2f）
  - 相机：透视相机，初始位置(300, 160, -50)
  - 光照：环境光、方向光、点光源组合
  
- 设备可视化：
  - 井口模型：300x300x300 彩色立方体
  - 支持三种场景切换：
    * 油泵场景：5个不同颜色立方体表示井口
    * 气体管道：加载3dzutai.gltf模型
    * 水注管理：加载cyj.glb模型
  
- 交互功能：
  - WASD：相机平移
  - QE：相机升降
  - 鼠标左键：设备选择
  - 鼠标拖动：场景旋转
  - ESC：取消选中
  
- 特效：
  - 选中设备高亮发光
  - 设备旋转动画
  - 边框勾勒效果
  - 鼠标悬停反馈

### 3.2 设备信息面板（DevicePanel组件）

- 面板布局：
  - 宽度：400px
  - 自适应高度根据内容展开
  - 背景：深色半透明（rgba(33, 33, 33, 0.95)）
  - 阴影效果：0 4px 16px rgba(0, 0, 0, 0.3)

- 数据展示区域：
  - 设备状态信息：
    * 设备ID和运行状态指示器（正常/异常）
    * 油压/套压/井口温度数据
    * 冲程/冲次运行参数
    * 有功/无功功率及功率因数

  - 电气参数面板（可折叠）：
    * A/B/C三相电压和电流
    * 滑动展开/收起动画
    * 深色背景区分视觉层级

  - 功图显示区域：
   * 支持地面功图/电功图切换
   * 基于ECharts的双曲线图表
   * 上行程（红色）/下行程（蓝色）区分
   * 自动缩放和响应式布局
   * 坐标轴数值保留1位小数
   * 显示最大/最小载荷或电流数据
   * 暗色主题适配和样式优化
   * 图表自适应容器尺寸调整

### 3.3 聊天面板（ChatPanel组件）

- 基础布局：
  - 位置：左下角固定（left:20px, bottom:20px）
  - 收起状态：50px圆形图标
  - 展开尺寸：450px宽，全屏高度减40px
  - 背景：深色半透明（rgba(13, 17, 23, 0.95)）

- 界面元素：
  - 顶部栏：
    * 标题："智能助手"
    * 文档管理按钮（文件夹图标）
    * 关闭按钮（X图标）
  
  - 消息列表：
    * 用户消息：蓝色背景(#1a4b91)右对齐
    * AI消息：深灰背景(#2f3441)左对齐
    * Markdown渲染支持（marked库）
    * 代码块样式优化
  
  - 输入区域：
    * 多行文本框自适应
    * Enter键发送消息
    * 发送按钮状态响应
    * 输入框焦点视觉反馈

### 3.4 RAG文档管理系统（RagManager组件）

- 基础配置：
  - 模态框布局，居中显示
  - 尺寸：800x600px
  - 背景：深色半透明（与聊天面板统一风格）
  - 滚动优化：自定义滚动条样式

- 视图组件：
  - HomeView（首页）：
    * Milvus向量库状态展示
    * 文档总数统计信息
    * 快速上传入口
    * 系统状态概览

  - DocumentsView（文档列表）：
    * 文件列表展示
    * 文件状态标识
    * 上传时间排序
    * 索引进度指示

  - SearchView（检索界面）：
    * 语义相似度搜索
    * 结果关联度展示
    * 内容片段预览
    * 上下文理解增强

- 上传功能：
  - 支持拖拽和点击上传
  - 文件类型校验（PDF/DOCX/XLSX）
  - 上传进度实时显示
  - 大文件分块处理


## 4. 交互功能说明

### 4.1 场景操作

```mermaid
flowchart TD
    A[鼠标左键按下] --> B{在空白区域?}
    B -->|是| C[旋转视角]
    B -->|否| D[选中设备]
    D --> E[显示信息面板]
    E --> F[开启外发光特效]
```

### 4.2 数据更新机制

- 实时数据：每20秒通过MQTT获取
- 历史数据：首次加载时获取最近24小时
- 异常数据：红色闪烁警示（animation: blink 1s infinite）

### 4.3 智能对话流程

1. 用户输入问题
2. 提交到FastAPI服务
3. 接收SSE流式响应
4. 动态渲染Markdown内容
5. 检测到设备名称时自动切换场景视角

### 4.4 聊天窗口与文档管理流程

- 聊天面板
  - 点击左下角聊天图标调出/收起聊天面板
  - 支持Markdown格式消息渲染
  - Enter键快捷发送消息
  - 自动滚动到最新消息
  
- 文档管理功能
  - 通过聊天面板顶部的文件夹图标打开文档管理器
  - 文档管理器支持以下功能：
    * 首页概览（HomeView）
    * 文档列表浏览（DocumentsView）
    * 文档内容搜索（SearchView）
    * 拖拽或点击上传文档

## 5. 详细开发步骤

1. VUE3 最小开发环境构建
    - 初始化Vue3项目
    - 集成Three.js渲染引擎
2. 三维场景开发：
    - 初始化Three.js场景（设置透视相机、渲染器、灯光）
    - 加载天空盒纹理（使用深蓝色科技感HDR贴图）
    - 构建中心平台及设备模型
3. 信息面板开发：
    - 点击设备模型后，模型轮廓高亮同时在鼠标左侧/右侧弹出信息面板
    - 点击场景中空白位置时，模型恢复面板消失
    - 实现响应式数据面板组件（Vue3 Composition API）
    - 配置MQTT客户端连接（保留自动重连机制）
    - 集成ECharts实现功图曲线渲染：
    - 添加面板动画过渡效果（Vue Transition）
4. 聊天面板开发：
    - 实现消息历史滚动容器（虚拟滚动优化）
    - 开发Markdown格式消息渲染器
    - SSE流式消息处理
    - 添加场景视角联动功能
5. RAG文档管理面板：
    - 实现拖拽上传组件（支持PDF/DOCX/XLSX）
    - 集成Milvus客户端显示索引状态
    - 添加文件上传进度指示器
    - 配置Web Worker处理大文件分块

