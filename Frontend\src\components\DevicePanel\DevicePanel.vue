<template>
  <div class="device-panel" :style="panelStyle" ref="panelRef">
    <!-- 设备名称和控制按钮 -->
    <div class="panel-header" @mousedown="startDrag">
      <div class="header-controls">
        <h3>{{ deviceInfo.display_name || deviceInfo.WELL_COMMON_NAME }}</h3>
        <div class="panel-buttons">
          <button class="panel-btn minimize-btn" @click="minimizePanel" title="最小化">
            <span>—</span>
          </button>
          <button class="panel-btn close-btn" @click="closePanel" title="关闭">
            <span>×</span>
          </button>
        </div>
      </div>
    </div>

    <div v-if="!isMinimized" class="panel-content">
      <!-- 状态面板 -->
      <div class="status-panel">
        <div class="status-row">
          <span class="label">井名:</span>
          <span class="value">{{ deviceInfo.display_name || deviceInfo.WELL_COMMON_NAME }}</span>
          <div :class="['status-indicator', deviceInfo.status === 'normal' ? 'normal' : 'abnormal']"></div>
        </div>
        
        <div class="status-row">
          <div class="col">
            <span class="label">油压:</span>
            <span class="value">{{ formatNumber(deviceInfo.oilPressure) }}Mpa</span>
          </div>
          <div class="col">
            <span class="label">套压:</span>
            <span class="value">{{ formatNumber(deviceInfo.casingPressure) }}Mpa</span>
          </div>
        </div>

        <div class="status-row">
          <span class="label">井口温度:</span>
          <span class="value">{{ formatNumber(deviceInfo.wellheadTemp) }}℃</span>
        </div>

        <div class="status-row">
          <div class="col">
            <span class="label">冲程:</span>
            <span class="value">{{ formatNumber(deviceInfo.strokeLength) }}m</span>
          </div>
          <div class="col">
            <span class="label">冲次:</span>
            <span class="value">{{ formatNumber(deviceInfo.strokeRate) }}/min</span>
          </div>
        </div>

        <div class="status-row">
          <div class="col">
            <span class="label">有功功率:</span>
            <span class="value">{{ formatNumber(deviceInfo.activePower) }}KW</span>
          </div>
          <div class="col">
            <span class="label">无功功率:</span>
            <span class="value">{{ formatNumber(deviceInfo.reactivePower) }}KW</span>
          </div>
        </div>

        <div class="status-row">
          <div class="col">
            <span class="label">功率因数:</span>
            <span class="value">{{ formatNumber(deviceInfo.powerFactor) }}</span>
          </div>
          <div class="col">
            <span class="label">总功耗:</span>
            <span class="value">{{ formatNumber(deviceInfo.totalPower) }}KWh</span>
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="main-content">
        <!-- 电气参数区域 -->
        <div class="collapsible-sections">
          <!-- 电气参数 - 保留折叠功能 -->
          <div class="electrical-section panel-section">
            <div class="section-header" @click="toggleElectricalParams">
              <span>电气参数</span>
              <span :class="['arrow', showElectricalParams ? 'up' : 'down']">▼</span>
            </div>
            <div v-show="showElectricalParams" class="electrical-params">
              <div class="param-row">
                <div class="col">
                  <span class="label">A相电压:</span>
                  <span class="value">{{ formatNumber(deviceInfo.phaseA?.voltage) }}V</span>
                </div>
                <div class="col">
                  <span class="label">A相电流:</span>
                  <span class="value">{{ formatNumber(deviceInfo.phaseA?.current) }}A</span>
                </div>
              </div>
              <div class="param-row">
                <div class="col">
                  <span class="label">B相电压:</span>
                  <span class="value">{{ formatNumber(deviceInfo.phaseB?.voltage) }}V</span>
                </div>
                <div class="col">
                  <span class="label">B相电流:</span>
                  <span class="value">{{ formatNumber(deviceInfo.phaseB?.current) }}A</span>
                </div>
              </div>
              <div class="param-row">
                <div class="col">
                  <span class="label">C相电压:</span>
                  <span class="value">{{ formatNumber(deviceInfo.phaseC?.voltage) }}V</span>
                </div>
                <div class="col">
                  <span class="label">C相电流:</span>
                  <span class="value">{{ formatNumber(deviceInfo.phaseC?.current) }}A</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 功图部分 - 移除折叠功能 -->
        <div class="card-section panel-section">
          <div class="section-header">
            <span>功图分析</span>
          </div>
          <div class="card-content-fixed">
            <div class="tab-header">
              <div 
                :class="['tab', activeTab === 'surface' ? 'active' : '']"
                @click="activeTab = 'surface'"
              >
                地面功图
              </div>
              <div 
                :class="['tab', activeTab === 'power' ? 'active' : '']"
                @click="activeTab = 'power'"
              >
                电功图
              </div>
            </div>
            <div class="acquisition-time">
              采集时间: {{ deviceInfo.Check_date || '-' }}
            </div>
            <div class="card-container" ref="chartRef">
              <!-- ECharts 将在这里渲染 -->
            </div>
            <div class="chart-info">
              <template v-if="activeTab === 'surface'">
                <div class="info-item">
                  <span class="info-label">最大载荷:</span>
                  <span class="info-value">{{ formatNumber(chartMaxValue) }} kN</span>
                </div>
                <div class="info-item">
                  <span class="info-label">最小载荷:</span>
                  <span class="info-value">{{ formatNumber(chartMinValue) }} kN</span>
                </div>
              </template>
              <template v-else>
                <div class="info-item">
                  <span class="info-label">最大电流:</span>
                  <span class="info-value">{{ formatNumber(chartMaxValue) }} A</span>
                </div>
                <div class="info-item">
                  <span class="info-label">最小电流:</span>
                  <span class="info-value">{{ formatNumber(chartMinValue) }} A</span>
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import * as echarts from 'echarts'
import { message } from 'ant-design-vue'
import screenfull from 'screenfull'

// 格式化数字，保留2位小数
const formatNumber = (value) => {
  if (value === undefined || value === null) return '-'
  return Number(value).toFixed(2)
}

// 定义props和emits
const props = defineProps({
  deviceInfo: {
    type: Object,
    required: true
  },
  position: {
    type: Object,
    required: true,
    default: () => ({ x: 0, y: 0 })
  }
})

const emit = defineEmits(['heightChange', 'close'])

// 计算面板高度
const panelHeight = computed(() => {
  // 基础高度（header + status panel）
  let height = 140
  
  // 如果电气参数面板展开，添加其高度
  if (showElectricalParams.value) {
    height += 120
  }
  
  // 功图面板（固定高度）
  height += 320
  
  // 额外的边距，确保内容不会紧贴底部
  height += 10
  
  return height
})

// 最小化和关闭控制
const isMinimized = ref(false)
const minimizePanel = () => {
  isMinimized.value = !isMinimized.value
}

const closePanel = () => {
  emit('close')
}

// 拖动相关
const panelRef = ref(null)
let isDragging = false
let dragStartX = 0
let dragStartY = 0
let initialX = 0
let initialY = 0
const customPosition = ref({ x: props.position.x, y: props.position.y })

const startDrag = (event) => {
  // 避免点击按钮时触发拖动
  if (event.target.closest('.panel-btn')) return
  
  isDragging = true
  dragStartX = event.clientX
  dragStartY = event.clientY
  initialX = customPosition.value.x
  initialY = customPosition.value.y
  
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

const onDrag = (event) => {
  if (!isDragging) return
  
  const dx = event.clientX - dragStartX
  const dy = event.clientY - dragStartY
  
  customPosition.value = {
    x: initialX + dx,
    y: initialY + dy
  }
}

const stopDrag = () => {
  isDragging = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 面板位置样式
const panelStyle = computed(() => {
  // 发送高度信息到父组件
  const height = isMinimized.value ? 50 : panelHeight.value
  emit('heightChange', height)
  
  return {
    left: `${customPosition.value.x}px`,
    top: `${customPosition.value.y}px`,
    height: 'auto', // 使用自动高度，允许内容撑开面板
    overflowY: 'visible', // 不使用滚动条，允许内容自然溢出
    overflowX: 'hidden', // 防止水平滚动
    cursor: isDragging ? 'grabbing' : 'default',
    padding: '10px 10px 15px 10px' /* 调整内边距 */
  }
})

// 控制电气参数显示
const showElectricalParams = ref(false)
const toggleElectricalParams = () => {
  showElectricalParams.value = !showElectricalParams.value
}

// 控制功图标签页
const activeTab = ref('surface')

// 功图相关
const chartRef = ref(null)
let chart = null
const chartMaxValue = ref(0)
const chartMinValue = ref(0)

// 解析功图数据
const parseCardData = (data) => {
  if (!data) {
    return { upstroke: [], downstroke: [] }
  }

  try {
    const points = data.split('|').map(point => {
      const [x, y] = point.split(',').map(Number)
      if (isNaN(x) || isNaN(y)) {
        throw new Error(`无效的数据点: ${point}`)
      }
      return [x, y]
    })
    
    // 分离上行程和下行程数据
    const midPoint = Math.floor(points.length / 2)
    const upstroke = points.slice(0, midPoint)
    const downstroke = points.slice(midPoint)
    
    return { upstroke, downstroke }
  } catch (error) {
    return { upstroke: [], downstroke: [] }
  }
}

// 初始化图表
const initChart = () => {
  if (chart) {
    chart.dispose()
  }
  nextTick(() => {
    if (chartRef.value) {
      chart = echarts.init(chartRef.value)
      window.addEventListener('resize', chart.resize)
    }
  })
}

// 渲染功图
const renderChart = () => {
  if (!chartRef.value || !chart) return
  
  const { disp_load, disp_current } = props.deviceInfo
  const chartData = activeTab.value === 'surface'
    ? { data: disp_load, yAxisName: '载荷 (kN)' }
    : { data: disp_current, yAxisName: '电流 (A)' }
  
  console.log(`正在渲染${activeTab.value === 'surface' ? '地面功图' : '电功图'}`);
  
  const { upstroke, downstroke } = parseCardData(chartData.data)
  
  try {
    // 检查是否有足够的数据点来渲染图表
    if (upstroke.length === 0 || downstroke.length === 0) {
      console.warn('没有足够的功图数据点');
      return;
    }
    
    // 计算数据边界
    const allPoints = [...upstroke, ...downstroke]
    if (allPoints.length === 0) return
    
    const xValues = allPoints.map(point => point[0])
    const yValues = allPoints.map(point => point[1])
    const xMin = Math.min(...xValues)
    const xMax = Math.max(...xValues)
    const yMin = Math.min(...yValues)
    const yMax = Math.max(...yValues)
    
    // 更新最大最小值显示
    chartMaxValue.value = yMax
    chartMinValue.value = yMin

    // 计算合适的边界，确保数据不会超出边界
    const xPadding = (xMax - xMin) * 0.1
    const yPadding = (yMax - yMin) * 0.1
    
    // 明确设置坐标轴最小值为0，确保只显示第一象限
    const finalXMin = Math.max(0, xMin - xPadding)
    // 确保X轴最大值足够大，以显示完整数据
    const finalXMax = Math.max(6.0, xMax + xPadding)
    const finalYMin = Math.max(0, yMin - yPadding)
    
    let option = {
      color: ['#1890FF', '#E73344'],
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        },
        confine: true,    // 确保提示框不会超出图表区域
        extraCssText: 'z-index: 1000;'  // 确保提示框在最高层级
      },
      grid: {
        top: 25,
        right: 20,  // 从15增加到20，进一步增加右侧空间
        bottom: 35,
        left: 15,
        containLabel: true
      },
      legend: {
        data: ['上行程', '下行程'],
        top: 0,
        left: 'center',     // 将图例放在顶部中央
        itemGap: 20,        // 增加图例项之间的间距
        textStyle: {
          color: '#000000',
          fontSize: 10
        },
        itemWidth: 15,      // 增加图例图标宽度
        itemHeight: 7       // 增加图例图标高度
      },
      xAxis: {
        type: 'value',
        name: '位移 (m)',
        min: 0,
        max: xMax * 1.1,
        axisLabel: {
          color: '#000000',
          fontSize: 11,
          margin: 8,      
          align: 'center',
          formatter: function(value, index) {
            return value.toFixed(1);
          },
          inside: false,  // 确保标签在轴外部
          showMaxLabel: true  // 显示最大刻度标签
        }
      },
      yAxis: {
        type: 'value',
        name: chartData.yAxisName,
        min: 0,
        max: yMax * 1.1,
        scale: true,
        axisLabel: {
          color: '#000000',
          formatter: (value) => {
            // 简化数值显示，只使用整数
            return Math.round(value);
          },
          fontSize: 9,     // 从10减小到9，使数字更小
          margin: 3,       // 从5减小到3，减少标签与轴线的距离
          align: 'right'
        },
        nameTextStyle: {
          fontSize: 9,     // 从10减小到9，使名称更小
          color: '#000',
          padding: [0, 0, 0, 0]
        }
      },
      series: [
        {
          name: '上行程',
          type: 'line',
          data: upstroke,
          showSymbol: false,
          lineStyle: { 
            color: '#f5222d', 
            width: 2.5       // 增加线宽，使线条更明显
          },
          smooth: true
        },
        {
          name: '下行程',
          type: 'line',
          data: downstroke,
          showSymbol: false,
          lineStyle: { 
            color: '#1890ff', 
            width: 2.5       // 增加线宽，使线条更明显
          },
          smooth: true
        }
      ],
      graphic: [{
        type: 'text',
        right: 10,  // 从5增加到10
        bottom: 8,
        style: {
          text: '位移(m)',
          fontSize: 11,
          fill: '#666'
        }
      }]
    }

    chart.setOption(option)
    chart.resize(); // 确保图表完全适应容器
  } catch (error) {
    console.error('渲染功图出错:', error);
    chart = null
  }
}

// 监听标签页切换和数据变化
watch([activeTab, () => props.deviceInfo], () => {
  nextTick(() => renderChart())
}, { deep: true })

// 监听事件的处理函数引用，方便后续移除
const handleResize = () => {
  if (chart) {
    chart.resize()
  }
}

onMounted(() => {
  // 初始化自定义位置
  customPosition.value = { x: props.position.x, y: props.position.y }
  
  nextTick(() => {
    initChart()
    renderChart()
  })
  
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 清理拖动事件监听
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  
  // 清理图表资源
  if (chart) {
    // 移除resize事件监听器
    window.removeEventListener('resize', chart.resize)
    chart.dispose()
    chart = null
  }
  
  // 移除窗口resize事件监听
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 面板整体布局 */
.device-panel {
  width: 430px;
  background-color: rgba(250, 250, 250, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 10px;
  color: #333;
  position: fixed;
  z-index: 1000;
  text-shadow: none;
  max-height: none;
  overflow-y: visible;
  overflow-x: hidden;
  transition: all 0.3s ease;
  user-select: none;
}

/* 面板内容布局 */
.panel-content {
  display: flex;
  flex-direction: column;
  padding: 0 3px; /* 减少内容区域的内边距 */
}

/* 主内容区域 */
.main-content {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px; /* 从15px减少到8px，减少各区域之间的间距 */
}

/* 每个区域块共享的样式 */
.panel-section {
  margin-bottom: 5px; /* 从10px减少到5px，减少底部边距 */
  display: flex;
  flex-direction: column;
  position: relative; /* 添加相对定位 */
}

/* 电气参数部分的包装容器 */
.collapsible-sections {
  display: flex;
  flex-direction: column;
  position: relative;
  margin-bottom: 0; /* 从5px减少到0，移除与功图区域的间距 */
}

/* 电气参数区域 */
.electrical-params {
  padding: 10px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
  margin-top: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  max-height: 120px; /* 固定最大高度 */
  overflow: hidden;
}

/* 功图区域固定样式 */
.card-section {
  overflow: visible; /* 改为visible允许内容溢出 */
  flex-shrink: 0; /* 防止被挤压 */
  position: relative;
  display: flex; 
  flex-direction: column;
  width: 100%; /* 确保占满整个宽度 */
  min-height: auto; /* 自适应内容高度 */
  max-height: none; /* 移除最大高度限制 */
  margin-bottom: 0; /* 移除底部边距 */
  margin-top: 0; /* 新增顶部边距0 */
}

/* 功图容器样式 - 固定高度，不再折叠 */
.card-content-fixed {
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  background-color: #f7f7f7;
  margin-top: 2px; /* 从4px减少到2px，缩小section-header与内容的间距 */
  overflow: visible; /* 更改为visible */
  height: auto; /* 自适应高度 */
}

/* 功图容器固定高度 */
.card-container {
  height: 280px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.85);
  overflow: visible;
  position: relative;
  background-color: #fff;
  margin-bottom: 0;
  padding: 5px 20px 0 5px; /* 从5px 15px 0 0修改，增加右侧内边距，增加左侧内边距 */
}

.panel-header {
  margin-bottom: 15px; /* 增加头部底部边距 */
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  cursor: grab;
  padding-bottom: 5px; /* 增加头部底部内边距 */
}

.header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-buttons {
  display: flex;
  align-items: center;
}

.panel-btn {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  margin-left: 6px;
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
  color: white;
}

.minimize-btn {
  background-color: #faad14;
}

.close-btn {
  background-color: #f5222d;
}

.panel-header h3 {
  margin: 0;
  padding-bottom: 6px;
  color: #333;
  font-size: 1em;
}

.status-panel {
  margin-bottom: 15px; /* 增加状态面板的底部边距 */
}

.status-row {
  display: flex;
  align-items: center;
  margin-bottom: 6px;
  padding: 2px 0;
}

.status-row .col {
  flex: 1;
  display: flex;
  align-items: center;
}

.label {
  color: rgba(0, 0, 0, 0.85);
  margin-right: 8px;
  font-weight: 500;
}

.value {
  font-weight: 600;
  color: rgba(0, 0, 0, 1);
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-left: 8px;
}

.status-indicator.normal {
  background-color: #52c41a;
}

.status-indicator.abnormal {
  background-color: #f5222d;
}

.electrical-section {
  margin-bottom: 12px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px; /* 从10px减少到6px，减少上下内边距 */
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  cursor: pointer;
  color: #333;
  font-weight: 600;
  border-left: 3px solid #1890ff;
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.section-header:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

.arrow {
  font-size: 12px;
  transition: transform 0.3s;
  color: rgba(0, 0, 0, 0.65);
}

.arrow.up {
  transform: rotate(180deg);
}

.param-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
}

.acquisition-time {
  padding: 0 6px;  /* 从1px减小到0px */
  color: rgba(0, 0, 0, 0.7); /* 减淡文字颜色 */
  font-size: 0.7em;  /* 从0.75em减小到0.7em */
  border-bottom: 1px solid rgba(0, 0, 0, 0.08); /* 减淡边框 */
  background: rgba(0, 0, 0, 0.02); /* 减淡背景 */
  line-height: 16px; /* 添加行高控制 */
  margin-top: 3px;
  margin-bottom: 3px;
}

.tab-header {
  display: flex;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08); /* 减淡边框 */
  background-color: #f8f8f8; /* 淡化背景色 */
  padding: 0;
}

.tab {
  padding: 1px 8px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  margin-bottom: 2px;
  color: rgba(0, 0, 0, 0.65);
  transition: color 0.3s;
  font-size: 0.75em;  /* 从0.8em减小到0.75em */
  line-height: 18px; /* 添加行高控制 */
}

.tab:hover {
  color: rgba(0, 0, 0, 0.9);
}

.tab.active {
  border-bottom-color: #1890ff;
  color: #1890ff;
}

.chart-info {
  padding: 1px 5px 3px 8px;  /* 从2px 5px 5px 8px减小到1px 5px 3px 8px，进一步缩小上下间距 */
  display: flex;
  justify-content: space-around;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  background: rgba(247, 247, 247, 0.7);
  font-size: 0.75em;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

.info-item {
  display: flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.6); /* 减少不透明度 */
  padding: 2px 5px;  /* 从2px 5px减少到1px 5px */
  border-radius: 3px;
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.02); /* 减小阴影 */
}

.info-label {
  color: rgba(0, 0, 0, 0.75); /* 减淡文字颜色 */
  margin-right: 4px;  /* 从5px减少到4px */
  font-size: 0.8em;  /* 从0.85em减小到0.8em */
  font-weight: 500;
}

.info-value {
  color: #1890ff; /* 保持蓝色 */
  font-weight: 600;
  font-size: 0.8em;  /* 从0.85em减小到0.8em */
}
</style>