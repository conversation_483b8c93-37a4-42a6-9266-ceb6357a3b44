import json
import sys
import os

CONFIG_FILE = "config.json"

def update_mqtt_config(host=None, port=None, path=None):
    """更新MQTT配置"""
    # 检查配置文件是否存在
    if not os.path.exists(CONFIG_FILE):
        # 创建默认配置
        config = {
            "mqtt": {
                "host": "************",
                "port": 8083,
                "path": "/mqtt"
            },
            "devices": []
        }
    else:
        # 加载现有配置
        with open(CONFIG_FILE, "r", encoding="utf-8") as f:
            config = json.load(f)
    
    # 更新MQTT配置
    if "mqtt" not in config:
        config["mqtt"] = {}
    
    if host:
        config["mqtt"]["host"] = host
    if port:
        config["mqtt"]["port"] = int(port)
    if path:
        config["mqtt"]["path"] = path
    
    # 保存配置
    with open(CONFIG_FILE, "w", encoding="utf-8") as f:
        json.dump(config, f, indent=2, ensure_ascii=False)
    
    print(f"MQTT配置已更新: host={config['mqtt'].get('host')}, port={config['mqtt'].get('port')}, path={config['mqtt'].get('path')}")
    return config

def main():
    """主函数"""
    # 从命令行参数获取MQTT配置
    host = None
    port = None
    path = None
    
    # 解析命令行参数
    if len(sys.argv) > 1:
        for arg in sys.argv[1:]:
            if arg.startswith("--host="):
                host = arg.split("=")[1]
            elif arg.startswith("--port="):
                port = arg.split("=")[1]
            elif arg.startswith("--path="):
                path = arg.split("=")[1]
            elif arg == "--help" or arg == "-h":
                print("用法: python update_mqtt_config.py [--host=主机地址] [--port=端口] [--path=路径]")
                print("例如: python update_mqtt_config.py --host=************* --port=1884 --path=/mqtt")
                return
    
    # 如果没有提供任何参数，则交互式获取
    if not any([host, port, path]):
        print("===== MQTT配置更新工具 =====")
        
        # 读取现有配置
        if os.path.exists(CONFIG_FILE):
            with open(CONFIG_FILE, "r", encoding="utf-8") as f:
                config = json.load(f)
                current_host = config.get("mqtt", {}).get("host", "************")
                current_port = config.get("mqtt", {}).get("port", 8083)
                current_path = config.get("mqtt", {}).get("path", "/mqtt")
        else:
            current_host = "************"
            current_port = 8083
            current_path = "/mqtt"
        
        # 交互式获取新配置
        host_input = input(f"MQTT服务器地址 [{current_host}]: ")
        host = host_input if host_input else current_host
        
        port_input = input(f"MQTT服务器端口 [{current_port}]: ")
        port = port_input if port_input else current_port
        
        path_input = input(f"MQTT WebSocket路径 [{current_path}]: ")
        path = path_input if path_input else current_path
    
    # 更新配置
    config = update_mqtt_config(host, port, path)
    
    # 提示下一步操作
    print("\n配置已更新，您可以执行以下操作:")
    print("1. 运行 python mqtt_discovery.py 来发现设备")
    print("2. 运行 python ../custom_interactive_client_sse.py 启动主应用程序")

if __name__ == "__main__":
    main() 