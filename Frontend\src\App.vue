<script setup>
import { ref, provide } from 'vue'
import Scene from './components/Scene/Scene.vue'
import ChatPanel from './components/ChatPanel/ChatPanel.vue'
import FileViewer from './components/FileViewer/FileViewer.vue'

const sceneRef = ref(null)
provide('sceneRef', sceneRef)
</script>

<template>
  <div class="app">
    <Scene ref="sceneRef" />
    <router-view></router-view>
    <FileViewer />
  </div>
</template>

<style>
body {
  margin: 0;
  overflow: hidden;
}

.app {
  padding: 20px;
}

nav {
  padding: 20px 0;
}

nav a {
  margin: 0 10px;
  text-decoration: none;
  color: #333;
}

nav a.router-link-active {
  color: #42b983;
}
</style>
