#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
RTSP to MJPEG Proxy Server
将RTSP视频流转换为浏览器可直接显示的MJPEG格式
"""

import os
import sys
import time
import logging
import argparse
import threading
import subprocess
from io import BytesIO
from typing import Dict, List, Optional, Tuple, Union
import json
from datetime import datetime

import cv2
import numpy as np
from PIL import Image

from flask import Flask, Response, request, jsonify, render_template, send_from_directory
from flask_cors import CORS

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# 全局变量
active_streams = {}  # 存储活跃的视频流
app = Flask(__name__)
CORS(app)  # 允许跨域请求
CONFIG_FILE = "stream_config.json"  # 配置文件路径

# 检查FFmpeg是否安装
def check_ffmpeg():
    try:
        subprocess.run(['ffmpeg', '-version'], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        return True
    except FileNotFoundError:
        logger.error("FFmpeg未安装或不在PATH中。请安装FFmpeg。")
        return False

# RTSP流处理类
class RTSPStream:
    def __init__(self, url: str, name: str, fps: int = 15, width: int = 640, height: int = 480):
        self.url = url
        self.name = name
        self.fps = fps
        self.width = width
        self.height = height
        self.frame_interval = 1.0 / fps
        self.running = False
        self.last_frame = None
        self.last_error = None
        self.lock = threading.Lock()
        self.clients = set()
        self.created_at = datetime.now().isoformat()
        self.last_frame_time = None
        self.process = None
        
    def start(self):
        if self.running:
            return
        
        self.running = True
        self.thread = threading.Thread(target=self._capture_frames)
        self.thread.daemon = True
        self.thread.start()
        logger.info(f"已启动RTSP流: {self.name}")
        
    def stop(self):
        self.running = False
        if hasattr(self, 'thread') and self.thread.is_alive():
            self.thread.join(timeout=1.0)
        
        if self.process and self.process.poll() is None:
            self.process.terminate()
            self.process.wait(timeout=1.0)
            
        logger.info(f"已停止RTSP流: {self.name}")
        
    def _capture_frames(self):
        # 使用FFmpeg作为后端
        command = [
            'ffmpeg',
            '-i', self.url,  # 输入RTSP URL
            '-f', 'image2pipe',  # 输出到管道
            '-pix_fmt', 'bgr24',  # 像素格式
            '-vcodec', 'rawvideo',  # 输出原始视频
            '-r', str(self.fps),  # 帧率
            '-s', f"{self.width}x{self.height}",  # 分辨率
            '-'  # 输出到stdout
        ]
        
        try:
            self.process = subprocess.Popen(
                command, 
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                bufsize=10**6  # 降低缓冲区大小以减少内存使用
            )
            
            frame_size = self.width * self.height * 3  # 每帧的大小 (BGR24 = 3 bytes per pixel)
            
            while self.running:
                start_time = time.time()
                
                try:
                    # 读取一帧，添加超时处理
                    raw_frame = self.process.stdout.read(frame_size)
                    if len(raw_frame) != frame_size:
                        if self.process.poll() is not None:
                            error_output = self.process.stderr.read().decode('utf-8', errors='ignore')
                            self.last_error = f"FFmpeg进程终止: {error_output}"
                            logger.error(f"FFmpeg进程终止: {self.name}, 错误: {error_output}")
                        break
                    
                    # 转换为numpy数组，然后为OpenCV格式
                    frame = np.frombuffer(raw_frame, dtype=np.uint8).reshape((self.height, self.width, 3))
                    
                    # 保存帧
                    with self.lock:
                        self.last_frame = frame.copy()
                        self.last_frame_time = datetime.now().isoformat()
                    
                    # 控制帧率
                    elapsed = time.time() - start_time
                    sleep_time = max(0, self.frame_interval - elapsed)
                    if sleep_time > 0:
                        time.sleep(sleep_time)
                except Exception as e:
                    logger.error(f"流 {self.name} 处理帧时出错: {str(e)}")
                    time.sleep(0.1)  # 出错时稍微暂停一下
                    
        except Exception as e:
            self.last_error = str(e)
            logger.error(f"流 {self.name} 捕获错误: {e}")
        finally:
            self.running = False
            if self.process and self.process.poll() is None:
                try:
                    self.process.terminate()
                    self.process.wait(timeout=1.0)
                except:
                    pass
                
    def get_mjpeg_frame(self):
        """获取MJPEG帧的JPEG编码字节"""
        with self.lock:
            if self.last_frame is None:
                # 返回黑色帧
                black_frame = np.zeros((self.height, self.width, 3), dtype=np.uint8)
                # 添加错误文本
                if self.last_error:
                    cv2.putText(
                        black_frame, "Error: " + self.last_error[:50], 
                        (10, self.height // 2), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1
                    )
                _, jpeg_frame = cv2.imencode('.jpg', black_frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
                return jpeg_frame.tobytes()
            
            # 编码为JPEG
            _, jpeg_frame = cv2.imencode('.jpg', self.last_frame, [cv2.IMWRITE_JPEG_QUALITY, 80])
            return jpeg_frame.tobytes()
            
    def add_client(self, client_id):
        self.clients.add(client_id)
        logger.info(f"流 {self.name} 添加客户端: {client_id}, 总客户端: {len(self.clients)}")
        
    def remove_client(self, client_id):
        self.clients.discard(client_id)
        logger.info(f"流 {self.name} 移除客户端: {client_id}, 剩余客户端: {len(self.clients)}")
        return len(self.clients)
        
    def get_status(self):
        return {
            "name": self.name,
            "url": self.url,
            "running": self.running,
            "fps": self.fps,
            "resolution": f"{self.width}x{self.height}",
            "clients": len(self.clients),
            "created_at": self.created_at,
            "last_frame_time": self.last_frame_time,
            "has_error": self.last_error is not None,
            "error": self.last_error
        }

# Flask路由
@app.route('/')
def index():
    return render_template('index.html')

@app.route('/test')
def test_page():
    return render_template('test.html')

@app.route('/api/status')
def api_status():
    """API状态检查"""
    return jsonify({
        "status": "ok",
        "version": "1.0.0",
        "streams_count": len(active_streams),
        "ffmpeg_available": check_ffmpeg(),
        "config_file": os.path.abspath(CONFIG_FILE)
    })

@app.route('/api/streams', methods=['GET'])
def list_streams():
    """列出所有活跃的流"""
    streams_info = {name: stream.get_status() for name, stream in active_streams.items()}
    return jsonify(streams_info)

@app.route('/api/streams/create', methods=['POST'])
def create_stream():
    """创建新的RTSP流"""
    data = request.json
    if not data:
        return jsonify({"success": False, "error": "无效的请求数据"}), 400
    
    url = data.get('url')
    name = data.get('name')
    fps = int(data.get('fps', 15))
    width = int(data.get('width', 640))
    height = int(data.get('height', 480))
    
    if not url or not name:
        return jsonify({"success": False, "error": "缺少必要参数: url 或 name"}), 400
    
    # 检查流是否已存在
    if name in active_streams:
        return jsonify({"success": False, "error": f"流名称已存在: {name}"}), 409
    
    try:
        # 创建新流
        stream = RTSPStream(url, name, fps, width, height)
        active_streams[name] = stream
        stream.start()
        
        # 保存配置
        save_stream_config()
        
        return jsonify({
            "success": True, 
            "message": f"已创建流: {name}", 
            "stream": stream.get_status()
        })
    except Exception as e:
        logger.error(f"创建流错误: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/streams/<name>/stop', methods=['POST'])
def stop_stream(name):
    """停止指定的流"""
    if name not in active_streams:
        return jsonify({"success": False, "error": f"流不存在: {name}"}), 404
    
    try:
        # 检查是否需要从配置中移除
        remove_from_config = request.json.get('remove_from_config', False) if request.json else False
        
        stream = active_streams[name]
        stream.stop()
        del active_streams[name]
        
        # 如果需要，从配置文件中移除
        if remove_from_config:
            remove_stream_from_config(name)
            message = f"已停止流: {name} 并从配置中移除"
        else:
            message = f"已停止流: {name} (保留在配置中)"
        
        return jsonify({"success": True, "message": message})
    except Exception as e:
        logger.error(f"停止流错误: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/streams/<name>/start', methods=['POST'])
def start_stream(name):
    """启动指定的流（从配置中或使用提供的参数）"""
    # 检查流是否已经存在并运行
    if name in active_streams and active_streams[name].running:
        return jsonify({"success": False, "error": f"流 {name} 已经在运行中"}), 409
    
    # 如果是重启已有配置的流
    config = None
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                all_config = json.load(f)
                if name in all_config:
                    config = all_config[name]
        except Exception as e:
            logger.error(f"读取配置文件失败: {str(e)}")
    
    # 如果请求中有参数，优先使用请求参数
    data = request.json or {}
    
    try:
        if name in active_streams:
            # 流存在但已停止，重新启动它
            active_streams[name].start()
            message = f"重新启动流: {name}"
        elif config or data:
            # 创建新流
            params = data if data else config
            if not params:
                return jsonify({"success": False, "error": f"没有找到流 {name} 的配置"}), 404
                
            url = params.get('url')
            fps = int(params.get('fps', 15))
            width = int(params.get('width', 640))
            height = int(params.get('height', 480))
            
            if not url:
                return jsonify({"success": False, "error": "缺少必要参数: url"}), 400
                
            # 创建新流
            stream = RTSPStream(url, name, fps, width, height)
            active_streams[name] = stream
            stream.start()
            
            message = f"已启动流: {name}"
        else:
            return jsonify({"success": False, "error": f"无法启动流 {name}: 没有配置信息"}), 404
        
        return jsonify({"success": True, "message": message, "stream": active_streams[name].get_status()})
    except Exception as e:
        logger.error(f"启动流错误: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/stream/<name>/mjpeg')
def mjpeg_stream(name):
    """提供MJPEG流"""
    if name not in active_streams:
        return Response(
            f"流不存在: {name}",
            status=404,
            mimetype='text/plain'
        )
    
    stream = active_streams[name]
    if not stream.running:
        stream.start()
    
    # 生成唯一的客户端ID
    client_id = request.remote_addr + "_" + str(time.time())
    stream.add_client(client_id)
    
    def generate_frames():
        try:
            while stream.running:
                frame_bytes = stream.get_mjpeg_frame()
                yield (b'--frame\r\n'
                       b'Content-Type: image/jpeg\r\n\r\n' + frame_bytes + b'\r\n')
                time.sleep(stream.frame_interval)
        finally:
            # 客户端断开连接时，从流中移除
            remaining = stream.remove_client(client_id)
            # 如果没有客户端，考虑停止流以节省资源
            if remaining == 0 and name in active_streams:
                logger.info(f"流 {name} 已无客户端连接，准备停止")
                # 这里可以选择停止流或保持运行一段时间
                # 为简单起见，我们保持运行，通过定时任务或管理接口停止空闲流
    
    return Response(
        generate_frames(),
        mimetype='multipart/x-mixed-replace; boundary=frame'
    )

@app.route('/api/config/save', methods=['POST'])
def save_config():
    """手动保存当前流配置"""
    try:
        save_stream_config()
        return jsonify({"success": True, "message": f"成功保存了{len(active_streams)}个流配置"})
    except Exception as e:
        logger.error(f"手动保存配置失败: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

@app.route('/api/config/load', methods=['POST'])
def load_config_api():
    """手动从配置文件加载所有流"""
    try:
        # 计数器
        loaded = 0
        skipped = 0
        
        if not os.path.exists(CONFIG_FILE):
            return jsonify({"success": False, "error": f"配置文件 {CONFIG_FILE} 不存在"}), 404
            
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        for name, stream_config in config.items():
            # 检查是否已存在同名流
            if name in active_streams and active_streams[name].running:
                logger.warning(f"流 {name} 已在运行，跳过加载")
                skipped += 1
                continue
            
            try:
                # 创建并启动流
                url = stream_config.get("url")
                fps = stream_config.get("fps", 15)
                width = stream_config.get("width", 640)
                height = stream_config.get("height", 480)
                
                if name in active_streams:
                    # 如果流存在但已停止，重新启动
                    active_streams[name].start()
                else:
                    # 创建新流
                    stream = RTSPStream(url, name, fps, width, height)
                    active_streams[name] = stream
                    stream.start()
                    
                loaded += 1
                logger.info(f"从配置文件加载并启动流: {name}")
            except Exception as e:
                logger.error(f"加载流 {name} 失败: {str(e)}")
        
        return jsonify({
            "success": True, 
            "message": f"已加载 {loaded} 个流, 跳过 {skipped} 个已运行的流",
            "loaded": loaded,
            "skipped": skipped
        })
    except Exception as e:
        logger.error(f"加载配置失败: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500

# 创建必要的目录和文件
def setup_resources():
    # 创建templates目录
    os.makedirs('templates', exist_ok=True)
    
    # 创建index.html
    with open('templates/index.html', 'w', encoding='utf-8') as f:
        f.write("""
<!DOCTYPE html>
<html>
<head>
    <title>RTSP 代理服务器</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        h2 {
            color: #444;
            margin-top: 20px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .streams-container {
            margin-top: 20px;
        }
        .stream-item {
            border: 1px solid #ddd;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .stream-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .stream-title {
            font-weight: bold;
            font-size: 18px;
        }
        .stream-controls button {
            padding: 5px 10px;
            margin-left: 5px;
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .stream-controls button.start-btn {
            background-color: #4CAF50;
        }
        .stream-preview {
            width: 100%;
            height: 300px;
            background-color: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
        }
        .stream-preview img {
            max-width: 100%;
            max-height: 100%;
        }
        .stream-info {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        .create-form {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .form-row {
            margin-bottom: 10px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="number"] {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button[type="submit"] {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .error {
            color: #f44336;
            margin-top: 5px;
        }
        .success {
            color: #4CAF50;
            margin-top: 5px;
        }
        .config-info {
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .config-btn {
            padding: 5px 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RTSP 代理服务器</h1>
        
        <div class="create-form">
            <h2>创建新的RTSP流</h2>
            <form id="createStreamForm">
                <div class="form-row">
                    <label for="url">RTSP URL:</label>
                    <input type="text" id="url" name="url" placeholder="rtsp://username:password@ip:port/path" required>
                </div>
                <div class="form-row">
                    <label for="name">流名称:</label>
                    <input type="text" id="name" name="name" placeholder="唯一标识符，如camera1" required>
                </div>
                <div class="form-row">
                    <label for="fps">帧率 (FPS):</label>
                    <input type="number" id="fps" name="fps" value="15" min="1" max="30">
                </div>
                <div class="form-row">
                    <label for="width">宽度:</label>
                    <input type="number" id="width" name="width" value="640" min="160" max="1920">
                </div>
                <div class="form-row">
                    <label for="height">高度:</label>
                    <input type="number" id="height" name="height" value="480" min="120" max="1080">
                </div>
                <button type="submit">创建流</button>
                <div id="formMessage"></div>
            </form>
        </div>
        
        <h2>当前活跃流</h2>
        <div class="config-info">
            <p>流配置保存在: <code id="configPath">stream_config.json</code></p>
            <p>启动时会自动加载保存的流。如不希望自动加载，请使用 <code>--no-load-config</code> 参数启动。</p>
            <button id="saveConfigBtn" class="config-btn">手动保存配置</button>
            <button id="loadConfigBtn" class="config-btn">从配置加载流</button>
            <span id="saveConfigMessage"></span>
        </div>
        <div id="streamsContainer" class="streams-container">
            <div>加载中...</div>
        </div>
    </div>

    <script>
        // 获取所有流并显示
        function loadStreams() {
            fetch('/api/streams')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('streamsContainer');
                    container.innerHTML = '';
                    
                    if (Object.keys(data).length === 0) {
                        container.innerHTML = '<div>没有活跃的流</div>';
                        return;
                    }
                    
                    for (const [name, info] of Object.entries(data)) {
                        const streamItem = document.createElement('div');
                        streamItem.className = 'stream-item';
                        
                        const header = document.createElement('div');
                        header.className = 'stream-header';
                        
                        const title = document.createElement('div');
                        title.className = 'stream-title';
                        title.textContent = name;
                        
                        const controls = document.createElement('div');
                        controls.className = 'stream-controls';
                        
                        if (info.running) {
                            const stopButton = document.createElement('button');
                            stopButton.textContent = '停止';
                            stopButton.onclick = () => stopStream(name);
                            controls.appendChild(stopButton);
                        } else {
                            const startButton = document.createElement('button');
                            startButton.textContent = '启动';
                            startButton.className = 'start-btn';
                            startButton.onclick = () => startStream(name);
                            controls.appendChild(startButton);
                        }
                        
                        header.appendChild(title);
                        header.appendChild(controls);
                        
                        const preview = document.createElement('div');
                        preview.className = 'stream-preview';
                        
                        if (info.running) {
                            const img = document.createElement('img');
                            // 添加时间戳防止缓存
                            img.src = `/api/stream/${name}/mjpeg?t=${Date.now()}`;
                            preview.appendChild(img);
                        } else {
                            preview.textContent = '流已停止';
                        }
                        
                        const infoDiv = document.createElement('div');
                        infoDiv.className = 'stream-info';
                        infoDiv.innerHTML = `
                            <div>URL: ${info.url}</div>
                            <div>分辨率: ${info.resolution}</div>
                            <div>FPS: ${info.fps}</div>
                            <div>客户端数: ${info.clients}</div>
                            <div>创建时间: ${new Date(info.created_at).toLocaleString()}</div>
                            ${info.has_error ? `<div style="color:red">错误: ${info.error}</div>` : ''}
                        `;
                        
                        streamItem.appendChild(header);
                        streamItem.appendChild(preview);
                        streamItem.appendChild(infoDiv);
                        
                        container.appendChild(streamItem);
                    }
                })
                .catch(error => {
                    console.error('加载流失败:', error);
                    document.getElementById('streamsContainer').innerHTML = 
                        `<div class="error">加载流失败: ${error.message}</div>`;
                });
        }
        
        // 停止流
        function stopStream(name) {
            if (!confirm(`确定要停止流 "${name}" 吗?`)) return;
            
            // 询问是否从配置中移除
            const removeFromConfig = confirm(`是否要从配置文件中移除流 "${name}"?\n点击"确定"移除, 点击"取消"仅停止但保留配置(推荐)`);
            
            fetch(`/api/streams/${name}/stop`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ remove_from_config: removeFromConfig })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadStreams();
                } else {
                    alert(`停止流失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('停止流错误:', error);
                alert(`停止流错误: ${error.message}`);
            });
        }
        
        // 启动/重启流
        function startStream(name) {
            fetch(`/api/streams/${name}/start`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadStreams();
                } else {
                    alert(`启动流失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('启动流错误:', error);
                alert(`启动流错误: ${error.message}`);
            });
        }
        
        // 创建新流
        document.getElementById('createStreamForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                url: document.getElementById('url').value,
                name: document.getElementById('name').value,
                fps: parseInt(document.getElementById('fps').value),
                width: parseInt(document.getElementById('width').value),
                height: parseInt(document.getElementById('height').value)
            };
            
            fetch('/api/streams/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                const messageDiv = document.getElementById('formMessage');
                if (data.success) {
                    messageDiv.className = 'success';
                    messageDiv.textContent = `成功创建流: ${formData.name}`;
                    // 重置表单
                    document.getElementById('createStreamForm').reset();
                    // 重新加载流列表
                    loadStreams();
                } else {
                    messageDiv.className = 'error';
                    messageDiv.textContent = `创建流失败: ${data.error}`;
                }
            })
            .catch(error => {
                console.error('创建流错误:', error);
                const messageDiv = document.getElementById('formMessage');
                messageDiv.className = 'error';
                messageDiv.textContent = `创建流错误: ${error.message}`;
            });
        });
        
        // 页面加载时获取流
        document.addEventListener('DOMContentLoaded', function() {
            loadStreams();
            // 更新配置文件路径
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('configPath').textContent = data.config_file;
                })
                .catch(error => console.error('获取状态失败:', error));
                
            // 添加保存配置按钮事件处理
            document.getElementById('saveConfigBtn').addEventListener('click', function() {
                fetch('/api/config/save', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    const messageElem = document.getElementById('saveConfigMessage');
                    if (data.success) {
                        messageElem.textContent = data.message;
                        messageElem.style.color = 'green';
                    } else {
                        messageElem.textContent = '保存失败: ' + data.error;
                        messageElem.style.color = 'red';
                    }
                    // 3秒后清除消息
                    setTimeout(() => {
                        messageElem.textContent = '';
                    }, 3000);
                })
                .catch(error => {
                    console.error('保存配置失败:', error);
                    const messageElem = document.getElementById('saveConfigMessage');
                    messageElem.textContent = '保存失败: ' + error.message;
                    messageElem.style.color = 'red';
                });
            });
            
            // 添加加载配置按钮事件处理
            document.getElementById('loadConfigBtn').addEventListener('click', function() {
                fetch('/api/config/load', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    const messageElem = document.getElementById('saveConfigMessage');
                    if (data.success) {
                        messageElem.textContent = data.message;
                        messageElem.style.color = 'green';
                        // 重新加载流列表
                        loadStreams();
                    } else {
                        messageElem.textContent = '加载失败: ' + data.error;
                        messageElem.style.color = 'red';
                    }
                    // 3秒后清除消息
                    setTimeout(() => {
                        messageElem.textContent = '';
                    }, 3000);
                })
                .catch(error => {
                    console.error('加载配置失败:', error);
                    const messageElem = document.getElementById('saveConfigMessage');
                    messageElem.textContent = '加载失败: ' + error.message;
                    messageElem.style.color = 'red';
                });
            });
        });
        
        // 定期刷新流列表
        setInterval(loadStreams, 30000);
    </script>
</body>
</html>
        """)
    
    # 创建test.html
    with open('templates/test.html', 'w', encoding='utf-8') as f:
        f.write("""
<!DOCTYPE html>
<html>
<head>
    <title>RTSP流测试页面</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
        }
        .test-form {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-row {
            margin-bottom: 10px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .preview {
            margin-top: 20px;
            width: 100%;
            height: 400px;
            background-color: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
        }
        .preview img {
            max-width: 100%;
            max-height: 100%;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RTSP流测试页面</h1>
        
        <div class="test-form">
            <div class="form-row">
                <label for="streamName">流名称:</label>
                <input type="text" id="streamName" placeholder="输入已创建的流名称">
            </div>
            <button id="loadStream">加载流</button>
        </div>
        
        <div id="preview" class="preview">
            <div>在此处显示视频流</div>
        </div>
    </div>
    
    <script>
        document.getElementById('loadStream').addEventListener('click', function() {
            const streamName = document.getElementById('streamName').value.trim();
            if (!streamName) {
                alert('请输入流名称');
                return;
            }
            
            const preview = document.getElementById('preview');
            preview.innerHTML = '';
            
            const img = document.createElement('img');
            // 添加时间戳防止缓存
            img.src = `/api/stream/${streamName}/mjpeg?t=${Date.now()}`;
            img.onerror = function() {
                preview.innerHTML = '<div style="color: red">加载流失败，请检查流名称是否正确</div>';
            };
            
            preview.appendChild(img);
        });
    </script>
</body>
</html>
        """)

# 配置文件操作
def save_stream_config():
    """保存当前所有流配置到文件，保留不在active_streams中但存在于文件中的流配置"""
    # 先读取现有配置
    existing_config = {}
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                existing_config = json.load(f)
        except Exception as e:
            logger.error(f"读取现有配置失败: {str(e)}")
    
    # 更新配置（保留不在active_streams中的配置）
    for name, stream in active_streams.items():
        existing_config[name] = {
            "url": stream.url,
            "fps": stream.fps,
            "width": stream.width,
            "height": stream.height
        }
    
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(existing_config, f, indent=4, ensure_ascii=False)
        logger.info(f"已保存配置到 {CONFIG_FILE}，共{len(existing_config)}个流")
    except Exception as e:
        logger.error(f"保存配置文件失败: {str(e)}")

# 仅在停止流时移除该流的配置
def remove_stream_from_config(name):
    """从配置文件中移除指定流"""
    if not os.path.exists(CONFIG_FILE):
        return
    
    try:
        # 读取现有配置
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 移除指定流
        if name in config:
            del config[name]
            logger.info(f"已从配置中移除流: {name}")
            
            # 保存更新后的配置
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=4, ensure_ascii=False)
    except Exception as e:
        logger.error(f"从配置文件移除流失败: {str(e)}")

def load_stream_config():
    """从配置文件加载流"""
    if not os.path.exists(CONFIG_FILE):
        logger.info(f"配置文件 {CONFIG_FILE} 不存在，跳过加载")
        return
    
    try:
        with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        for name, stream_config in config.items():
            # 检查是否已存在同名流
            if name in active_streams:
                logger.warning(f"流 {name} 已存在，跳过加载")
                continue
            
            try:
                # 创建并启动流
                stream = RTSPStream(
                    url=stream_config["url"],
                    name=name,
                    fps=stream_config.get("fps", 15),
                    width=stream_config.get("width", 640),
                    height=stream_config.get("height", 480)
                )
                active_streams[name] = stream
                stream.start()
                logger.info(f"从配置文件加载并启动流: {name}")
            except Exception as e:
                logger.error(f"加载流 {name} 失败: {str(e)}")
        
        logger.info(f"从配置文件成功加载了 {len(active_streams)} 个流")
    except Exception as e:
        logger.error(f"加载配置文件失败: {str(e)}")

def main():
    parser = argparse.ArgumentParser(description='RTSP to MJPEG Proxy Server')
    parser.add_argument('--host', default='0.0.0.0', help='服务器主机地址')
    parser.add_argument('--port', type=int, default=8001, help='服务器端口')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--no-load-config', action='store_true', help='不加载保存的配置')
    
    args = parser.parse_args()
    
    # 检查FFmpeg
    if not check_ffmpeg():
        logger.warning("FFmpeg未安装或不在PATH中，无法处理RTSP流")
    
    # 设置资源
    setup_resources()
    
    # 从配置文件加载流
    if not args.no_load_config:
        load_stream_config()
    
    # 启动服务器
    logger.info(f"RTSP代理服务器启动于 http://{args.host}:{args.port}")
    app.run(host=args.host, port=args.port, debug=args.debug, threaded=True)

if __name__ == '__main__':
    main() 