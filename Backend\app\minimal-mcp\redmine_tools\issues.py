#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from typing import Dict, Any, Optional
from pydantic import BaseModel
from .redmine_config import redmine_config

logger = logging.getLogger(__name__)

class TimeRange(BaseModel):
    start_date: str
    end_date: str

class ProjectFilter(BaseModel):
    project_id: Optional[int] = None
    keyword: Optional[str] = None

async def get_issues(time_range: TimeRange, filter_by: Optional[ProjectFilter] = None) -> Dict[str, Any]:
    """
    获取Redmine问题
    
    获取指定时间范围内创建或更新的问题。可选择按项目ID或关键词过滤。
    """
    try:
        redmine = redmine_config.get_client()
        
        filter_params = {
            'status_id': '*',  # 所有状态
        }
        
        # 添加项目过滤
        if filter_by and filter_by.project_id:
            filter_params['project_id'] = filter_by.project_id
        
        # 获取创建日期在范围内的问题
        created_issues = list(redmine.issue.filter(
            created_on=f'><{time_range.start_date}|{time_range.end_date}',
            **filter_params
        ))
        
        # 获取更新日期在范围内的问题
        updated_issues = list(redmine.issue.filter(
            updated_on=f'><{time_range.start_date}|{time_range.end_date}',
            **filter_params
        ))
        
        # 合并问题列表并去重
        all_issues = {}
        for issue in created_issues + updated_issues:
            all_issues[issue.id] = issue
        
        # 转换为可序列化的字典列表
        result = []
        total_issues = len(all_issues)
        
        for issue in all_issues.values():
            # 如果有关键词过滤，检查主题中是否包含关键词
            if filter_by and filter_by.keyword and filter_by.keyword.lower() not in getattr(issue, 'subject', '').lower():
                continue
                
            data = {
                "id": getattr(issue, 'id', 0),
                "subject": getattr(issue, 'subject', '未知问题'),
                "description": getattr(issue, 'description', ''),
                "created_on": getattr(issue, 'created_on', '').strftime("%Y-%m-%d") if hasattr(issue, 'created_on') else '',
                "updated_on": getattr(issue, 'updated_on', '').strftime("%Y-%m-%d") if hasattr(issue, 'updated_on') else '',
                "done_ratio": getattr(issue, 'done_ratio', 0),
                "status": {
                    "id": getattr(issue.status, 'id', 0),
                    "name": getattr(issue.status, 'name', '未知状态')
                } if hasattr(issue, 'status') else {"id": 0, "name": "未知状态"},
            }
            
            # 添加项目信息
            if hasattr(issue, 'project'):
                data["project"] = {
                    "id": getattr(issue.project, 'id', 0),
                    "name": getattr(issue.project, 'name', '未知项目')
                }
            else:
                data["project"] = {"id": 0, "name": "未知项目"}
            
            # 添加优先级信息
            if hasattr(issue, 'priority'):
                data["priority"] = {
                    "id": getattr(issue.priority, 'id', 0),
                    "name": getattr(issue.priority, 'name', '普通')
                }
            else:
                data["priority"] = {"id": 0, "name": "普通"}
            
            # 添加作者信息
            if hasattr(issue, 'author'):
                data["author"] = {
                    "id": getattr(issue.author, 'id', 0),
                    "name": getattr(issue.author, 'name', '未知用户')
                }
            else:
                data["author"] = {"id": 0, "name": "未知用户"}
            
            # 添加指派人信息
            if hasattr(issue, 'assigned_to'):
                data["assigned_to"] = {
                    "id": getattr(issue.assigned_to, 'id', 0),
                    "name": getattr(issue.assigned_to, 'name', '未知用户')
                }
            else:
                data["assigned_to"] = None
                
            result.append(data)
        
        return {
            "issues": result,
            "count": len(result),
            "filteredOut": total_issues - len(result)
        }
        
    except Exception as e:
        logger.error(f"获取问题失败: {str(e)}")
        raise Exception(f"获取问题失败: {str(e)}")

async def get_issue_statuses() -> Dict[str, Any]:
    """
    获取Redmine问题状态
    
    获取所有可用的问题状态。
    """
    try:
        redmine = redmine_config.get_client()
        
        # 获取所有问题状态
        statuses = list(redmine.issue_status.all())
        
        # 转换为可序列化的字典列表
        result = []
        
        for status in statuses:
            data = {
                "id": getattr(status, 'id', 0),
                "name": getattr(status, 'name', '未知'),
                "is_closed": getattr(status, 'is_closed', False)
            }
            
            # 添加其他可能的属性
            if hasattr(status, 'default_done_ratio'):
                data["default_done_ratio"] = status.default_done_ratio
                
            result.append(data)
        
        return {
            "statuses": result,
            "count": len(result)
        }
        
    except Exception as e:
        logger.error(f"获取Redmine问题状态失败: {str(e)}")
        raise Exception(f"获取Redmine问题状态失败: {str(e)}")

async def create_issue(
    project_id: int, 
    subject: str, 
    description: str = "", 
    assigned_to_id: Optional[int] = None, 
    tracker_id: Optional[int] = None,
    status_id: Optional[int] = None,
    priority_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    创建Redmine问题
    
    在Redmine中创建一个新问题。
    """
    try:
        redmine = redmine_config.get_client()
        
        # 准备创建问题的参数
        issue_params = {
            'project_id': project_id,
            'subject': subject,
            'description': description
        }
        
        # 添加可选参数
        if assigned_to_id:
            issue_params['assigned_to_id'] = assigned_to_id
        
        if tracker_id:
            issue_params['tracker_id'] = tracker_id
            
        if status_id:
            issue_params['status_id'] = status_id
            
        if priority_id:
            issue_params['priority_id'] = priority_id
        
        # 创建问题
        issue = redmine.issue.create(**issue_params)
        
        # 返回创建的问题信息
        return {
            "status": "success",
            "issue": {
                "id": issue.id,
                "subject": issue.subject,
                "description": issue.description if hasattr(issue, 'description') else '',
                "created_on": issue.created_on.strftime("%Y-%m-%d %H:%M:%S") if hasattr(issue, 'created_on') else '',
                "url": f"{redmine_config.url}/issues/{issue.id}"
            }
        }
        
    except Exception as e:
        logger.error(f"创建Redmine问题失败: {str(e)}")
        raise Exception(f"创建Redmine问题失败: {str(e)}")

async def get_trackers() -> Dict[str, Any]:
    """
    获取Redmine跟踪器
    
    获取系统中定义的所有跟踪器（Tracker）。
    """
    try:
        redmine = redmine_config.get_client()
        
        # 获取所有跟踪器
        trackers = list(redmine.tracker.all())
        
        # 转换为可序列化的字典列表
        result = []
        
        for tracker in trackers:
            data = {
                "id": getattr(tracker, 'id', 0),
                "name": getattr(tracker, 'name', '未知')
            }
            
            # 添加其他可能的属性
            if hasattr(tracker, 'default_status'):
                try:
                    data["default_status"] = {
                        "id": tracker.default_status.id,
                        "name": tracker.default_status.name
                    }
                except:
                    pass
                
            result.append(data)
        
        return {
            "trackers": result,
            "count": len(result)
        }
        
    except Exception as e:
        logger.error(f"获取Redmine跟踪器失败: {str(e)}")
        raise Exception(f"获取Redmine跟踪器失败: {str(e)}") 