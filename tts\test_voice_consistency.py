#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音色一致性 - 验证每次生成的音频是否使用完全相同的音色
"""

import requests
import base64
import time
import hashlib

# TTS服务地址
TTS_URL = "http://localhost:8003"

def test_voice_consistency():
    """测试音色一致性"""
    print("=== 测试音色一致性 ===")
    
    # 测试文本
    test_text = "这是音色一致性测试，每次生成的音频应该使用完全相同的音色。"
    
    print(f"测试文本: {test_text}")
    print("生成5次音频，检查音色是否完全一致...")
    
    audio_hashes = []
    
    for i in range(5):
        print(f"\n第 {i+1} 次生成:")
        
        try:
            # 发送TTS请求
            response = requests.post(
                f"{TTS_URL}/tts",
                json={
                    "text": test_text,
                    "preprocess_text": True
                },
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result["success"]:
                    # 保存音频文件
                    audio_data = base64.b64decode(result["audio_base64"])
                    output_file = f"consistency_test_{i+1}.wav"
                    
                    with open(output_file, "wb") as f:
                        f.write(audio_data)
                    
                    # 计算音频数据的哈希值
                    audio_hash = hashlib.md5(audio_data).hexdigest()
                    audio_hashes.append(audio_hash)
                    
                    print(f"  音频已保存: {output_file}")
                    print(f"  音频时长: {result['duration']:.2f}秒")
                    print(f"  音频哈希: {audio_hash[:16]}...")
                    print(f"  消息: {result['message']}")
                else:
                    print(f"  生成失败: {result['message']}")
                    return False
            else:
                print(f"  请求失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  测试出错: {e}")
            return False
        
        # 等待1秒再进行下一次测试
        time.sleep(1)
    
    # 分析一致性
    print(f"\n=== 一致性分析 ===")
    print(f"生成了 {len(audio_hashes)} 个音频文件")
    
    if len(set(audio_hashes)) == 1:
        print("✅ 音色完全一致！所有音频的哈希值相同")
        print(f"统一哈希值: {audio_hashes[0]}")
        return True
    else:
        print("❌ 音色不一致！发现不同的音频哈希值:")
        for i, hash_val in enumerate(audio_hashes):
            print(f"  第{i+1}次: {hash_val}")
        
        # 统计相同哈希值的数量
        hash_counts = {}
        for hash_val in audio_hashes:
            hash_counts[hash_val] = hash_counts.get(hash_val, 0) + 1
        
        print(f"\n哈希值统计:")
        for hash_val, count in hash_counts.items():
            print(f"  {hash_val}: {count}次")
        
        return False

def test_different_texts():
    """测试不同文本的音色一致性"""
    print("\n=== 测试不同文本的音色一致性 ===")
    
    test_texts = [
        "短文本测试",
        "这是一个中等长度的文本测试，包含更多的内容。",
        "点击查看完整的作业区生产油井A2日报，这是一个包含特殊字符的长文本测试。"
    ]
    
    for i, text in enumerate(test_texts):
        print(f"\n测试文本 {i+1}: {text}")
        
        try:
            response = requests.post(
                f"{TTS_URL}/tts",
                json={
                    "text": text,
                    "preprocess_text": True
                },
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result["success"]:
                    audio_data = base64.b64decode(result["audio_base64"])
                    output_file = f"different_text_{i+1}.wav"
                    
                    with open(output_file, "wb") as f:
                        f.write(audio_data)
                    
                    print(f"  音频已保存: {output_file}")
                    print(f"  音频时长: {result['duration']:.2f}秒")
                    print(f"  消息: {result['message']}")
                else:
                    print(f"  生成失败: {result['message']}")
            else:
                print(f"  请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"  测试出错: {e}")
        
        time.sleep(1)

def test_service_config():
    """测试服务配置"""
    print("\n=== 检查服务配置 ===")
    
    try:
        # 检查健康状态
        response = requests.get(f"{TTS_URL}/health")
        if response.status_code == 200:
            result = response.json()
            print(f"服务状态: {result['status']}")
            print(f"模型已加载: {result.get('model_loaded', 'N/A')}")
            print(f"使用设备: {result.get('device', 'N/A')}")
            print(f"默认音色: {result.get('default_voice', 'N/A')}")
            print(f"固定音色种子: {result.get('fixed_voice_seed', 'N/A')}")
            print(f"固定音频种子: {result.get('fixed_audio_seed', 'N/A')}")
        
        # 检查音色配置
        response = requests.get(f"{TTS_URL}/voices")
        if response.status_code == 200:
            result = response.json()
            print(f"\n当前音色配置: {result['default_voice']}")
            config = result["current_config"]
            print("配置参数:")
            for key, value in config.items():
                print(f"  {key}: {value}")
        
        return True
    except Exception as e:
        print(f"无法连接到服务: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试TTS音色固定功能...")
    
    # 检查服务配置
    if not test_service_config():
        print("TTS服务未运行或配置有误")
        return
    
    # 测试音色一致性
    consistency_result = test_voice_consistency()
    
    # 测试不同文本
    test_different_texts()
    
    print("\n=== 测试总结 ===")
    if consistency_result:
        print("✅ 音色固定功能正常，所有音频使用相同音色")
    else:
        print("❌ 音色固定功能异常，需要进一步调试")
    
    print("\n请播放生成的音频文件，人工验证音色是否一致：")
    print("- consistency_test_*.wav: 相同文本的多次生成")
    print("- different_text_*.wav: 不同文本的音色对比")

if __name__ == "__main__":
    main()
