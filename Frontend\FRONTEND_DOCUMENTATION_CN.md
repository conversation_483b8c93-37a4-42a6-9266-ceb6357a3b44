# 前端实现技术文档

## 项目概述

本项目是一个基于Vue3和Three.js的智能油田数字孪生平台，实现了油井设备的3D可视化监控、实时数据展示和功图分析等功能。通过MQTT协议实时获取设备数据，将物联网数据与3D场景有机结合，提供直观的设备状态监控。

## 技术栈

- **前端框架**：Vue 3 + Vite
- **状态管理**：Pinia
- **3D引擎**：Three.js
- **通信协议**：MQTT (WebSocket)
- **图表可视化**：ECharts

## 系统架构

### 数据流向

```
设备数据 → MQTT服务器 → 前端MQTT客户端 → Pinia状态管理 → UI组件渲染
```

### 核心模块

1. **3D场景模块**：基于Three.js实现数字孪生场景
2. **设备数据模块**：处理MQTT实时数据
3. **设备监控面板**：展示油井参数和功图分析
4. **视频监控模块**：集成设备视频流
5. **预警分析模块**：异常状态监测与提示

## MQTT数据通信实现

### 连接配置

MQTT客户端通过WebSocket连接到服务器：

```javascript
// MQTT服务器连接配置
const options = {
  protocol: 'ws',
  port: 5710,
  path: '/mqtt',           
  hostname: '127.0.0.1', 
  wsOptions: {
    rejectUnauthorized: false
  }
};
```

### 设备数据订阅

```javascript
// 选中设备后订阅对应主题
selectDevice(device) {
  if (device && device.topic) {
    mqttClient.subscribe(device.topic)
  }
}
```

### 数据处理流程

1. 接收MQTT消息
2. 数据清洗和格式化
3. 提取关键指标（油压、套压等）
4. 更新UI组件状态

## Three.js 3D场景实现

### 场景初始化

```javascript
function initBaseScene() {
  scene = new THREE.Scene()
  camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 10000)
  renderer = new THREE.WebGLRenderer({ antialias: true })
  
  // 后处理效果
  composer = new EffectComposer(renderer)
  outlinePass = new OutlinePass(
    new THREE.Vector2(width, height), scene, camera
  )
}
```

### 模型加载与处理

使用GLTFLoader加载模型，并为设备添加交互属性：

```javascript
// 加载模型
loader.load(modelPath, (gltf) => {
  scene.add(gltf.scene)
  
  // 遍历所有子对象，设置交互属性
  gltf.scene.traverse((child) => {
    if (child.name?.includes('JH')) {
      child.userData = {
        wellname: child.name,
        station: 'HN3S1',
        topic: `data/oilwell/${child.name}`
      }
    }
  })
})
```

### 设备名称与MQTT主题对应关系

#### 可点击的对象类型

3D场景中主要有以下几种可点击的对象类型：

1. **抽油机组对象**：对象名称格式为 `组xxx`，例如：
   - `组001`
   - `组002` 
   - ...
   - `组008`

2. **普通设备对象**：通过代码创建的设备对象

#### 3D模型命名约定

3D模型的命名约定是设备识别和数据关联的关键基础：

```javascript
// 遍历场景，标记抽油机组
gltf.scene.traverse((node) => {
  // 检查是否是抽油机组（直接检查名称是否匹配"组xxx"格式）
  const groupMatch = node.name?.match(/组(\d{3})/)
  if (groupMatch) {
    const groupNumber = groupMatch[1]
    node.userData.isPumpJack = true
    node.userData.groupNumber = groupNumber
    node.userData.id = `pumpjack_${groupNumber}`
    node.userData.wellname = `JH${groupNumber}`
    node.userData.station = "HN3S1"
    node.userData.topic = `/HN3S1/JH${groupNumber}`
    console.log(`找到抽油机组: ${node.name}`)
    // ...
  }
})
```

这段代码揭示了以下关键信息：

1. 3D模型文件（GLTF格式）本身就包含了对象命名，抽油机组在模型中被命名为"组001"、"组002"等
2. 代码使用正则表达式`node.name?.match(/组(\d{3})/)`来识别这些特定命名的对象
3. 当找到名称匹配"组xxx"模式的对象时，系统将其标识为抽油机组并添加相应的属性

这表明3D模型的创建者（可能是3D美工或开发人员）在制作模型时已经按照"组xxx"的格式命名了抽油机组对象，而前端代码则按照这个命名约定来识别和处理这些对象。

因此，这部分功能实现依赖于3D模型的命名规范，如果模型命名发生变化，相应的代码逻辑也需要调整。

#### 设备对象数据映射

系统通过userData属性将3D对象映射到实际设备：

```javascript
// 抽油机组映射
node.userData = {
  isPumpJack: true,
  groupNumber: groupNumber,  // 001, 002 等
  id: `pumpjack_${groupNumber}`,
  wellname: `JH${groupNumber}`,  // JH001, JH002 等
  station: "HN3S1",
  topic: `/HN3S1/JH${groupNumber}`
}

// 一般设备映射
device.userData = {
  station: "HN3S1",
  wellname: `HN15V${index + 1}`,  // HN15V1, HN15V2 等
  id: `HN15V${index + 1}`,
  topic: `/HN3S1/HN15V${index + 1}`
}
```

#### MQTT主题命名规则

系统根据设备类型使用不同的MQTT主题格式：

1. **抽油机组**：
   - 格式：`/HN3S1/JH{xxx}`
   - 示例：`/HN3S1/JH001`, `/HN3S1/JH002`, ...

2. **普通设备**：
   - 格式：`/HN3S1/HN15V{x}`
   - 示例：`/HN3S1/HN15V1`, `/HN3S1/HN15V2`, ...

#### 设备查找机制

系统支持多种格式查找设备：

```javascript
function findDeviceByName(deviceName) {
  if (!deviceName) return null
  
  let formattedName = deviceName
  
  // 处理不同格式的设备ID
  if (deviceName.startsWith('JH')) {
    // JH005 -> 组005
    formattedName = `组${deviceName.slice(2).padStart(3, '0')}`
  } else if (deviceName.match(/^\d+$/)) {
    // 纯数字 -> 组xxx
    formattedName = `组${deviceName.padStart(3, '0')}`
  }
  
  // 在场景对象中查找匹配的设备
  let foundDevice = null
  
  sceneObjects.forEach(obj => {
    obj.traverse((node) => {
      // 检查节点名称是否匹配
      if (node.name === formattedName || 
          (node.userData && (
            node.userData.wellname === deviceName || 
            node.userData.id === deviceName ||
            node.name?.includes(formattedName)
          ))) {
        foundDevice = node
      }
    })
  })
  
  return foundDevice
}
```

这种灵活的命名和查找机制，使系统能够将不同格式的设备标识与3D模型对象和MQTT主题有效关联起来。

### 交互实现

通过射线检测（Raycaster）实现设备选择：

```javascript
function onClick(event) {
  raycaster.setFromCamera(mouse, camera)
  const intersects = raycaster.intersectObjects(scene.children, true)
  
  if (intersects.length > 0) {
    const targetObject = intersects[0].object
    
    // 高亮选中设备
    outlinePass.selectedObjects = [targetObject]
    
    // 显示设备面板
    devicePanelPosition.value = { x, y, visible: true }
    
    // 选中设备并订阅数据
    deviceStore.selectDevice({
      wellname: targetObject.userData.wellname,
      topic: targetObject.userData.topic
    })
  }
}
```

## 设备面板实现

### 面板组件结构

```vue
<template>
  <div class="device-panel" :style="panelStyle">
    <!-- 设备名称和控制按钮 -->
    <div class="panel-header">
      <h3>{{ deviceInfo.WELL_COMMON_NAME }}</h3>
    </div>

    <div class="panel-content">
      <!-- 状态面板 - 显示油压、套压等参数 -->
      <div class="status-panel">...</div>
      
      <!-- 电气参数 -->
      <div class="electrical-params">...</div>
      
      <!-- 功图分析 -->
      <div class="card-section">
        <div class="card-container" ref="chartRef">
          <!-- ECharts 功图渲染区域 -->
        </div>
      </div>
    </div>
  </div>
</template>
```

### 功图渲染

使用ECharts实现功图绘制：

```javascript
function renderChart() {
  // 获取上下行程数据
  const upstroke = []
  const downstroke = []
  
  // 创建功图配置
  const option = {
    grid: { left: 40, right: 10, top: 30, bottom: 30 },
    xAxis: { type: 'value', name: '位移(m)' },
    yAxis: { type: 'value', name: '载荷(kN)' },
    series: [
      {
        name: '上行程',
        type: 'line',
        data: upstroke,
        showSymbol: false,
        lineStyle: { color: '#1890ff', width: 2.5 }
      },
      {
        name: '下行程',
        type: 'line',
        data: downstroke,
        lineStyle: { color: '#f5222d', width: 2.5 }
      }
    ]
  }
  
  chart.setOption(option)
}
```

## 项目配置说明

### 设备配置

在config.json中定义设备列表：

```json
{
  "mqtt": {
    "host": "127.0.0.1",
    "port": 5710,
    "path": "/mqtt"
  },
  "devices": [
    { "station": "HN3S1", "wellname": "JH005" },
    { "station": "HN3S1", "wellname": "JH006" },
    // 其他设备...
  ]
}
```

## 开发指南

### 添加新设备

1. 在config.json中添加设备配置
2. 在3D模型中添加相应设备并命名
3. 在设备数据处理逻辑中添加特定参数映射

### 自定义功图显示

可以修改`DevicePanel.vue`中的`renderChart`函数，自定义功图显示样式和数据处理逻辑。

### 添加新的数据指标

1. 在deviceStore中添加新的数据属性
2. 在MQTT数据处理中增加对应字段的映射
3. 在UI组件中添加新指标的显示元素

## 性能优化建议

1. 3D模型优化：减少多边形数量，使用LOD技术
2. 使用WebWorker处理复杂数据
3. 按需加载场景和设备模型
4. 针对移动设备优化渲染设置

## 部署指南

1. 构建生产版本：`npm run build`
2. 配置环境变量：调整`.env.production`中的MQTT服务器地址
3. 配置Nginx服务器支持WebSocket连接

## 故障排除

1. MQTT连接问题
   - 检查MQTT服务器地址和端口配置
   - 确认WebSocket路径是否正确
   
2. 3D场景加载失败
   - 检查模型文件路径
   - 确认Three.js版本兼容性
   
3. 设备数据不更新
   - 检查MQTT主题订阅
   - 查看浏览器控制台错误信息 