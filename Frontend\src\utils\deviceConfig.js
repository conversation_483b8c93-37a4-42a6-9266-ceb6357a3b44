import config from '../components/Scene/config.json';

/**
 * 设备配置管理
 * 负责加载设备配置并提供查找功能
 */
class DeviceConfigManager {
  constructor() {
    this.devicesByModelName = new Map();
    this.devicesByTopic = new Map();
    this.devicesByDisplayName = new Map();
    this.loadConfig();
  }

  /**
   * 加载设备配置
   */
  loadConfig() {
    if (!config || !config.devices || !Array.isArray(config.devices)) {
      console.error('设备配置格式错误');
      return;
    }

    // 清空现有映射
    this.devicesByModelName.clear();
    this.devicesByTopic.clear();
    this.devicesByDisplayName.clear();

    // 构建映射关系
    config.devices.forEach(device => {
      if (device.model_name) {
        this.devicesByModelName.set(device.model_name, device);
      }
      if (device.topic) {
        this.devicesByTopic.set(device.topic, device);
      }
      if (device.display_name) {
        this.devicesByDisplayName.set(device.display_name, device);
      }
    });

    console.log(`已加载 ${config.devices.length} 个设备配置`);
  }

  /**
   * 根据模型名称查找设备配置
   * @param {string} modelName - 模型名称，例如"组001"
   * @returns {Object|null} 设备配置对象
   */
  findByModelName(modelName) {
    return this.devicesByModelName.get(modelName) || null;
  }

  /**
   * 根据MQTT主题查找设备配置
   * @param {string} topic - MQTT主题
   * @returns {Object|null} 设备配置对象
   */
  findByTopic(topic) {
    return this.devicesByTopic.get(topic) || null;
  }

  /**
   * 根据显示名称查找设备配置
   * @param {string} displayName - 显示名称，例如"井01"
   * @returns {Object|null} 设备配置对象
   */
  findByDisplayName(displayName) {
    return this.devicesByDisplayName.get(displayName) || null;
  }

  /**
   * 通用查找方法，尝试多种方式查找设备
   * @param {string} query - 查询字符串，可以是模型名称、主题或显示名称
   * @returns {Object|null} 设备配置对象
   */
  findDevice(query) {
    if (!query) return null;

    // 尝试各种可能的查找方式
    let device = this.findByModelName(query);
    if (device) return device;

    device = this.findByTopic(query);
    if (device) return device;

    device = this.findByDisplayName(query);
    if (device) return device;

    // 尝试处理特殊格式
    // 例如输入"JH001"时尝试查找对应的模型名称"组001"
    if (query.startsWith('JH') && query.length > 2) {
      const groupNumber = query.substring(2);
      const modelName = `组${groupNumber.padStart(3, '0')}`;
      device = this.findByModelName(modelName);
      if (device) return device;
    }
    
    // 处理"海南17-5"格式井名
    const hnMatch = query.match(/海南(\d+)-(\d+)/i);
    if (hnMatch) {
      // 遍历所有设备，查找display_name匹配的设备
      for (const [_, deviceConfig] of this.devicesByModelName) {
        if (deviceConfig.display_name && 
            deviceConfig.display_name.includes(query)) {
          return deviceConfig;
        }
      }
      
      // 如果没有精确匹配，尝试使用数字部分查找
      const wellNumber = `${hnMatch[1]}-${hnMatch[2]}`;
      for (const [_, deviceConfig] of this.devicesByModelName) {
        if (deviceConfig.display_name && 
            deviceConfig.display_name.includes(wellNumber)) {
          return deviceConfig;
        }
      }
    }

    // 纯数字尝试转换为"组xxx"格式
    if (/^\d+$/.test(query)) {
      const modelName = `组${query.padStart(3, '0')}`;
      device = this.findByModelName(modelName);
      if (device) return device;
    }

    return null;
  }

  /**
   * 获取所有设备配置
   * @returns {Array} 设备配置数组
   */
  getAllDevices() {
    return config.devices || [];
  }
}

// 创建单例实例
const deviceConfigManager = new DeviceConfigManager();

export default deviceConfigManager; 