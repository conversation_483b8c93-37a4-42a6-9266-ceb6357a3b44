import random
import math
import time
from datetime import datetime


class DataGenerator:
    def __init__(self):
        self.status = 0
        self.Dyna_points = 200,
        self.base_values = {
            'ADL': 87.5,  # A相电流
            'BDL': 32.5,  # B相电流
            'CDL': 92.1,  # C相电流
            'ADY': 387,  # A相电压
            'BDY': 376,  # B相电压
            'CDY': 392,  # C相电压
            'SLV': 4.7,  # 冲程
            'CHC': 5,  # 冲次
            'GYS': 0.78,  # 功率因数
            'ZYG': 3586,  # 总有功功率
            'ZWG': 2070,  # 总无功功率
            'UWL': 49.6,  # 最大载荷
            'DWL': 22.1,  # 最小载荷
            'WBR': 76,  # 平衡度
            'YGL': 568754,  # 有功用电量
            'CPV': 1.3,  # 套压
            'WIP': 1.4,  # 井口油压
            'TWT': 57,  # 井口油温
        }

    def generate_curve_data(self):
        """生成功图曲线数据"""
        points = self.Dyna_points[0]
        # 生成位移数据(0-4的等间距序列)
        displacement = []
        slv = self.base_values['SLV'] + random.uniform(-1, 1)
        step = slv / (points / 2)

        current = 0.0
        for _ in range(int(points/2)):
            displacement.append(f"{current:.2f}")
            current += step

        # 下行程
        current = slv
        for _ in range(points-int(points/2)):
            displacement.append(f"{current:.2f}")
            current -= step

        # 生成载荷数据(正弦曲线+随机波动)
        offset = []
        # 上行程
        # 首先确认上升过程占比
        up_ratio = random.uniform(0.2, 0.4)
        up_points = int((points / 2) * up_ratio)
        # 生成上升过程的数据，生成数据递增
        for i in range(up_points):
            base = 22 + 15 * math.sin((math.pi / 2) * (i / up_points))
            noise = random.uniform(-2, 2)
            offset.append(f"{base + noise:.5f}")
        # 生成上升后平稳阶段的数据
        for i in range(int(points / 2 - up_points)):
            base = 37 + 3 * math.sin(2 * math.pi * i / random.randint(20, 50))
            noise = random.uniform(-2, 2)
            offset.append(f"{base + noise:.5f}")
        # 生成快速下降过程的数据，生成的数据递减
        down_ratio = random.uniform(0.2, 0.4)
        down_points = int((points / 2) * down_ratio)
        for i in range(down_points):
            base = 40 - 20 * math.sin((math.pi / 2) * (i / down_points))
            noise = random.uniform(-2, 2)
            offset.append(f"{base + noise:.5f}")
        # 生成下降后平稳阶段的数据
        for i in range(int(points / 2 - down_points)):
            base = 20 + 5 * math.sin(2 * math.pi * i / random.randint(20, 50))
            noise = random.uniform(-2, 2)
            offset.append(f"{base + noise:.5f}")

        # 生成电流数据(正弦曲线+随机波动)
        currentload = []
        # 上行程
        # 首先确认上升过程占比
        up_ratio = random.uniform(0.2, 0.4)
        up_points = int((points / 2) * up_ratio)
        # 生成上升过程的数据，生成数据递增
        for i in range(up_points):
            base = 87 + 30 * math.sin((math.pi / 2) * (i / up_points))
            noise = random.uniform(-2, 2)
            currentload.append(f"{base + noise:.5f}")
        # 生成上升后平稳阶段的数据
        for i in range(int(points / 2 - up_points)):
            base = 117 + 3 * math.sin(2 * math.pi * i / random.randint(20, 50))
            noise = random.uniform(-2, 2)
            currentload.append(f"{base + noise:.5f}")
        # 生成快速下降过程的数据，生成的数据递减
        down_ratio = random.uniform(0.2, 0.4)
        down_points = int((points / 2) * down_ratio)
        for i in range(down_points):
            base = 117 - 30 * math.sin((math.pi / 2) * (i / down_points))
            noise = random.uniform(-2, 2)
            currentload.append(f"{base + noise:.5f}")
        # 生成下降后平稳阶段的数据
        for i in range(int(points / 2 - down_points)):
            base = 87 + 5 * math.sin(2 * math.pi * i / random.randint(20, 50))
            noise = random.uniform(-2, 2)
            currentload.append(f"{base + noise:.5f}")

        return '|'.join(displacement), '|'.join(currentload), '|'.join(offset)

    def generate_values(self):
        """生成仪表数据"""
        values = []
        for key, base in self.base_values.items():
            # 添加随机波动
            noise = random.randint(80, 120) / 100.0
            value = base * noise
            values.append({
                "id": key,
                "Value": value
            })
        return values

    def update_status(self, new_status):
        """更新设备状态"""
        self.status = new_status

    def generate_data(self, station="HN3S1", wellname="HN15V25"):
        """生成完整的数据包"""
        payload, currentload, offset = self.generate_curve_data()
        data = {
            "Station": station,
            "Wellname": wellname,
            "Status": self.status,
            "Check_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "Dyna_points": self.Dyna_points,
            "Disp_load": offset,
            "Disp_current": currentload,
            "Displacement": payload,
            "Values": self.generate_values()
        }
        return data
