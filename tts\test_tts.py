#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChatTTS服务测试脚本
"""

import requests
import base64
import json
import time
from pathlib import Path

# 服务配置
TTS_SERVICE_URL = "http://localhost:8003"

def test_health_check():
    """测试健康检查接口"""
    print("测试健康检查接口...")
    try:
        response = requests.get(f"{TTS_SERVICE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 健康检查通过: {data}")
            return True
        else:
            print(f"✗ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 健康检查异常: {e}")
        return False

def test_tts_api(text="你好，这是一个测试语音。"):
    """测试TTS API接口"""
    print(f"测试TTS API接口，文本: {text}")
    
    try:
        # 准备请求数据
        request_data = {
            "text": text,
            "voice_seed": 42,
            "temperature": 0.3,
            "top_p": 0.7,
            "top_k": 20,
            "refine_text": True
        }
        
        print("发送TTS请求...")
        start_time = time.time()
        
        response = requests.post(
            f"{TTS_SERVICE_URL}/tts",
            json=request_data,
            timeout=60  # 60秒超时
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ TTS请求成功，处理时间: {processing_time:.2f}秒")
            print(f"  - 音频时长: {data.get('duration', 0):.2f}秒")
            print(f"  - 采样率: {data.get('sample_rate', 0)} Hz")
            
            # 保存音频文件
            if data.get('audio_base64'):
                audio_bytes = base64.b64decode(data['audio_base64'])
                output_file = Path("test_output.wav")
                with open(output_file, "wb") as f:
                    f.write(audio_bytes)
                print(f"  - 音频已保存到: {output_file}")
                
            return True
        else:
            print(f"✗ TTS请求失败: {response.status_code}")
            print(f"  错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ TTS请求异常: {e}")
        return False

def test_tts_stream(text="这是流式音频测试。"):
    """测试流式TTS接口"""
    print(f"测试流式TTS接口，文本: {text}")
    
    try:
        request_data = {
            "text": text,
            "voice_seed": 42,
            "temperature": 0.3,
            "top_p": 0.7,
            "top_k": 20,
            "refine_text": True
        }
        
        print("发送流式TTS请求...")
        start_time = time.time()
        
        response = requests.post(
            f"{TTS_SERVICE_URL}/tts/stream",
            json=request_data,
            timeout=60
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if response.status_code == 200:
            print(f"✓ 流式TTS请求成功，处理时间: {processing_time:.2f}秒")
            
            # 保存音频文件
            output_file = Path("test_stream_output.wav")
            with open(output_file, "wb") as f:
                f.write(response.content)
            print(f"  - 流式音频已保存到: {output_file}")
            
            return True
        else:
            print(f"✗ 流式TTS请求失败: {response.status_code}")
            print(f"  错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ 流式TTS请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("ChatTTS服务测试")
    print("=" * 50)
    
    # 等待服务启动
    print("等待服务启动...")
    time.sleep(2)
    
    # 测试健康检查
    if not test_health_check():
        print("服务未正常运行，请检查服务状态")
        return
    
    print("\n" + "-" * 30)
    
    # 测试TTS API
    test_tts_api("你好，我是ChatTTS语音合成系统。今天天气很好。")
    
    print("\n" + "-" * 30)
    
    # 测试流式TTS
    test_tts_stream("这是一个流式音频测试，用于验证流式接口的功能。")
    
    print("\n" + "=" * 50)
    print("测试完成")

if __name__ == "__main__":
    main()
