#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from typing import Dict, Any
from .redmine_config import redmine_config

logger = logging.getLogger(__name__)

async def get_users() -> Dict[str, Any]:
    """
    获取Redmine用户
    
    获取所有用户的基本信息。
    """
    try:
        redmine = redmine_config.get_client()
        
        users = list(redmine.user.all())
        
        # 转换为可序列化的字典列表
        result = []
        
        for user in users:
            data = {
                "id": getattr(user, 'id', 0),
                "name": getattr(user, 'name', '未知用户'),
                "login": getattr(user, 'login', ''),
                "mail": getattr(user, 'mail', ''),
                "last_login_on": getattr(user, 'last_login_on', '').strftime("%Y-%m-%d %H:%M") if hasattr(user, 'last_login_on') and user.last_login_on is not None else '',
                "created_on": getattr(user, 'created_on', '').strftime("%Y-%m-%d") if hasattr(user, 'created_on') else '',
            }
                
            result.append(data)
        
        return {
            "users": result,
            "count": len(result)
        }
        
    except Exception as e:
        logger.error(f"获取用户失败: {str(e)}")
        raise Exception(f"获取用户失败: {str(e)}") 