#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import yaml
import logging
from redminelib import Redmine
from datetime import datetime

# 配置日志
logger = logging.getLogger(__name__)

class RedmineConfig:
    """Redmine配置类"""
    def __init__(self):
        """初始化Redmine配置"""
        # 尝试在多个位置查找配置文件
        config_paths = [
            os.path.join(os.getcwd(), 'config', 'config.yaml'),
            os.path.abspath(os.path.join(os.path.dirname(__file__), '..', 'config', 'config.yaml'))
        ]
        
        config_found = False
        for config_path in config_paths:
            if os.path.exists(config_path):
                logger.info(f"找到配置文件: {config_path}")
                # 加载配置文件
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                # 获取Redmine配置
                redmine_config = config.get('redmine', {})
                self.url = redmine_config.get('url', "https://rm.978543210.com/")
                self.api_key = redmine_config.get('api_key', "af21ef3a9a7df53a944ea71e1eed587540871fca")
                self.verify_ssl = redmine_config.get('verify_ssl', False)
                config_found = True
                break
        
        # 如果配置文件不存在，使用默认设置
        if not config_found:
            logger.warning(f"未找到配置文件，使用默认设置")
            self.url = "https://rm.978543210.com/"
            self.api_key = "af21ef3a9a7df53a944ea71e1eed587540871fca"
            self.verify_ssl = False
        
        logger.info(f"Redmine URL: {self.url}")

    def get_client(self):
        """获取Redmine客户端"""
        return Redmine(
            self.url,
            key=self.api_key,
            requests={'verify': self.verify_ssl}
        )
    
    def get_config_info(self):
        """获取配置信息"""
        return {
            "url": self.url,
            "verify_ssl": self.verify_ssl,
            "api_key_set": bool(self.api_key)
        }
        
    def get_status(self):
        """获取Redmine状态"""
        try:
            redmine = self.get_client()
            
            # 尝试获取当前用户信息，用于测试连接
            current_user = redmine.user.get('current')
            
            return {
                "status": "connected",
                "url": self.url,
                "user": {
                    "id": getattr(current_user, 'id', 0),
                    "name": getattr(current_user, 'name', '未知用户'),
                    "login": getattr(current_user, 'login', '')
                },
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            logger.error(f"获取Redmine状态失败: {str(e)}")
            return {
                "status": "error",
                "message": str(e),
                "url": self.url,
                "time": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

# 创建全局Redmine配置实例
redmine_config = RedmineConfig() 