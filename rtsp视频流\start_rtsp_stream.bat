@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo RTSP流推送工具 - 将MP4文件推送为RTSP流
echo =======================================

:: 检查ffmpeg是否安装
where ffmpeg >nul 2>nul
if %errorlevel% neq 0 (
    echo 错误：未找到ffmpeg。请先安装ffmpeg并添加到系统环境变量中。
    goto :end
)

:: 设置RTSP服务器地址和凭据
set "rtsp_server=localhost"
set "rtsp_port=8554"
set "rtsp_username=admin"
set "rtsp_password=admin123"

:: 当前目录下的MP4文件
set "video1=%~dp01.mp4"
set "video2=%~dp02.mp4"
set "video3=%~dp0video1.mp4"
set "video4=%~dp0video2.mp4"
set "video5=%~dp0video3.mp4"

:: 确保RTSP代理服务器正在运行
echo 请确保RTSP代理服务器已经启动（使用 ..\rtsp_proxy\start_proxy.bat）
echo.
echo 按任意键开始推送视频流...
pause >nul

:: 启动五个单独的窗口推送视频流
echo 正在启动视频流1...
start "RTSP流1" cmd /c "chcp 65001 >nul && ffmpeg -re -stream_loop -1 -i "%video1%" -c copy -f rtsp rtsp://%rtsp_username%:%rtsp_password%@%rtsp_server%:%rtsp_port%/stream1"

echo 正在启动视频流2...
start "RTSP流2" cmd /c "chcp 65001 >nul && ffmpeg -re -stream_loop -1 -i "%video2%" -c copy -f rtsp rtsp://%rtsp_username%:%rtsp_password%@%rtsp_server%:%rtsp_port%/stream2"

echo 正在启动视频流3...
start "RTSP流3" cmd /c "chcp 65001 >nul && ffmpeg -re -stream_loop -1 -i "%video3%" -c copy -f rtsp rtsp://%rtsp_username%:%rtsp_password%@%rtsp_server%:%rtsp_port%/stream3"

echo 正在启动视频流4...
start "RTSP流4" cmd /c "chcp 65001 >nul && ffmpeg -re -stream_loop -1 -i "%video4%" -c copy -f rtsp rtsp://%rtsp_username%:%rtsp_password%@%rtsp_server%:%rtsp_port%/stream4"

echo 正在启动视频流5...
start "RTSP流5" cmd /c "chcp 65001 >nul && ffmpeg -re -stream_loop -1 -i "%video5%" -c copy -f rtsp rtsp://%rtsp_username%:%rtsp_password%@%rtsp_server%:%rtsp_port%/stream5"

echo.
echo 所有视频流已启动！
echo RTSP流地址：
echo rtsp://%rtsp_username%:%rtsp_password%@%rtsp_server%:%rtsp_port%/stream1
echo rtsp://%rtsp_username%:%rtsp_password%@%rtsp_server%:%rtsp_port%/stream2
echo rtsp://%rtsp_username%:%rtsp_password%@%rtsp_server%:%rtsp_port%/stream3
echo rtsp://%rtsp_username%:%rtsp_password%@%rtsp_server%:%rtsp_port%/stream4
echo rtsp://%rtsp_username%:%rtsp_password%@%rtsp_server%:%rtsp_port%/stream5
echo.
echo 要停止所有流，请关闭已打开的命令窗口或按Ctrl+C
echo.

:end
pause 