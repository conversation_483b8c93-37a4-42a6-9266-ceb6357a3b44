"""
Main entry point for the RAG system with local API documentation.
"""

from fastapi import FastAPI, Depends, Request, WebSocket, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import time
import uvicorn
import logging
import sys
from pathlib import Path
from datetime import datetime
from contextlib import asynccontextmanager
import asyncio
from loguru import logger
from .routes import websocket_routes
from .websockets.manager import manager
from .utils.alert_generator import alert_generator  # 导入告警生成器实例
from .utils.log_config import setup_logging  # 导入新的日志配置

# 添加项目根目录到 Python 路径
sys.path.append(str(Path(__file__).parent.parent))

# 导入其他依赖模块
from app.websockets.manager import manager
from app.routes.websocket_routes import router as websocket_router

# 配置日志系统
setup_logging(
    level=logging.ERROR,  # 只输出错误
    log_file=str(Path(__file__).parent.parent / "logs" / "application.log")
)

logger = logging.getLogger(__name__)

# 全局变量
global_monitor_task = None
MONITOR_INTERVAL = 60  # 全局监控间隔（秒）
SIMULATE_DATA_INTERVAL = 60  # 模拟数据生成间隔（秒）

# 定时任务：全局设备监控
async def global_device_monitor():
    """定时监控所有设备并广播警报"""
    data_simulation_time = 0
    
    while True:
        try:
            # 首先处理MQTT线程产生的实时警报队列
            await alert_generator.process_realtime_alerts()
            
            # logger.info("[全局监控] 开始执行定时监控任务")
            
            # 执行全局监控并获取警报
            alerts = await alert_generator.monitor_all_devices()
            
            if alerts:
                # logger.info(f"[全局监控] 监控到 {len(alerts)} 个警报，准备广播")
                # 通过WebSocket广播给所有客户端
                await manager.broadcast_alerts(alerts)
                # logger.info("[全局监控] 警报广播完成")
            else:
                # logger.info("[全局监控] 未检测到异常，无需发送警报")
                pass
                
        except Exception as e:
            logger.error(f"[全局监控] 执行监控任务时出错: {str(e)}")
            
        # 等待下一次监控
        await asyncio.sleep(MONITOR_INTERVAL)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("IOT Agent API 服务启动")
    logger.info(f"服务启动于: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 连接到MQTT服务器获取实时数据
    try:
        logger.info("连接到MQTT服务器...")
        mqtt_connected = alert_generator.connect_to_mqtt(
            host="************", 
            port=8083, 
            path="/mqtt", 
            use_websocket=True
        )
        if mqtt_connected:
            logger.info("MQTT服务器连接成功，将从实际设备获取数据")
        else:
            logger.warning("MQTT服务器连接失败，将无法获取实时设备数据")
    except Exception as e:
        logger.error(f"连接MQTT服务器时出错: {str(e)}")
    
    # 启动全局监控任务
    global global_monitor_task
    global_monitor_task = asyncio.create_task(global_device_monitor())
    logger.info(f"[全局监控] 全局设备监控已启动，监控间隔: {MONITOR_INTERVAL}秒")
    
    yield
    
    # 关闭时执行
    logger.info("IOT Agent API 服务关闭")
    
    # 停止全局监控任务
    if global_monitor_task:
        global_monitor_task.cancel()
        logger.info("[全局监控] 全局设备监控已停止")
    
    # 断开MQTT连接
    try:
        if alert_generator.mqtt_client and alert_generator.mqtt_connected:
            logger.info("[MQTT] 断开MQTT连接")
            alert_generator.mqtt_client.disconnect()
            logger.info("[MQTT] MQTT连接已断开")
    except Exception as e:
        logger.error(f"[MQTT] 断开MQTT连接时出错: {str(e)}")

# 创建 FastAPI 应用
app = FastAPI(
    title="IOT Agent API",
    description="物联网智能体API，提供数据分析、查询和知识库检索功能",
    version="0.1.0",
    lifespan=lifespan
)

# 定义异常处理中间件函数
@app.middleware("http")
async def handle_exceptions(request: Request, call_next):
    try:
        return await call_next(request)
    except Exception as e:
        # 捕获所有异常，包括图片处理错误
        error_msg = str(e)
        if "cannot unpack non-iterable Image object" in error_msg:
            # 图片处理错误特殊处理
            logger.error(f"图片处理错误: {error_msg}")
            return JSONResponse(
                status_code=500,
                content={"message": "图片处理错误，请检查文档内图片格式", "error": error_msg}
            )
        # 其他通用错误
        logger.error(f"服务器错误: {error_msg}")
        return JSONResponse(
            status_code=500,
            content={"message": "服务器内部错误", "error": error_msg}
        )

# 添加中间件 - 注意顺序很重要
# 1. 先添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
    expose_headers=["*"]
)

# 设置控制日志输出的状态变量
verbose_logging = False

# 在app中添加日志控制函数
@app.get("/api/toggle_logging")
async def toggle_logging(enable: bool = True):
    """切换详细日志记录的API端点"""
    global verbose_logging
    verbose_logging = enable
    
    # 设置WebSocket路由的日志级别
    from .routes.websocket_routes import set_verbose_logging as set_ws_routes_logging
    set_ws_routes_logging(enable)
    
    # 修改WebSocket manager的日志级别
    from .websockets.manager import manager
    manager.set_verbose_logging(enable)
    
    return {"status": "success", "verbose_logging": enable}

# 添加WebSocket路由 - 直接挂载到根路径，绕过CORS和其他中间件限制
for route in websocket_router.routes:
    app.routes.append(route)
logger.info("已添加WebSocket路由")

@app.get("/", include_in_schema=False)
async def root():
    """根路径处理"""
    return {"message": "欢迎使用IOT Agent API"}

@app.get("/api/health", tags=["System"])
async def health_check():
    """健康检查接口"""
    return {
        "status": "online",
        "services": {
            "database": True,
            "vector_store": True,
            "file_storage": True,
        },
        "timestamp": time.time()
    }

# 如果直接运行此文件，则启动服务
if __name__ == "__main__":
    uvicorn.run("app.main:app", host="0.0.0.0", port=8000, reload=True)
