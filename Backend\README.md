# IOT Agent 后端系统

## 概述
基于 FastAPI 的物联网智能体后端系统，专门用于处理和分析物联网设备数据。系统集成了WebSocket实时通信、智能聊天代理、MQTT数据处理等功能，用于高效的物联网数据分析和设备管理。系统使用多智能体协作方式构建，通过LangGraph实现智能体间的协调工作。

## 技术栈
- **核心框架**: FastAPI
- **ASGI 服务器**: Uvicorn
- **AI/ML 组件**:
  - LangChain
  - LangChain Community
  - LangChain Core
  - LangGraph
  - Ollama (本地LLM)
- **通信协议**:
  - WebSockets (实时通信)
  - MQTT (物联网设备通信)
- **工具库**:
  - Loguru (日志记录)
  - Redis (可选，用于缓存)
  - MySQL (可选，用于数据持久化)

## 项目结构
```
Backend/
├── app/                         # 应用主目录
│   ├── __init__.py
│   ├── main.py                  # FastAPI应用入口
│   ├── agents/                  # 智能体模块
│   │   ├── __init__.py
│   │   ├── iot_chat_agents.py   # 物联网聊天智能体
│   │   ├── mysql_agent.py       # MySQL数据处理智能体
│   │   ├── multi_agents.py      # 多智能体协作
│   │   └── question_classifier_agent.py # 问题分类智能体 
│   ├── core/                    # 核心功能模块
│   │   └── __init__.py
│   ├── routes/                  # 路由模块
│   │   └── websocket_routes.py  # WebSocket和API路由处理
│   ├── tools/                   # 工具集合
│   │   ├── __init__.py
│   │   ├── mqtt_data_tool.py    # MQTT数据处理工具
│   │   └── rag_data_tool.py     # RAG数据处理工具
│   ├── utils/                   # 工具类和辅助函数
│   │   ├── __init__.py
│   │   ├── alert_generator.py   # 告警生成器
│   │   └── db/                  # 数据库相关工具
│   └── websockets/              # WebSocket处理
│       └── manager.py           # WebSocket连接管理器
├── mqtt_alert_monitor.py        # MQTT独立监控脚本
├── uploads/                     # 上传文件存储
├── venv/                        # 虚拟环境
├── .env.example                 # 环境变量示例
├── Dockfile                     # Docker构建文件 
├── requirements.txt             # 依赖清单
└── README.md                    # 项目说明文档
```

## 主要功能

1. **物联网数据处理**
   - MQTT设备数据接收和处理
   - 实时数据分析与监控
   - 异常检测和告警生成
   - 设备状态追踪

2. **智能聊天**
   - 基于LangGraph的多智能体协作
   - 问题分类智能体 (区分知识库查询、设备数据查询等)
   - 设备状态查询和数据分析
   - 问题诊断和建议

3. **实时通信**
   - WebSocket实时数据推送
   - 设备状态更新和告警通知
   - 摄像头控制命令传输
   - 心跳检测与连接维护

4. **安全特性**
   - CORS 配置
   - 异常处理
   - 详细日志记录
   - 可配置的日志级别

## 核心模块说明

### 智能体模块 (app/agents/)
- **iot_chat_agents.py**: 实现基于LangGraph的智能体协作流程，包括问题分类、知识库查询、设备数据查询等功能
- **mysql_agent.py**: 专门处理与MySQL数据库交互的智能体
- **question_classifier_agent.py**: 问题分类智能体，将用户问题分为知识库查询、设备数据查询或混合查询

### 工具模块 (app/tools/)
- **mqtt_data_tool.py**: 提供MQTT数据处理功能，从设备获取实时数据
- **rag_data_tool.py**: 提供RAG（检索增强生成）数据处理功能

### 工具类 (app/utils/)
- **alert_generator.py**: 负责生成设备告警，包括阈值检测、异常分析等

### 通信模块
- **websockets/manager.py**: 管理所有WebSocket连接，实现消息广播等功能
- **routes/websocket_routes.py**: 定义WebSocket和API路由，处理不同类型的消息和请求

## 安装与配置

### 环境要求
- Python 3.8+
- Ollama (本地LLM)
- MQTT Broker

### 安装步骤
1. 创建并激活虚拟环境：
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/Mac
   venv\Scripts\activate     # Windows
   ```

2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

3. 配置环境变量：
   ```bash
   cp .env.example .env
   # 编辑 .env 文件进行配置
   ```

### 运行应用

#### 开发模式
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### 生产模式
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

## WebSocket 接口

### 警报接口
- 连接：`/alerts`
- 消息类型：
  - 告警通知 (alert)
  - 设备状态更新 (device_data_processed)
  - 心跳消息 (heartbeat)
  - 测试消息 (test_message)

### 客户端消息示例
```json
{
  "type": "device_data",
  "device_id": "JH001",
  "data": {
    "温度": 75.5,
    "压力": 0.85,
    "电流": 1.2
  }
}
```

### 服务器响应示例
```json
{
  "type": "alert",
  "title": "温度过高警报",
  "content": "设备JH001温度达到75.5°C，超过正常阈值",
  "alertType": "warning",
  "timestamp": "2023-05-01T12:34:56",
  "id": "alert-123456",
  "device": {
    "id": "JH001" 
  }
}
```

## API 接口
- 健康检查：`/api/health`
- 日志控制：`/api/toggle_logging`
- 获取设备状态：`/api/devices/status`
- 摄像头控制：`/camera/move`
- 广播摄像头告警：`/broadcast/camera_alert`
- 测试接口：
  - `/api/test/alert` - 生成测试告警
  - `/api/test/device_data` - 提交测试设备数据

## 独立MQTT监控
系统包含一个独立的MQTT监控脚本 `mqtt_alert_monitor.py`，可以单独运行以监控MQTT设备数据，支持WebSocket通信。

## 错误处理
- 全局异常处理器
- 结构化错误响应
- 使用 Loguru 详细日志记录
- WebSocket连接异常处理

## 开发指南

### 代码规范
- 遵循 PEP 8
- 使用类型提示
- 所有函数都需要文档字符串

### 日志记录
- 可配置详细程度（verbose_logging）
- 系统级日志和WebSocket连接日志
- 告警和错误日志

## 贡献指南
1. Fork 仓库
2. 创建特性分支
3. 实现更改并添加测试
4. 提交拉取请求

## 许可证
MIT License - 详见 LICENSE 文件
