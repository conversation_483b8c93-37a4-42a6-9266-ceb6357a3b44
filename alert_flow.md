```mermaid
flowchart TD
    %% 设备和数据源
    Device[设备数据源] -->|发送设备数据| Backend
    
    %% 后端处理流程
    subgraph Backend[后端处理]
        MQTTData[MQTT数据工具] -->|处理设备数据| AlertGenerator
        AlertGenerator["告警生成器<br/>alert_generator.py"] -->|检查数据阈值| Threshold{"数据是否超出阈值?"}
        Threshold -->|是| GenerateAlert[生成警报消息]
        Threshold -->|否| NoAlert[不生成警报]
        
        GenerateAlert -->|警报消息| WSManager["WebSocket管理器<br/>manager.py"]
        NoAlert --> End[结束处理]
        
        WSRoutes["WebSocket路由<br/>websocket_routes.py"] -->|广播消息| WSManager
    end
    
    %% 前端处理流程
    subgraph Frontend[前端处理]
        WSConnection[WebSocket连接] -->|接收警报消息| WSHandler[警报消息处理]
        WSHandler -->|更新| WSStore["WebSocket存储<br/>websocketStore.js"]
        WSHandler -->|显示警报| AlertDialog["警报对话框组件<br/>AlertDialog.vue"]
        
        WSStore -->|提供警报数据| AlertDialog
    end
    
    %% 连接后端和前端
    WSManager -->|广播警报| WSConnection
    
    %% 用户交互
    User[用户] -->|查看| AlertDialog
    AlertDialog -->|显示| User
    User -->|关闭警报| AlertDialog
    
    %% 其他触发来源
    ManualTrigger["手动触发<br/>(API调用)"] -->|生成自定义警报| WSRoutes
    CameraSystem[摄像头系统] -->|检测异常| WSRoutes
    
    %% 流程说明
    classDef backend fill:#f9f,stroke:#333,stroke-width:2px
    classDef frontend fill:#bbf,stroke:#333,stroke-width:2px
    classDef user fill:#bfb,stroke:#333,stroke-width:2px
    classDef device fill:#fbb,stroke:#333,stroke-width:2px
    
    class Backend,MQTTData,AlertGenerator,Threshold,GenerateAlert,NoAlert,WSManager,WSRoutes,End backend
    class Frontend,WSConnection,WSHandler,WSStore,AlertDialog frontend
    class User user
    class Device,ManualTrigger,CameraSystem device
``` 