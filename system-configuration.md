# IoT Agent MVP2 系统配置文档

## 1. 系统架构概览

### 1.1 核心组件
- **后端服务**: FastAPI (端口: 8000)
- **前端应用**: Vue 3 + Vite
- **智能体系统**: 基于LangGraph的多智能体协作
- **消息队列**: MQTT Broker (WebSocket: 8083, TCP: 1884)
- **大语言模型**: Ollama (qwen2.5:32b)
- **外部服务**: RAGFlow、Redmine、Gitea

### 1.2 数据流架构

```mermaid
graph TB
    A[前端Vue应用] -->|HTTP/WebSocket| B[FastAPI后端]
    B -->|MQTT WebSocket| C[MQTT Broker]
    C -->|设备数据| D[IoT设备]
    B -->|API调用| E[RAGFlow服务]
    B -->|HTTP API| F[Redmine系统]
    B -->|HTTP API| G[Gitea代码仓库]
    B -->|本地调用| H[Ollama LLM]
    B -->|WebSocket广播| I[实时警报系统]
    
    subgraph "智能体系统"
        J[问题分类智能体]
        K[MySQL智能体]
        L[IoT聊天智能体]
        M[多智能体协作]
    end
    
    B --> J
    J --> K
    J --> L
    K --> M
    L --> M
```

## 2. 外部服务配置清单

### 2.1 MQTT服务配置

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 主机地址 | ************ | MQTT Broker服务器 | `.env.ascii`, `config.json` |
| WebSocket端口 | 8083 | 前端WebSocket连接 | 多个配置文件 |
| TCP端口 | 1884 | 后端TCP连接备用 | `mqtt_discovery.py` |
| WebSocket路径 | /mqtt | WebSocket连接路径 | 全局配置 |
| 客户端ID前缀 | alert-monitor- | 后端客户端标识 | `alert_generator.py` |
| 用户名 | 111 | 前端连接认证 | `mqtt.js` |

**连接方式**:
- 前端: WebSocket (ws://************:8083/mqtt)
- 后端: WebSocket优先，TCP备用

### 2.2 RAGFlow服务配置

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 基础URL | 127.0.0.1 | RAGFlow服务地址 | 代码中硬编码 |
| API密钥 | ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG | API认证 | `api.js` |
| 端点路径 | /api/v1/chats_openai/{chat_id}/chat/completions | 对话API | 后端代理 |
| 认证方式 | Bearer Token | HTTP头认证 | API调用 |

### 2.3 Redmine项目管理系统

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 服务URL | https://rm.978543210.com/ | Redmine服务器 | `config.yaml` |
| API密钥 | af21ef3a9a7df53a944ea71e1eed587540871fca | API认证 | `config.yaml` |
| SSL验证 | false | 跳过SSL证书验证 | `config.yaml` |
| 超时设置 | 默认 | HTTP请求超时 | 使用默认值 |

### 2.4 Gitea代码仓库

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 服务URL | https://git.978543210.com/ | Gitea服务器 | `config.yaml` |
| API密钥 | 2f6bd626dc3fddc403ec580c5639ed7091c88320 | API认证 | `config.yaml` |
| SSL验证 | false | 跳过SSL证书验证 | `config.yaml` |

### 2.5 Ollama大语言模型

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 模型名称 | qwen2.5:32b | 主要对话模型 | `iot_chat_agents.py` |
| 温度参数 | 0.7 | 生成随机性控制 | 智能体配置 |
| 连接方式 | 本地调用 | 直接调用本地服务 | LangChain配置 |

## 3. 系统内部配置

### 3.1 FastAPI应用配置

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 应用标题 | IOT Agent API | API文档标题 | `main.py` |
| 版本号 | 0.1.0 | API版本 | `main.py` |
| 监听地址 | 0.0.0.0 | 服务绑定地址 | 启动参数 |
| 监听端口 | 8000 | HTTP服务端口 | 启动参数 |
| 重载模式 | True | 开发模式热重载 | 启动参数 |

### 3.2 数据库配置

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 数据库URL | sqlite:///./app.db | SQLite数据库文件 | `.env.ascii` |
| 连接池 | 默认 | 数据库连接管理 | 默认配置 |

### 3.3 日志配置

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 日志级别 | ERROR | 默认日志级别 | `main.py` |
| 日志文件 | logs/application.log | 日志文件路径 | `log_config.py` |
| 详细日志 | 可切换 | 运行时控制 | `/api/toggle_logging` |
| 警报日志 | TRUE | 警报日志开关 | `.env.ascii` |

## 4. 网络和安全配置

### 4.1 CORS配置
- **允许来源**: 所有来源 (*)
- **允许方法**: GET, POST, PUT, DELETE, OPTIONS
- **允许头部**: 所有头部
- **凭据支持**: 启用

### 4.2 WebSocket安全
- **认证方式**: 无认证（开发环境）
- **连接验证**: 始终允许
- **跨域策略**: 允许所有来源

### 4.3 API安全
- **认证机制**: Bearer Token (RAGFlow)
- **API密钥**: 配置文件存储
- **SSL验证**: 外部服务禁用（开发环境）

## 5. 设备监控配置

### 5.1 设备阈值设置

| 参数类型 | 最小值 | 最大值 | 单位 | 说明 |
|----------|--------|--------|------|------|
| 温度 | -50 | 500 | °C | 极宽范围，几乎不触发警报 |
| 压力 | -10 | 100 | MPa | 极宽范围设置 |
| 电流 | -100 | 1000 | A | 极宽范围设置 |
| 振动 | -100 | 1000 | mm | 极宽范围设置 |

### 5.2 监控设备列表
- **HN系列**: HN15V1-HN15V10, HN15V24-HN15V26, HN15V67
- **JH系列**: JH001-JH009
- **监控间隔**: 可配置
- **数据存储**: 内存缓存 + 历史记录

## 6. 智能体系统配置

### 6.1 智能体类型和功能

| 智能体名称 | 功能描述 | 配置参数 | 文件位置 |
|------------|----------|----------|----------|
| 问题分类智能体 | 将用户问题分类为知识库查询、设备数据查询或混合查询 | 模型: qwen2.5:32b | `question_classifier_agent.py` |
| MySQL智能体 | 处理数据库查询和操作 | 数据库连接配置 | `mysql_agent.py` |
| IoT聊天智能体 | 处理物联网相关对话和设备交互 | LangGraph状态管理 | `iot_chat_agents.py` |
| 多智能体协作 | 协调多个智能体的工作流程 | 工具集成配置 | `multi_agents.py` |

### 6.2 智能体工作流程

```mermaid
graph LR
    A[用户输入] --> B[问题分类智能体]
    B -->|知识库查询| C[知识库响应智能体]
    B -->|设备数据查询| D[MQTT响应智能体]
    B -->|混合查询| E[协作智能体]
    B -->|直接回答| F[直接回答智能体]
    C --> G[最终响应]
    D --> G
    E --> G
    F --> G
```

### 6.3 工具集成配置

| 工具名称 | 功能 | 配置位置 | 集成方式 |
|----------|------|----------|----------|
| MQTT数据工具 | 获取实时设备数据 | `mqtt_data_tool.py` | 直接集成 |
| RAG数据工具 | 知识库检索增强生成 | `rag_data_tool.py` | API调用 |
| Tavily搜索工具 | 网络搜索功能 | `multi_agents.py` | 第三方API |
| Python REPL工具 | 代码执行和图表生成 | `multi_agents.py` | 本地执行 |

## 7. 数据流处理配置

### 7.1 WebSocket消息类型

| 消息类型 | 方向 | 数据格式 | 处理逻辑 |
|----------|------|----------|----------|
| connection_status | 服务器→客户端 | `{"type": "connection_status", "status": "connected"}` | 连接确认 |
| device_data | 客户端→服务器 | `{"type": "device_data", "device_id": "JH001", "data": {...}}` | 设备数据处理 |
| alert | 服务器→客户端 | `{"type": "alert", "title": "...", "content": "..."}` | 警报广播 |
| test_message | 双向 | `{"type": "test_message", "deviceId": "JH001"}` | 测试功能 |
| heartbeat | 服务器→客户端 | `{"type": "heartbeat", "timestamp": "..."}` | 连接保活 |

### 7.2 MQTT主题订阅

| 主题模式 | 用途 | 订阅者 | 数据格式 |
|----------|------|--------|----------|
| `HN3S2/+/+/data` | 海南站点设备数据 | 后端监控 | JSON格式设备数据 |
| `$SYS/#` | 系统状态信息 | 设备发现 | MQTT系统消息 |
| `device/+/status` | 设备状态更新 | 前后端 | 状态信息 |

### 7.3 数据处理管道

```mermaid
graph TD
    A[MQTT设备数据] --> B[数据接收]
    B --> C[数据验证]
    C --> D[阈值检查]
    D -->|正常| E[数据存储]
    D -->|异常| F[生成警报]
    F --> G[WebSocket广播]
    E --> H[历史数据分析]
    G --> I[前端显示]
    H --> J[趋势分析]
```

## 8. 错误处理和重试机制

### 8.1 连接重试配置

| 服务类型 | 重试次数 | 重试间隔 | 超时时间 | 降级策略 |
|----------|----------|----------|----------|----------|
| MQTT连接 | 无限重试 | 5秒递增 | 60秒 | TCP备用连接 |
| WebSocket | 自动重连 | 1秒起始 | 30秒 | 连接状态通知 |
| HTTP API | 3次 | 1秒 | 10秒 | 错误响应返回 |
| 数据库连接 | 5次 | 2秒 | 30秒 | 服务降级 |

### 8.2 异常处理策略

| 异常类型 | 处理方式 | 日志级别 | 用户通知 |
|----------|----------|----------|----------|
| 网络连接失败 | 自动重试 | WARNING | 连接状态提示 |
| 数据格式错误 | 跳过处理 | ERROR | 错误消息返回 |
| 认证失败 | 停止服务 | CRITICAL | 认证错误提示 |
| 资源不足 | 限流处理 | WARNING | 服务繁忙提示 |

## 9. 性能和监控配置

### 9.1 系统监控指标

| 监控项 | 阈值 | 检查频率 | 告警方式 |
|--------|------|----------|----------|
| CPU使用率 | >80% | 30秒 | 日志记录 |
| 内存使用率 | >85% | 30秒 | 日志记录 |
| WebSocket连接数 | >100 | 实时 | 连接管理 |
| MQTT消息频率 | >1000/分钟 | 实时 | 限流处理 |

### 9.2 缓存配置

| 缓存类型 | 存储位置 | 过期时间 | 清理策略 |
|----------|----------|----------|----------|
| 设备数据缓存 | 内存 | 5分钟 | LRU淘汰 |
| 警报历史 | 内存 | 1小时 | 定时清理 |
| WebSocket消息 | 内存 | 10分钟 | 自动清理 |
| 用户会话 | 内存 | 30分钟 | 超时清理 |

## 10. 部署配置

### 10.1 Docker配置
- **容器名称**: ag-python
- **基础镜像**: python:3.12
- **端口映射**: 8010:8000
- **网络**: agent-network (***********)

### 10.2 环境变量
```bash
APP_ENV=development
SECRET_KEY=your_secret_key_here
DATABASE_URL=sqlite:///./app.db
API_TITLE=IOT_Device_Monitoring_System
API_VERSION=0.1.0
MQTT_HOST=************
MQTT_PORT=8083
MQTT_PATH=/mqtt
ALERT_LOGGING_ENABLED=TRUE
ALERT_LOG_LEVEL=INFO
MQTT_USE_WEBSOCKET=TRUE
```

### 10.3 启动命令

**开发模式**:
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**生产模式**:
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

**Docker启动**:
```bash
docker-compose up -d
```

## 11. API端点配置

### 11.1 系统管理API

| 端点 | 方法 | 功能 | 认证要求 |
|------|------|------|----------|
| `/api/health` | GET | 系统健康检查 | 无 |
| `/api/toggle_logging` | GET | 切换日志级别 | 无 |
| `/` | GET | 根路径欢迎信息 | 无 |

### 11.2 设备管理API

| 端点 | 方法 | 功能 | 认证要求 |
|------|------|------|----------|
| `/api/devices/status` | GET | 获取所有设备状态 | 无 |
| `/api/devices/{device_id}` | GET | 获取指定设备信息 | 无 |
| `/api/test/device_data` | POST | 模拟设备数据测试 | 无 |

### 11.3 警报管理API

| 端点 | 方法 | 功能 | 认证要求 |
|------|------|------|----------|
| `/api/alerts` | GET | 获取所有警报信息 | 无 |
| `/api/alerts/recent` | GET | 获取最近警报信息 | 无 |
| `/api/alerts/acknowledge` | POST | 确认警报 | 无 |
| `/camera/alert` | POST | 发送摄像头警报 | 无 |
| `/camera/move` | POST | 摄像头移动请求 | 无 |

### 11.4 WebSocket端点

| 端点 | 协议 | 功能 | 认证要求 |
|------|------|------|----------|
| `/alerts` | WebSocket | 实时警报推送 | 无 |

## 12. 配置文件位置汇总

### 12.1 后端配置文件

| 文件名 | 路径 | 用途 | 格式 |
|--------|------|------|------|
| `.env.ascii` | `Backend/.env.ascii` | 环境变量配置 | ENV |
| `config.yaml` | `Backend/app/minimal-mcp/config/config.yaml` | 外部服务配置 | YAML |
| `config.json` | `Backend/app/minimal-mcp/mqtt_tools/config.json` | MQTT设备配置 | JSON |
| `requirements.txt` | `Backend/requirements.txt` | Python依赖 | TXT |
| `docker-compose.yml` | `Deploy/docker-compose.yml` | Docker配置 | YAML |

### 12.2 前端配置文件

| 文件名 | 路径 | 用途 | 格式 |
|--------|------|------|------|
| `api.js` | `Frontend/src/config/api.js` | API配置 | JS |
| `package.json` | `Frontend/package.json` | 依赖和脚本 | JSON |
| `vite.config.js` | `Frontend/vite.config.js` | 构建配置 | JS |

## 13. 安全配置建议

### 13.1 生产环境安全配置

| 配置项 | 开发环境 | 生产环境建议 |
|--------|----------|--------------|
| SSL验证 | false | true |
| CORS来源 | * | 指定域名 |
| API密钥 | 明文存储 | 环境变量 |
| 日志级别 | DEBUG/INFO | WARNING/ERROR |
| WebSocket认证 | 无 | Token验证 |

### 13.2 敏感信息管理

| 信息类型 | 当前存储方式 | 建议改进 |
|----------|--------------|----------|
| API密钥 | 配置文件 | 环境变量或密钥管理服务 |
| 数据库密码 | 配置文件 | 加密存储 |
| MQTT凭据 | 明文 | 加密传输 |
| 会话密钥 | 固定值 | 动态生成 |

## 14. 故障排除指南

### 14.1 常见问题和解决方案

| 问题类型 | 症状 | 可能原因 | 解决方案 |
|----------|------|----------|----------|
| MQTT连接失败 | 设备数据无法接收 | 网络问题或配置错误 | 检查网络连接和配置参数 |
| WebSocket断开 | 前端无法接收实时数据 | 网络不稳定或服务重启 | 自动重连机制 |
| API调用失败 | 外部服务无响应 | 服务不可用或认证失败 | 检查服务状态和认证信息 |
| 内存泄漏 | 系统性能下降 | 缓存未及时清理 | 重启服务或调整缓存策略 |

### 14.2 日志分析

| 日志类型 | 位置 | 关键字段 | 分析要点 |
|----------|------|----------|----------|
| 应用日志 | `logs/application.log` | timestamp, level, message | 错误模式和频率 |
| MQTT日志 | `logs/mqtt_monitor.log` | device_id, status, data | 设备连接状态 |
| WebSocket日志 | 控制台输出 | connection_id, message_type | 连接质量 |

## 15. 维护和更新

### 15.1 定期维护任务

| 任务 | 频率 | 执行方式 | 目的 |
|------|------|----------|------|
| 日志清理 | 每周 | 自动脚本 | 释放磁盘空间 |
| 缓存清理 | 每天 | 自动任务 | 保持性能 |
| 配置备份 | 每月 | 手动备份 | 灾难恢复 |
| 依赖更新 | 按需 | 手动更新 | 安全补丁 |

### 15.2 监控告警

| 监控项 | 告警阈值 | 通知方式 | 处理优先级 |
|--------|----------|----------|------------|
| 服务可用性 | 连续5分钟不可用 | 日志记录 | 高 |
| 错误率 | >5% | 日志记录 | 中 |
| 响应时间 | >5秒 | 日志记录 | 中 |
| 资源使用 | >90% | 日志记录 | 低 |

---

**文档版本**: 1.0
**最后更新**: 2024年
**维护者**: IoT Agent开发团队
