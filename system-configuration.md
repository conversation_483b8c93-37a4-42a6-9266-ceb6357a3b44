# IoT Agent MVP2 系统配置文档

## 1. 系统架构概览

### 1.1 核心组件
- **后端服务**: FastAPI (端口: 8000)
- **前端应用**: Vue 3 + Vite
- **智能体系统**: 基于LangGraph的多智能体协作
- **消息队列**: MQTT Broker (WebSocket: 8083, TCP: 1884)
- **大语言模型**: Ollama (qwen2.5:32b)
- **外部服务**: RAGFlow、Redmine、Gitea

### 1.2 数据流架构

```mermaid
graph TB
    A[前端Vue应用] -->|HTTP/WebSocket| B[FastAPI后端]
    B -->|MQTT WebSocket| C[MQTT Broker]
    C -->|设备数据| D[IoT设备]
    B -->|API调用| E[RAGFlow服务]
    B -->|HTTP API| F[Redmine系统]
    B -->|HTTP API| G[Gitea代码仓库]
    B -->|本地调用| H[Ollama LLM]
    B -->|WebSocket广播| I[实时警报系统]
    
    subgraph "智能体系统"
        J[问题分类智能体]
        K[MySQL智能体]
        L[IoT聊天智能体]
        M[多智能体协作]
    end
    
    B --> J
    J --> K
    J --> L
    K --> M
    L --> M
```

## 2. 外部服务配置清单

### 2.1 MQTT服务配置

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 主机地址 | ************ | MQTT Broker服务器 | `.env.ascii`, `config.json` |
| WebSocket端口 | 8083 | 前端WebSocket连接 | 多个配置文件 |
| TCP端口 | 1884 | 后端TCP连接备用 | `mqtt_discovery.py` |
| WebSocket路径 | /mqtt | WebSocket连接路径 | 全局配置 |
| 客户端ID前缀 | alert-monitor- | 后端客户端标识 | `alert_generator.py` |
| 用户名 | 111 | 前端连接认证 | `mqtt.js` |

**连接方式**:
- 前端: WebSocket (ws://************:8083/mqtt)
- 后端: WebSocket优先，TCP备用

### 2.2 RAGFlow服务配置

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 基础URL | 127.0.0.1 | RAGFlow服务地址 | 代码中硬编码 |
| API密钥 | ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG | API认证 | `api.js` |
| 端点路径 | /api/v1/chats_openai/{chat_id}/chat/completions | 对话API | 后端代理 |
| 认证方式 | Bearer Token | HTTP头认证 | API调用 |

### 2.3 Redmine项目管理系统

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 服务URL | https://rm.978543210.com/ | Redmine服务器 | `config.yaml` |
| API密钥 | af21ef3a9a7df53a944ea71e1eed587540871fca | API认证 | `config.yaml` |
| SSL验证 | false | 跳过SSL证书验证 | `config.yaml` |
| 超时设置 | 默认 | HTTP请求超时 | 使用默认值 |

### 2.4 Gitea代码仓库

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 服务URL | https://git.978543210.com/ | Gitea服务器 | `config.yaml` |
| API密钥 | 2f6bd626dc3fddc403ec580c5639ed7091c88320 | API认证 | `config.yaml` |
| SSL验证 | false | 跳过SSL证书验证 | `config.yaml` |

### 2.5 Ollama大语言模型

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 模型名称 | qwen2.5:32b | 主要对话模型 | `iot_chat_agents.py` |
| 温度参数 | 0.7 | 生成随机性控制 | 智能体配置 |
| 连接方式 | 本地调用 | 直接调用本地服务 | LangChain配置 |

### 2.6 RTSP视频流服务

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| RTSP服务器 | MediaMTX | 开源RTSP流媒体服务器 | `mediamtx.exe` |
| 服务端口 | 8554 | RTSP协议端口 | `mediamtx.yml` |
| 认证用户名 | admin | RTSP访问认证 | `mediamtx.yml` |
| 认证密码 | admin123 | RTSP访问认证 | `mediamtx.yml` |
| HLS端口 | 8888 | HTTP Live Streaming | `mediamtx.yml` |
| WebRTC端口 | 8889 | WebRTC流媒体 | `mediamtx.yml` |
| RTMP端口 | 1935 | RTMP协议端口 | `mediamtx.yml` |

**支持的流媒体协议**:
- RTSP: rtsp://admin:admin123@localhost:8554/stream1
- HLS: http://localhost:8888/stream1/index.m3u8
- WebRTC: http://localhost:8889/stream1/whep
- RTMP: rtmp://localhost:1935/stream1

### 2.7 RTSP代理服务

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 代理服务器 | Flask应用 | RTSP到MJPEG转换 | `rtsp_proxy.py` |
| 监听地址 | 0.0.0.0 | 服务绑定地址 | 启动参数 |
| 监听端口 | 8001 | HTTP代理端口 | 启动参数 |
| 转换后端 | FFmpeg | 视频格式转换 | 系统依赖 |
| 配置文件 | stream_config.json | 流配置存储 | JSON格式 |

**支持的摄像头类型**:
- 本地测试流: rtsp://admin:admin123@localhost:8554/stream1
- 海康威视: rtsp://admin:sg123456@**************:554/h264/ch1/main/av_stream
- 大华摄像头: rtsp://admin:cyzy3q000@************/cam/realmonitor?channel=31&subtype=0&proto=Private3

### 2.8 设备数据模拟器

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 界面框架 | PyQt6 | 图形用户界面 | `gui.py` |
| MQTT客户端 | paho-mqtt | 消息发布客户端 | `mqtt_client.py` |
| 数据生成器 | 自定义算法 | 模拟设备数据 | `data_generator.py` |
| 配置文件 | config.json | 设备列表配置 | JSON格式 |

**模拟设备列表**:
- HN系列: HN15V1-HN15V10, HN15V24-HN15V26, HN15V67
- JH系列: JH001-JH009
- 站点: HN3S1 (海南3号站1区)

### 2.9 MCP服务 (Model Context Protocol)

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 服务框架 | FastAPI | MCP API服务器 | `custom_interactive_client_sse.py` |
| 监听端口 | 默认端口 | HTTP API服务 | 自动分配 |
| LangGraph集成 | create_react_agent | 智能体创建 | MCP客户端 |
| 工具适配器 | MultiServerMCPClient | 多服务器客户端 | langchain_mcp_adapters |

**集成的MCP工具**:
- Redmine工具: 项目管理和问题跟踪
- MQTT工具: 设备数据获取和发布
- 文件系统工具: 本地文件操作

## 3. 完整服务架构

### 3.1 服务启动顺序和依赖关系

根据 `start_all.bat` 脚本，系统包含以下服务：

| 序号 | 服务名称 | 启动命令 | 端口 | 依赖关系 |
|------|----------|----------|------|----------|
| 1 | 后端API服务 | `python -m app.main` | 8000 | 无 |
| 2 | 前端Web应用 | `npm run dev` | 5173 | 后端API |
| 3 | 设备数据模拟器 | `python main.py` | - | MQTT Broker |
| 4 | RTSP代理服务 | `python rtsp_proxy.py` | 8001 | FFmpeg |
| 5 | MCP服务 | `python custom_interactive_client_sse.py` | 动态 | 后端API |
| 6 | RTSP流媒体服务器 | `mediamtx.exe` | 8554 | 无 |
| 7 | FFmpeg视频流 | `ffmpeg -re -stream_loop...` | - | RTSP服务器 |

### 3.2 服务间通信架构

```mermaid
graph TB
    subgraph "核心服务层"
        A[后端API服务<br/>:8000]
        B[前端Web应用<br/>:5173]
        C[MQTT Broker<br/>:8083]
    end

    subgraph "视频服务层"
        D[RTSP流媒体服务器<br/>:8554]
        E[RTSP代理服务<br/>:8001]
        F[FFmpeg视频流]
    end

    subgraph "辅助服务层"
        G[设备数据模拟器<br/>PyQt6]
        H[MCP服务<br/>动态端口]
    end

    subgraph "外部服务"
        I[RAGFlow<br/>127.0.0.1]
        J[Ollama LLM<br/>本地]
        K[Redmine<br/>rm.978543210.com]
        L[Gitea<br/>git.978543210.com]
    end

    B -->|HTTP/WebSocket| A
    A -->|MQTT WebSocket| C
    G -->|MQTT发布| C
    A -->|API调用| I
    A -->|模型调用| J
    H -->|工具调用| K
    H -->|工具调用| L
    H -->|MCP协议| A

    F -->|RTSP推流| D
    D -->|RTSP流| E
    E -->|MJPEG/HTTP| B

    classDef core fill:#e8f5e8
    classDef video fill:#fff3e0
    classDef aux fill:#f3e5f5
    classDef external fill:#fce4ec

    class A,B,C core
    class D,E,F video
    class G,H aux
    class I,J,K,L external
```

### 3.3 视频流处理架构

#### 3.3.1 RTSP流媒体服务器 (MediaMTX)

| 配置项 | 值 | 说明 | 配置位置 |
|--------|----|----|----------|
| 服务器软件 | MediaMTX v1.12.2 | 开源流媒体服务器 | `mediamtx.exe` |
| 配置文件 | mediamtx.yml | 主配置文件 | YAML格式 |
| 日志级别 | info | 日志详细程度 | `logLevel: info` |
| 读取超时 | 10s | 读操作超时 | `readTimeout: 10s` |
| 写入超时 | 10s | 写操作超时 | `writeTimeout: 10s` |
| UDP最大负载 | 1472字节 | 避免网络分片 | `udpMaxPayloadSize: 1472` |

**协议端口配置**:
- RTSP: 8554 (TCP)
- RTSPS: 8322 (TCP/TLS)
- RTP: 8000 (UDP)
- RTCP: 8001 (UDP)
- RTMP: 1935 (TCP)
- RTMPS: 1936 (TCP/TLS)
- HLS: 8888 (HTTP)
- WebRTC: 8889 (HTTP)
- SRT: 8890 (UDP)

#### 3.3.2 RTSP代理服务配置

| 配置项 | 值 | 说明 | 配置位置 |
|--------|----|----|----------|
| 代理框架 | Flask + OpenCV | Python Web服务 | `rtsp_proxy.py` |
| 监听端口 | 8001 | HTTP代理端口 | 启动参数 |
| 转换格式 | MJPEG | 浏览器兼容格式 | 代码逻辑 |
| 视频后端 | FFmpeg | 视频处理引擎 | 系统依赖 |
| 默认帧率 | 15 FPS | 视频帧率 | `stream_config.json` |
| 默认分辨率 | 640x480 | 视频分辨率 | `stream_config.json` |

**API端点**:
- `/`: 主页面和流列表
- `/stream/<stream_name>`: MJPEG视频流
- `/api/streams`: 获取所有流信息
- `/api/streams/<stream_name>`: 获取特定流信息
- `/api/streams/<stream_name>/start`: 启动流
- `/api/streams/<stream_name>/stop`: 停止流
- `/api/add_stream`: 添加新的视频流

#### 3.3.3 视频流配置示例

**本地测试流**:
```json
{
    "stream1": {
        "url": "rtsp://admin:admin123@localhost:8554/stream1",
        "fps": 15,
        "width": 640,
        "height": 480
    }
}
```

**海康威视摄像头**:
```json
{
    "video_hikvision": {
        "url": "rtsp://admin:sg123456@**************:554/h264/ch1/main/av_stream",
        "fps": 10,
        "width": 640,
        "height": 450
    }
}
```

**大华摄像头**:
```json
{
    "video_dahua": {
        "url": "rtsp://admin:cyzy3q000@************/cam/realmonitor?channel=31&subtype=0&proto=Private3",
        "fps": 10,
        "width": 640,
        "height": 450
    }
}
```

### 3.4 设备数据模拟器配置

#### 3.4.1 模拟器架构

| 组件 | 技术栈 | 功能 | 文件位置 |
|------|--------|------|----------|
| 图形界面 | PyQt6 | 用户交互界面 | `gui.py` |
| MQTT客户端 | paho-mqtt | 消息发布 | `mqtt_client.py` |
| 数据生成器 | 自定义算法 | 模拟传感器数据 | `data_generator.py` |
| 主控制器 | Python | 协调各组件 | `main.py` |

#### 3.4.2 模拟设备配置

**设备分类**:
- **HN系列**: 海南油田设备 (HN15V1-HN15V10, HN15V24-HN15V26, HN15V67)
- **JH系列**: 江汉油田设备 (JH001-JH009)
- **站点**: HN3S1 (海南3号站1区)

**数据类型**:
- 油压 (MPa): 0.1-50.0
- 套压 (MPa): 0.1-10.0
- 井口温度 (°C): -20-100
- 冲程长度 (m): 0.5-6.0
- 冲次 (次/分钟): 1-20
- 有功功率 (kW): 0-100
- 无功功率 (kVar): 0-50
- 功率因数: 0.1-1.0
- 累计电量 (kWh): 0-99999

#### 3.4.3 MQTT发布配置

| 配置项 | 值 | 说明 |
|--------|----|----|
| 主题格式 | `/{station}/{wellname}` | 消息主题模式 |
| 消息格式 | JSON | 数据序列化格式 |
| QoS级别 | 0 | 消息质量等级 |
| 保留消息 | False | 不保留历史消息 |
| 发布频率 | 可配置 | 1-60秒间隔 |

**消息示例**:
```json
{
    "Station": "HN3S1",
    "Wellname": "HN15V1",
    "timestamp": "2024-01-01T12:00:00Z",
    "oil_pressure": 26.5,
    "casing_pressure": 5.2,
    "wellhead_temp": 38.6,
    "stroke_length": 3.2,
    "stroke_rate": 4.5,
    "active_power": 35.8,
    "reactive_power": 12.3,
    "power_factor": 0.85,
    "total_power": 2450.6,
    "status": 0,
    "check_date": "2024-01-01 12:00:00"
}
```

### 3.5 MCP服务配置

#### 3.5.1 MCP (Model Context Protocol) 架构

| 组件 | 技术 | 功能 | 配置位置 |
|------|------|------|----------|
| API服务器 | FastAPI | HTTP API接口 | `custom_interactive_client_sse.py` |
| MCP客户端 | MultiServerMCPClient | 多服务器连接 | langchain_mcp_adapters |
| 智能体引擎 | LangGraph | 反应式智能体 | create_react_agent |
| 工具集成 | 动态加载 | MCP工具适配 | 运行时加载 |

#### 3.5.2 集成的MCP工具

**Redmine工具集**:
- 项目管理: 获取项目列表、创建项目
- 问题跟踪: 创建问题、更新状态、查询问题
- 时间记录: 工时统计、时间条目管理
- Wiki管理: 页面创建、更新、查询
- 用户管理: 用户信息查询

**MQTT工具集**:
- 设备发现: 自动发现MQTT设备
- 数据订阅: 订阅设备数据主题
- 消息发布: 发布控制指令
- 连接管理: MQTT连接状态监控

**文件系统工具**:
- 文件操作: 读取、写入、删除文件
- 目录管理: 创建、删除、列出目录
- 权限管理: 文件权限设置

#### 3.5.3 MCP API端点

| 端点 | 方法 | 功能 | 返回格式 |
|------|------|------|----------|
| `/` | GET | 服务状态检查 | JSON |
| `/health` | GET | 健康检查 | JSON |
| `/chat` | POST | 智能体对话 | SSE流 |
| `/tools` | GET | 获取可用工具 | JSON |

### 3.6 FastAPI应用配置

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 应用标题 | IOT Agent API | API文档标题 | `main.py` |
| 版本号 | 0.1.0 | API版本 | `main.py` |
| 监听地址 | 0.0.0.0 | 服务绑定地址 | 启动参数 |
| 监听端口 | 8000 | HTTP服务端口 | 启动参数 |
| 重载模式 | True | 开发模式热重载 | 启动参数 |

### 3.7 数据库配置

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 数据库URL | sqlite:///./app.db | SQLite数据库文件 | `.env.ascii` |
| 连接池 | 默认 | 数据库连接管理 | 默认配置 |

### 3.8 日志配置

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 日志级别 | ERROR | 默认日志级别 | `main.py` |
| 日志文件 | logs/application.log | 日志文件路径 | `log_config.py` |
| 详细日志 | 可切换 | 运行时控制 | `/api/toggle_logging` |
| 警报日志 | TRUE | 警报日志开关 | `.env.ascii` |

### 3.9 前端应用配置

| 配置项 | 值 | 用途 | 配置位置 |
|--------|----|----|----------|
| 开发框架 | Vue 3 + Vite | 前端框架 | `package.json` |
| 开发端口 | 5173 | 开发服务器端口 | Vite默认 |
| 构建工具 | Vite | 构建和热重载 | `vite.config.js` |
| UI组件库 | Ant Design Vue | UI组件 | `package.json` |
| 状态管理 | Pinia | 状态管理 | `stores/` |
| 路由管理 | Vue Router | 页面路由 | `router/` |

## 4. 网络和安全配置

### 4.1 端口分配表

| 服务 | 端口 | 协议 | 用途 | 访问方式 |
|------|------|------|------|----------|
| 后端API | 8000 | HTTP | RESTful API和WebSocket | http://localhost:8000 |
| 前端Web | 5173 | HTTP | 开发服务器 | http://localhost:5173 |
| RTSP代理 | 8001 | HTTP | 视频流代理 | http://localhost:8001 |
| MQTT WebSocket | 8083 | WebSocket | MQTT over WebSocket | ws://************:8083/mqtt |
| MQTT TCP | 1884 | TCP | MQTT原生协议 | tcp://************:1884 |
| RTSP服务器 | 8554 | RTSP | 视频流服务 | rtsp://localhost:8554 |
| HLS服务 | 8888 | HTTP | HTTP Live Streaming | http://localhost:8888 |
| WebRTC服务 | 8889 | HTTP | WebRTC信令 | http://localhost:8889 |
| RTMP服务 | 1935 | RTMP | RTMP流媒体 | rtmp://localhost:1935 |
| MCP服务 | 动态 | HTTP | Model Context Protocol | 自动分配 |

### 4.2 CORS配置

**后端API (FastAPI)**:
- **允许来源**: 所有来源 (*)
- **允许方法**: GET, POST, PUT, DELETE, OPTIONS
- **允许头部**: 所有头部
- **凭据支持**: 启用

**RTSP代理服务 (Flask)**:
- **允许来源**: 所有来源 (*)
- **跨域请求**: 启用
- **预检请求**: 支持

**MCP服务 (FastAPI)**:
- **允许来源**: 所有来源 (*)
- **允许凭据**: 启用
- **允许方法**: 所有方法

### 4.3 WebSocket安全

| 服务 | 认证方式 | 连接验证 | 跨域策略 |
|------|----------|----------|----------|
| 后端WebSocket | 无认证 | 始终允许 | 允许所有来源 |
| MQTT WebSocket | 用户名密码 | 简单认证 | 允许配置来源 |

### 4.4 API安全配置

**认证机制**:
- **RAGFlow**: Bearer Token认证
- **Redmine**: API密钥认证
- **Gitea**: API密钥认证
- **内部API**: 无认证（开发环境）

**SSL/TLS配置**:
- **外部服务**: SSL验证禁用（开发环境）
- **内部服务**: HTTP明文传输
- **生产环境建议**: 启用HTTPS和SSL验证

## 5. 设备监控配置

### 5.1 设备阈值设置

| 参数类型 | 最小值 | 最大值 | 单位 | 正常范围 | 当前设置说明 |
|----------|--------|--------|------|----------|--------------|
| 温度 | -50 | 500 | °C | 20-80 | 极宽范围，几乎不触发警报 |
| 压力 | -10 | 100 | MPa | 0.1-1.0 | 极宽范围设置 |
| 电流 | -100 | 1000 | A | 0.5-2.0 | 极宽范围设置 |
| 振动 | -100 | 1000 | mm | 0-10 | 极宽范围设置 |

**注意**: 当前阈值设置为开发测试模式，生产环境应调整为合理范围。

### 5.2 监控设备清单

#### 5.2.1 海南油田设备 (HN系列)
| 设备ID | 站点 | 设备类型 | 监控状态 |
|--------|------|----------|----------|
| HN15V1-HN15V10 | HN3S1 | 抽油机 | 活跃监控 |
| HN15V24-HN15V26 | HN3S1 | 抽油机 | 活跃监控 |
| HN15V67 | HN3S1 | 抽油机 | 活跃监控 |

#### 5.2.2 江汉油田设备 (JH系列)
| 设备ID | 站点 | 设备类型 | 监控状态 |
|--------|------|----------|----------|
| JH001-JH009 | HN3S1 | 抽油机 | 活跃监控 |

#### 5.2.3 摄像头设备
| 设备类型 | IP地址 | 通道 | RTSP地址 |
|----------|--------|------|----------|
| 海康威视 | ************** | 1 | rtsp://admin:sg123456@**************:554/h264/ch1/main/av_stream |
| 大华摄像头 | ************ | 31 | rtsp://admin:cyzy3q000@************/cam/realmonitor?channel=31&subtype=0&proto=Private3 |
| 大华摄像头 | ************ | 47 | rtsp://admin:cyzy3q000@************/cam/realmonitor?channel=47&subtype=0&proto=Private3 |

### 5.3 监控配置参数

| 配置项 | 值 | 说明 | 配置位置 |
|--------|----|----|----------|
| 监控间隔 | 可配置 | 数据采集频率 | 模拟器设置 |
| 数据存储 | 内存缓存 | 临时数据存储 | `alert_generator.py` |
| 历史记录 | SQLite | 持久化存储 | 数据库 |
| 警报缓存 | 内存字典 | 警报状态跟踪 | `alert_cache` |
| 全局监控 | 启用 | 系统级监控开关 | `global_monitoring` |

## 6. 启动脚本分析

### 6.1 start_all.bat 详细分析

```batch
@echo off
echo 正在启动所有服务...

:: 后端服务
start cmd /k "cd Backend && venv\Scripts\activate && python -m app.main"

:: 前端服务
start cmd /k "cd frontend && npm run dev"

:: 模拟器
start cmd /k "cd simulator && venv\Scripts\activate && python main.py"

:: RTSP代理
start cmd /k "cd rtsp_proxy && venv\Scripts\activate && python rtsp_proxy.py --host 0.0.0.0 --port 8001"

:: MCP服务
start cmd /k "cd C:\AI\IOT_Agent_MVP2\Backend\app\minimal-mcp && .venv_new\Scripts\activate && python custom_interactive_client_sse.py"

:: RTSP流媒体服务器
start cmd /k "cd C:\AI\IOT_Agent_MVP2\rtsp视频流 && .\create_rtsp_server.bat"

:: FFmpeg视频流推送
start cmd /k "cd C:\AI\IOT_Agent_MVP2\rtsp视频流 && ffmpeg -re -stream_loop -1 -i 1.mp4 -c copy -f rtsp rtsp://admin:admin123@localhost:8554/stream1"

echo 所有服务已启动！
```

### 6.2 服务启动顺序和依赖

| 启动顺序 | 服务名称 | 工作目录 | 虚拟环境 | 启动命令 | 依赖服务 |
|----------|----------|----------|----------|----------|----------|
| 1 | 后端API服务 | Backend | venv | python -m app.main | 无 |
| 2 | 前端Web应用 | frontend | 无 | npm run dev | 后端API |
| 3 | 设备模拟器 | simulator | venv | python main.py | MQTT Broker |
| 4 | RTSP代理服务 | rtsp_proxy | venv | python rtsp_proxy.py | FFmpeg |
| 5 | MCP服务 | Backend/app/minimal-mcp | .venv_new | python custom_interactive_client_sse.py | 后端API |
| 6 | RTSP服务器 | rtsp视频流 | 无 | create_rtsp_server.bat | 无 |
| 7 | 视频流推送 | rtsp视频流 | 无 | ffmpeg命令 | RTSP服务器 |

### 6.3 虚拟环境配置

| 服务 | 虚拟环境路径 | Python版本 | 主要依赖 |
|------|--------------|------------|----------|
| 后端API | Backend/venv | 3.8+ | FastAPI, LangChain, paho-mqtt |
| 设备模拟器 | simulator/venv | 3.8+ | PyQt6, paho-mqtt |
| RTSP代理 | rtsp_proxy/venv | 3.8+ | Flask, OpenCV, FFmpeg |
| MCP服务 | Backend/app/minimal-mcp/.venv_new | 3.8+ | FastAPI, LangGraph, MCP |

### 6.4 外部依赖检查

**系统级依赖**:
- FFmpeg: 视频处理和转码
- Node.js: 前端开发环境
- Python 3.8+: 后端运行环境

**可选依赖**:
- MQTT Broker: 外部MQTT服务器
- Ollama: 本地大语言模型服务
- MediaMTX: 可自动下载

## 7. 智能体系统配置

### 6.1 智能体类型和功能

| 智能体名称 | 功能描述 | 配置参数 | 文件位置 |
|------------|----------|----------|----------|
| 问题分类智能体 | 将用户问题分类为知识库查询、设备数据查询或混合查询 | 模型: qwen2.5:32b | `question_classifier_agent.py` |
| MySQL智能体 | 处理数据库查询和操作 | 数据库连接配置 | `mysql_agent.py` |
| IoT聊天智能体 | 处理物联网相关对话和设备交互 | LangGraph状态管理 | `iot_chat_agents.py` |
| 多智能体协作 | 协调多个智能体的工作流程 | 工具集成配置 | `multi_agents.py` |

### 6.2 智能体工作流程

```mermaid
graph LR
    A[用户输入] --> B[问题分类智能体]
    B -->|知识库查询| C[知识库响应智能体]
    B -->|设备数据查询| D[MQTT响应智能体]
    B -->|混合查询| E[协作智能体]
    B -->|直接回答| F[直接回答智能体]
    C --> G[最终响应]
    D --> G
    E --> G
    F --> G
```

### 6.3 工具集成配置

| 工具名称 | 功能 | 配置位置 | 集成方式 |
|----------|------|----------|----------|
| MQTT数据工具 | 获取实时设备数据 | `mqtt_data_tool.py` | 直接集成 |
| RAG数据工具 | 知识库检索增强生成 | `rag_data_tool.py` | API调用 |
| Tavily搜索工具 | 网络搜索功能 | `multi_agents.py` | 第三方API |
| Python REPL工具 | 代码执行和图表生成 | `multi_agents.py` | 本地执行 |

## 8. 数据流处理配置

### 7.1 WebSocket消息类型

| 消息类型 | 方向 | 数据格式 | 处理逻辑 |
|----------|------|----------|----------|
| connection_status | 服务器→客户端 | `{"type": "connection_status", "status": "connected"}` | 连接确认 |
| device_data | 客户端→服务器 | `{"type": "device_data", "device_id": "JH001", "data": {...}}` | 设备数据处理 |
| alert | 服务器→客户端 | `{"type": "alert", "title": "...", "content": "..."}` | 警报广播 |
| test_message | 双向 | `{"type": "test_message", "deviceId": "JH001"}` | 测试功能 |
| heartbeat | 服务器→客户端 | `{"type": "heartbeat", "timestamp": "..."}` | 连接保活 |

### 7.2 MQTT主题订阅

| 主题模式 | 用途 | 订阅者 | 数据格式 |
|----------|------|--------|----------|
| `HN3S2/+/+/data` | 海南站点设备数据 | 后端监控 | JSON格式设备数据 |
| `$SYS/#` | 系统状态信息 | 设备发现 | MQTT系统消息 |
| `device/+/status` | 设备状态更新 | 前后端 | 状态信息 |

### 7.3 数据处理管道

```mermaid
graph TD
    A[MQTT设备数据] --> B[数据接收]
    B --> C[数据验证]
    C --> D[阈值检查]
    D -->|正常| E[数据存储]
    D -->|异常| F[生成警报]
    F --> G[WebSocket广播]
    E --> H[历史数据分析]
    G --> I[前端显示]
    H --> J[趋势分析]
```

## 9. 错误处理和重试机制

### 8.1 连接重试配置

| 服务类型 | 重试次数 | 重试间隔 | 超时时间 | 降级策略 |
|----------|----------|----------|----------|----------|
| MQTT连接 | 无限重试 | 5秒递增 | 60秒 | TCP备用连接 |
| WebSocket | 自动重连 | 1秒起始 | 30秒 | 连接状态通知 |
| HTTP API | 3次 | 1秒 | 10秒 | 错误响应返回 |
| 数据库连接 | 5次 | 2秒 | 30秒 | 服务降级 |

### 8.2 异常处理策略

| 异常类型 | 处理方式 | 日志级别 | 用户通知 |
|----------|----------|----------|----------|
| 网络连接失败 | 自动重试 | WARNING | 连接状态提示 |
| 数据格式错误 | 跳过处理 | ERROR | 错误消息返回 |
| 认证失败 | 停止服务 | CRITICAL | 认证错误提示 |
| 资源不足 | 限流处理 | WARNING | 服务繁忙提示 |

## 10. 性能和监控配置

### 9.1 系统监控指标

| 监控项 | 阈值 | 检查频率 | 告警方式 |
|--------|------|----------|----------|
| CPU使用率 | >80% | 30秒 | 日志记录 |
| 内存使用率 | >85% | 30秒 | 日志记录 |
| WebSocket连接数 | >100 | 实时 | 连接管理 |
| MQTT消息频率 | >1000/分钟 | 实时 | 限流处理 |

### 9.2 缓存配置

| 缓存类型 | 存储位置 | 过期时间 | 清理策略 |
|----------|----------|----------|----------|
| 设备数据缓存 | 内存 | 5分钟 | LRU淘汰 |
| 警报历史 | 内存 | 1小时 | 定时清理 |
| WebSocket消息 | 内存 | 10分钟 | 自动清理 |
| 用户会话 | 内存 | 30分钟 | 超时清理 |

## 11. 部署配置

### 10.1 Docker配置
- **容器名称**: ag-python
- **基础镜像**: python:3.12
- **端口映射**: 8010:8000
- **网络**: agent-network (***********)

### 10.2 环境变量
```bash
APP_ENV=development
SECRET_KEY=your_secret_key_here
DATABASE_URL=sqlite:///./app.db
API_TITLE=IOT_Device_Monitoring_System
API_VERSION=0.1.0
MQTT_HOST=************
MQTT_PORT=8083
MQTT_PATH=/mqtt
ALERT_LOGGING_ENABLED=TRUE
ALERT_LOG_LEVEL=INFO
MQTT_USE_WEBSOCKET=TRUE
```

### 10.3 启动命令

**开发模式**:
```bash
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**生产模式**:
```bash
uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

**Docker启动**:
```bash
docker-compose up -d
```

## 12. API端点配置

### 11.1 系统管理API

| 端点 | 方法 | 功能 | 认证要求 |
|------|------|------|----------|
| `/api/health` | GET | 系统健康检查 | 无 |
| `/api/toggle_logging` | GET | 切换日志级别 | 无 |
| `/` | GET | 根路径欢迎信息 | 无 |

### 11.2 设备管理API

| 端点 | 方法 | 功能 | 认证要求 |
|------|------|------|----------|
| `/api/devices/status` | GET | 获取所有设备状态 | 无 |
| `/api/devices/{device_id}` | GET | 获取指定设备信息 | 无 |
| `/api/test/device_data` | POST | 模拟设备数据测试 | 无 |

### 11.3 警报管理API

| 端点 | 方法 | 功能 | 认证要求 |
|------|------|------|----------|
| `/api/alerts` | GET | 获取所有警报信息 | 无 |
| `/api/alerts/recent` | GET | 获取最近警报信息 | 无 |
| `/api/alerts/acknowledge` | POST | 确认警报 | 无 |
| `/camera/alert` | POST | 发送摄像头警报 | 无 |
| `/camera/move` | POST | 摄像头移动请求 | 无 |

### 11.4 WebSocket端点

| 端点 | 协议 | 功能 | 认证要求 |
|------|------|------|----------|
| `/alerts` | WebSocket | 实时警报推送 | 无 |

## 13. 配置文件位置汇总

### 12.1 后端配置文件

| 文件名 | 路径 | 用途 | 格式 |
|--------|------|------|------|
| `.env.ascii` | `Backend/.env.ascii` | 环境变量配置 | ENV |
| `config.yaml` | `Backend/app/minimal-mcp/config/config.yaml` | 外部服务配置 | YAML |
| `config.json` | `Backend/app/minimal-mcp/mqtt_tools/config.json` | MQTT设备配置 | JSON |
| `requirements.txt` | `Backend/requirements.txt` | Python依赖 | TXT |
| `docker-compose.yml` | `Deploy/docker-compose.yml` | Docker配置 | YAML |

### 12.2 前端配置文件

| 文件名 | 路径 | 用途 | 格式 |
|--------|------|------|------|
| `api.js` | `Frontend/src/config/api.js` | API配置 | JS |
| `package.json` | `Frontend/package.json` | 依赖和脚本 | JSON |
| `vite.config.js` | `Frontend/vite.config.js` | 构建配置 | JS |

### 13.3 视频流配置文件

| 文件名 | 路径 | 用途 | 格式 |
|--------|------|------|------|
| `mediamtx.yml` | `rtsp视频流/mediamtx.yml` | RTSP服务器配置 | YAML |
| `stream_config.json` | `rtsp_proxy/stream_config.json` | 视频流代理配置 | JSON |
| `create_rtsp_server.bat` | `rtsp视频流/create_rtsp_server.bat` | 服务器启动脚本 | BAT |

### 13.4 模拟器配置文件

| 文件名 | 路径 | 用途 | 格式 |
|--------|------|------|------|
| `config.json` | `Simulator/config.json` | 设备模拟器配置 | JSON |
| `requirements.txt` | `Simulator/requirements.txt` | Python依赖 | TXT |

### 13.5 启动脚本

| 文件名 | 路径 | 用途 | 格式 |
|--------|------|------|------|
| `start_all.bat` | `start_all.bat` | 全服务启动脚本 | BAT |
| `setup_venv.bat` | `rtsp_proxy/setup_venv.bat` | 虚拟环境设置 | BAT |
| `start_proxy.bat` | `rtsp_proxy/start_proxy.bat` | 代理服务启动 | BAT |

## 14. 安全配置建议

### 13.1 生产环境安全配置

| 配置项 | 开发环境 | 生产环境建议 |
|--------|----------|--------------|
| SSL验证 | false | true |
| CORS来源 | * | 指定域名 |
| API密钥 | 明文存储 | 环境变量 |
| 日志级别 | DEBUG/INFO | WARNING/ERROR |
| WebSocket认证 | 无 | Token验证 |

### 13.2 敏感信息管理

| 信息类型 | 当前存储方式 | 建议改进 |
|----------|--------------|----------|
| API密钥 | 配置文件 | 环境变量或密钥管理服务 |
| 数据库密码 | 配置文件 | 加密存储 |
| MQTT凭据 | 明文 | 加密传输 |
| 会话密钥 | 固定值 | 动态生成 |

## 15. 故障排除指南

### 14.1 常见问题和解决方案

| 问题类型 | 症状 | 可能原因 | 解决方案 |
|----------|------|----------|----------|
| MQTT连接失败 | 设备数据无法接收 | 网络问题或配置错误 | 检查网络连接和配置参数 |
| WebSocket断开 | 前端无法接收实时数据 | 网络不稳定或服务重启 | 自动重连机制 |
| API调用失败 | 外部服务无响应 | 服务不可用或认证失败 | 检查服务状态和认证信息 |
| 内存泄漏 | 系统性能下降 | 缓存未及时清理 | 重启服务或调整缓存策略 |

### 14.2 日志分析

| 日志类型 | 位置 | 关键字段 | 分析要点 |
|----------|------|----------|----------|
| 应用日志 | `logs/application.log` | timestamp, level, message | 错误模式和频率 |
| MQTT日志 | `logs/mqtt_monitor.log` | device_id, status, data | 设备连接状态 |
| WebSocket日志 | 控制台输出 | connection_id, message_type | 连接质量 |

## 16. 维护和更新

### 15.1 定期维护任务

| 任务 | 频率 | 执行方式 | 目的 |
|------|------|----------|------|
| 日志清理 | 每周 | 自动脚本 | 释放磁盘空间 |
| 缓存清理 | 每天 | 自动任务 | 保持性能 |
| 配置备份 | 每月 | 手动备份 | 灾难恢复 |
| 依赖更新 | 按需 | 手动更新 | 安全补丁 |

### 15.2 监控告警

| 监控项 | 告警阈值 | 通知方式 | 处理优先级 |
|--------|----------|----------|------------|
| 服务可用性 | 连续5分钟不可用 | 日志记录 | 高 |
| 错误率 | >5% | 日志记录 | 中 |
| 响应时间 | >5秒 | 日志记录 | 中 |
| 资源使用 | >90% | 日志记录 | 低 |

---

**文档版本**: 1.0
**最后更新**: 2024年
**维护者**: IoT Agent开发团队
