@echo off
echo Starting RTSP Proxy Service Virtual Environment Setup...

REM Check if virtual environment exists
if not exist venv (
    echo Creating virtual environment...
    python -m venv venv
) else (
    echo Virtual environment already exists.
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Display Python version
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not properly installed in virtual environment.
    pause
    exit /b 1
)

REM Install dependencies
echo Installing dependencies...
pip install --upgrade pip
pip install -r requirements.txt

echo.
echo =================================================
echo Virtual environment setup complete! 
echo.
echo Please use start_proxy.bat to start the RTSP proxy service
echo =================================================
echo.

pause 