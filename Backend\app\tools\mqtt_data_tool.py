from typing import Annotated, List, Optional, Dict, Any
import json
import logging
from langchain_core.tools import tool
import time
from ..utils.alert_generator import alert_generator

# 配置日志
logger = logging.getLogger(__name__)

class MQTTDataTool:
    def __init__(self):
        self.device_data: Dict[str, dict] = {}
        self.last_update: Dict[str, float] = {}
        self.data_timeout = 30  # 数据超时时间（秒）
        
        logger.info("MQTT数据工具初始化完成")
    
    def process_device_data(self, device_id: str, data: dict) -> List[dict]:
        """
        处理设备数据并生成警报
        
        Args:
            device_id (str): 设备ID
            data (dict): 设备数据
            
        Returns:
            List[dict]: 生成的警报列表
        """
        alerts = []
        try:
            # 更新设备数据
            self.device_data[device_id] = data
            self.last_update[device_id] = time.time()
            
            # 检查设备状态
            if data.get("Status") == 0:
                alerts.append({
                    "type": "device_alert",
                    "device_id": device_id,
                    "alert_type": "device_offline",
                    "message": f"设备 {device_id} 离线",
                    "timestamp": time.time(),
                    "data": data
                })
                logger.warning(f"设备 {device_id} 离线")
            
            # 检查传感器数据
            if "Values" in data:
                for sensor in data["Values"]:
                    sensor_id = sensor.get("id")
                    sensor_value = sensor.get("Value")
                    
                    if sensor_value is None:
                        continue
                    
                    # 检查温度
                    if sensor_id == 'TWT':
                        logger.info(f"设备 {device_id} 温度: {sensor_value}℃")
                        if sensor_value > 60:
                            logger.warning(f"设备 {device_id} 温度过高: {sensor_value}℃")
                            alerts.append({
                                "type": "device_alert",
                                "device_id": device_id,
                                "alert_type": "high_temperature",
                                "message": f"设备 {device_id} 温度过高: {sensor_value}℃",
                                "timestamp": time.time(),
                                "data": {
                                    "temperature": sensor_value
                                }
                            })
                    
                    # 检查压力
                    elif sensor_id == 'WIP':
                        logger.info(f"设备 {device_id} 压力: {sensor_value}MPa")
                        if sensor_value > 6:
                            logger.warning(f"设备 {device_id} 压力过高: {sensor_value}MPa")
                            alerts.append({
                                "type": "device_alert",
                                "device_id": device_id,
                                "alert_type": "high_pressure",
                                "message": f"设备 {device_id} 压力过高: {sensor_value}MPa",
                                "timestamp": time.time(),
                                "data": {
                                    "pressure": sensor_value
                                }
                            })
            
            # 使用告警生成器处理数据
            alert_generator.update_device_data(device_id, data)
            
            # 获取设备状态
            device_status = alert_generator.get_device_status()
            logger.info(f"设备 {device_id} 状态: {device_status}")
            
            # 记录警报生成情况
            if alerts:
                logger.warning(f"设备 {device_id} 生成了 {len(alerts)} 个警报")
            else:
                logger.info(f"设备 {device_id} 数据正常，未生成警报")
            
            return alerts
            
        except Exception as e:
            logger.error(f"处理设备 {device_id} 数据时出错: {str(e)}")
            return []
    
    def get_device_data(self, device_ids: Optional[List[str]] = None) -> List[dict]:
        """
        获取设备数据
        
        Args:
            device_ids (Optional[List[str]]): 设备ID列表，如果为None则返回所有设备
            
        Returns:
            List[dict]: 设备数据列表
        """
        current_time = time.time()
        result = []
        
        # 检查数据是否超时
        timeout_count = 0
        for device_id, last_time in self.last_update.items():
            if current_time - last_time > self.data_timeout:
                logger.warning(f"设备 {device_id} 数据已超时")
                timeout_count += 1
        if timeout_count > 0:
            logger.warning(f"共有 {timeout_count} 个设备数据超时")
        
        # 获取指定设备或所有设备的数据
        if device_ids is None:
            device_ids = list(self.device_data.keys())
            logger.info(f"获取所有设备数据，共 {len(device_ids)} 个设备")
        else:
            logger.info(f"获取指定设备数据，设备ID列表: {device_ids}")
        
        for device_id in device_ids:
            if device_id in self.device_data:
                result.append(self.device_data[device_id])
                # logger.debug(f"成功获取设备 {device_id} 的数据")
            else:
                logger.warning(f"未找到设备 {device_id} 的数据")
        
        logger.info(f"成功获取 {len(result)} 个设备的数据")
        return result
    
    def get_device_status(self) -> dict:
        """
        获取所有设备状态
        
        Returns:
            dict: 设备状态信息
        """
        return alert_generator.get_device_status()

# 创建MQTT数据工具实例
mqtt_data_tool = MQTTDataTool()

@tool
def get_mqtt_device_data(
    device_ids: Annotated[List[str], "需要查询的设备ID列表，如['JH005', 'H008']"],
    topics: Annotated[Optional[List[str]], "可选的MQTT主题列表，如果为空则使用默认主题"] = None,
) -> Dict[str, Any]:
    """
    从MQTT服务器获取指定设备的实时数据。
    此工具会连接到MQTT服务器，订阅相关主题，并返回设备的最新数据。
    
    Args:
        device_ids: 需要查询的设备ID列表
        topics: 可选的MQTT主题列表，如果为空则使用默认主题
    
    Returns:
        包含设备数据的字典
    """
    logger.info(f"正在获取设备数据: {device_ids}, 主题: {topics}")
    
    try:
        # 这里应该是实际连接MQTT服务器的逻辑
        # 通常会包括：
        # 1. 连接到MQTT broker
        # 2. 订阅相关主题
        # 3. 接收设备数据
        # 4. 解析并返回数据
        
        # 生成模拟数据作为示例
        results = {}
        for device_id in device_ids:
            # 格式化设备ID（如果H005格式，转换为JH005格式）
            formatted_device_id = device_id
            if device_id.startswith('H') and not device_id.startswith('HN'):
                formatted_device_id = 'J' + device_id
                
            results[formatted_device_id] = {
                "device_id": formatted_device_id,
                "status": "正常运行",
                "last_update": "2023-04-15 14:32:45",
                "data": {
                    "oil_pressure": 26.5,  # 油压 MPa
                    "casing_pressure": 5.2,  # 套压 MPa
                    "wellhead_temp": 38.6,  # 井口温度 °C
                    "stroke_length": 3.2,  # 冲程长度 m
                    "stroke_rate": 4.5,  # 冲次 次/min
                    "active_power": 35.8,  # 有功功率 kW
                    "reactive_power": 12.3,  # 无功功率 kVar
                    "power_factor": 0.85,  # 功率因数
                    "total_power": 2450.6  # 累计电量 kWh
                }
            }
        
        logger.info(f"成功获取设备数据: {results}")
        return {
            "success": True,
            "devices": results
        }
        
    except Exception as e:
        logger.error(f"获取MQTT数据出错: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "devices": {}
        }

@tool
def format_mqtt_data_for_response(
    mqtt_data: Annotated[Dict[str, Any], "MQTT设备数据，通常是get_mqtt_device_data工具的返回结果"],
    user_query: Annotated[str, "用户的原始查询"]
) -> str:
    """
    将MQTT设备数据格式化为适合在聊天回复中展示的形式
    
    Args:
        mqtt_data: MQTT设备数据
        user_query: 用户的原始查询，用于上下文理解
    
    Returns:
        格式化后的文本
    """
    try:
        if not mqtt_data.get("success", False):
            return f"<mqtt-agent>未能获取到有效数据: {mqtt_data.get('error', '未知错误')}"
        
        devices = mqtt_data.get("devices", {})
        if not devices:
            return "<mqtt-agent>未找到符合条件的设备数据"
        
        # 生成格式化输出
        result = "<mqtt-agent>已获取设备实时数据:\n\n"
        
        for device_id, device_data in devices.items():
            result += f"## 设备: {device_id}\n"
            result += f"- 状态: {device_data.get('status', '未知')}\n"
            result += f"- 最后更新时间: {device_data.get('last_update', '未知')}\n\n"
            
            data = device_data.get("data", {})
            if data:
                result += "### 关键参数:\n"
                for key, value in data.items():
                    # 格式化显示名称
                    display_name = {
                        "oil_pressure": "油压",
                        "casing_pressure": "套压",
                        "wellhead_temp": "井口温度",
                        "stroke_length": "冲程长度",
                        "stroke_rate": "冲次",
                        "active_power": "有功功率",
                        "reactive_power": "无功功率",
                        "power_factor": "功率因数",
                        "total_power": "累计电量"
                    }.get(key, key)
                    
                    # 添加单位
                    unit = {
                        "oil_pressure": "MPa",
                        "casing_pressure": "MPa",
                        "wellhead_temp": "°C",
                        "stroke_length": "m",
                        "stroke_rate": "次/min",
                        "active_power": "kW",
                        "reactive_power": "kVar",
                        "power_factor": "",
                        "total_power": "kWh"
                    }.get(key, "")
                    
                    result += f"- {display_name}: {value} {unit}\n"
        
        return result
    except Exception as e:
        logger.error(f"格式化MQTT数据出错: {str(e)}")
        return f"<mqtt-agent>格式化设备数据时出错: {str(e)}"