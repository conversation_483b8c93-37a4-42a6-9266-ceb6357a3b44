<template>
  <div class="file-viewer-container" v-if="isVisible" :style="positionStyle" ref="container">
    <div class="file-viewer-header">
      <h3>{{ title }}</h3>
      <div class="header-actions">
        <button class="open-browser-btn" @click="openInNewWindow" title="在新窗口打开">
          <i class="fas fa-external-link-alt"></i>
        </button>
        <button class="close-btn" @click="closeViewer">
          <i class="fa-solid fa-xmark"></i>
        </button>
      </div>
    </div>
    <div class="file-viewer-content">
      <!-- 图片浏览模式(针对功图) -->
      <div v-if="isImageGallery" class="image-gallery-mode">
        <div class="gallery-navigation">
          <button class="nav-button prev-button" @click="showPrevImage" :disabled="currentImageIndex <= 0">
            <i class="fas fa-chevron-left"></i> 上一张
          </button>
          <span class="image-counter">{{ currentImageIndex + 1 }} / {{ totalImages }}</span>
          <button class="nav-button next-button" @click="showNextImage" :disabled="currentImageIndex >= totalImages - 1">
            下一张 <i class="fas fa-chevron-right"></i>
          </button>
        </div>
        <div class="gallery-image-container">
          <img :src="currentImagePath" class="gallery-image" alt="功图" ref="imageRef" @load="onImageLoad" />
        </div>
        <div class="image-details">
          <div class="image-title">{{ getImageTitle(currentImageIndex) }}</div>
        </div>
      </div>
      
      <!-- 单张图片查看模式 -->
      <div v-else-if="isSingleImage" class="single-image-mode">
        <div class="single-image-container">
          <img :src="filePath" class="single-image" alt="图片" ref="imageRef" @load="onImageLoad" />
        </div>
      </div>
      
      <!-- 标准PDF查看模式 -->
      <object v-else :data="filePath" type="application/pdf" class="pdf-viewer">
        <div class="pdf-fallback">
          <p>您的浏览器无法显示PDF，<a :href="filePath" target="_blank">点击此处在新窗口打开</a></p>
        </div>
      </object>
    </div>
    <!-- 调整大小的手柄 -->
    <div class="resize-handle resize-handle-se" @mousedown="startResize"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'
import { emitter } from '@/utils/emitter'

const isVisible = ref(false)
const filePath = ref('')
const position = ref({ x: 20, y: 10 }) // 默认是百分比值
const size = ref({ width: 70, height: 80 }) // 默认是百分比值
const title = ref('文件查看')
const container = ref(null)

// 图片浏览模式相关状态
const isImageGallery = ref(false)
const isSingleImage = ref(false) // 新增：单张图片模式
const currentImageIndex = ref(0)
const totalImages = ref(0)
const imageBasePath = ref('')
const currentImagePath = ref('')
const imageExtension = ref('jpg')
const imageRef = ref(null) // 添加对图片元素的引用
const userResized = ref(false) // 添加：标记用户是否手动调整过窗口大小

// 计算样式
const positionStyle = computed(() => {
  return {
    left: `${position.value.x}%`,
    top: `${position.value.y}%`,
    width: `${size.value.width}%`,
    height: `${size.value.height}%`
  }
})

// 根据图片大小调整弹出框尺寸
const adjustSizeToImage = (imgPath) => {
  // 如果用户已手动调整过窗口大小，则不再自动调整
  if (userResized.value && isImageGallery.value) {
    return
  }
  
  // 创建一个临时图片元素来获取图片尺寸
  const img = new Image()
  img.onload = () => {
    // 获取图片原始尺寸
    const imgWidth = img.width
    const imgHeight = img.height
    
    // 获取视口大小
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    // 计算图片比例
    const imgRatio = imgWidth / imgHeight
    
    // 额外空间：导航栏、标题栏和边距
    const extraWidth = 40  // 左右边距
    const extraHeight = 120  // 顶部标题栏+底部信息栏+边距
    
    // 初始计算：使窗口大小适合图片原始尺寸+边距
    let newWidth = imgWidth + extraWidth
    let newHeight = imgHeight + extraHeight
    
    // 如果图片太大，需要缩放
    if (newWidth > viewportWidth * 0.9 || newHeight > viewportHeight * 0.9) {
      const widthRatio = (viewportWidth * 0.9 - extraWidth) / imgWidth
      const heightRatio = (viewportHeight * 0.9 - extraHeight) / imgHeight
      
      // 取较小的比例进行缩放，保持图片比例
      const scale = Math.min(widthRatio, heightRatio)
      
      newWidth = imgWidth * scale + extraWidth
      newHeight = imgHeight * scale + extraHeight
    }
    
    // 设置最小尺寸
    newWidth = Math.max(newWidth, 400)
    newHeight = Math.max(newHeight, 300)
    
    // 转换为百分比
    const widthPercent = (newWidth / viewportWidth) * 100
    const heightPercent = (newHeight / viewportHeight) * 100
    
    // 更新尺寸
    size.value = {
      width: widthPercent,
      height: heightPercent
    }
    
    // 调整位置，使其居中
    position.value = {
      x: Math.max(0, (100 - widthPercent) / 2),
      y: Math.max(0, (100 - heightPercent) / 2)
    }
    
    // 确保窗口在可视区域内
    adjustPositionToViewport()
  }
  
  // 设置图片源以触发加载
  img.src = imgPath
}

// 监听显示文件事件
const handleOpenFileView = (data) => {
  filePath.value = data.filePath
  title.value = data.title || '文件查看'
  
  // 重置用户调整标记
  userResized.value = false
  
  if (data.position) {
    position.value = data.position
  }
  
  // 检测文件类型
  const isImage = checkIsImage(data.filePath || data.imagePath || '')
  
  // 检测是否是图片或功图模式
  if (data.imagePath || isImage) {
    const imagePath = data.imagePath || data.filePath
    
    // 对于任何类型的图片，应用自适应大小调整
    adjustSizeToImage(imagePath)
    
    // 检测是否是功图模式(海南19-13井功图)
    if (data.title && data.title.includes('功图') && imagePath.includes('.jpg')) {
      setupImageGallery(imagePath)
      isSingleImage.value = false
    } else {
      // 单张图片模式
      isImageGallery.value = false
      isSingleImage.value = true
    }
  } else {
    // 普通文件模式，使用默认尺寸
    isImageGallery.value = false
    isSingleImage.value = false
  }
  
  // 确保窗口在可视区域内
  adjustPositionToViewport()
  
  isVisible.value = true
}

// 检查是否为图片文件
const checkIsImage = (path) => {
  if (!path) return false
  const imageExts = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp']
  const lowerPath = path.toLowerCase()
  return imageExts.some(ext => lowerPath.endsWith(ext))
}

// 设置图片浏览模式
const setupImageGallery = (imagePath) => {
  isImageGallery.value = true
  
  // 提取图片基本路径和索引
  const pathMatch = imagePath.match(/(.+)\/(\d+)\.jpg$/)
  if (pathMatch) {
    imageBasePath.value = pathMatch[1] + '/'
    currentImageIndex.value = parseInt(pathMatch[2]) - 1
    
    // 功图总是10张
    totalImages.value = 10
    
    // 设置当前图片路径
    updateCurrentImage()
  } else {
    // 如果无法解析路径，回退到普通文件模式
    isImageGallery.value = false
  }
}

// 切换到上一张图片
const showPrevImage = () => {
  if (currentImageIndex.value > 0) {
    currentImageIndex.value--
    updateCurrentImage()
  }
}

// 切换到下一张图片
const showNextImage = () => {
  if (currentImageIndex.value < totalImages.value - 1) {
    currentImageIndex.value++
    updateCurrentImage()
  }
}

// 更新当前显示的图片
const updateCurrentImage = () => {
  currentImagePath.value = `${imageBasePath.value}${currentImageIndex.value + 1}.${imageExtension.value}`
  
  // 只有当用户未手动调整过窗口大小时，才自动调整窗口大小
  if (!userResized.value) {
    const newImagePath = currentImagePath.value
    // 等待DOM更新后再调整大小
    setTimeout(() => {
      adjustSizeToImage(newImagePath)
    }, 100)
  }
}

// 获取图片标题
const getImageTitle = (index) => {
  return `功图 ${index + 1}`
}

// 调整位置，确保窗口完全在可视区域内
const adjustPositionToViewport = () => {
  // 使用百分比值时不需要复杂的调整
  // 只需确保百分比值在合理范围内
  if (position.value.x + size.value.width > 100) {
    position.value.x = Math.max(0, 100 - size.value.width)
  }
  
  if (position.value.y + size.value.height > 100) {
    position.value.y = Math.max(0, 100 - size.value.height)
  }
}

// 关闭文件查看器
const closeViewer = () => {
  isVisible.value = false
}

// 在新窗口中打开文件
const openInNewWindow = () => {
  if (isImageGallery.value) {
    window.open(currentImagePath.value, '_blank')
  } else {
    window.open(filePath.value, '_blank')
  }
}

// 让窗口可拖动
let isDragging = false
let dragStartX = 0
let dragStartY = 0
let initialLeft = 0
let initialTop = 0

const startDrag = (e) => {
  const containerEl = container.value
  if (!containerEl || e.target.closest('.close-btn') || e.target.closest('.open-browser-btn') || e.target.closest('.resize-handle') || e.target.closest('.nav-button')) {
    return
  }
  
  if (e.target.closest('.file-viewer-header')) {
    isDragging = true
    dragStartX = e.clientX
    dragStartY = e.clientY
    
    // 获取当前位置的像素值
    const rect = containerEl.getBoundingClientRect()
    initialLeft = rect.left
    initialTop = rect.top
    
    document.addEventListener('mousemove', onDrag)
    document.addEventListener('mouseup', stopDrag)
    e.preventDefault()
  }
}

const onDrag = (e) => {
  if (isDragging) {
    const deltaX = e.clientX - dragStartX
    const deltaY = e.clientY - dragStartY
    
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    // 将像素位置转换为百分比
    const newLeft = ((initialLeft + deltaX) / viewportWidth) * 100
    const newTop = ((initialTop + deltaY) / viewportHeight) * 100
    
    // 更新位置，确保不超出视口
    position.value = {
      x: Math.max(0, Math.min(100 - size.value.width, newLeft)),
      y: Math.max(0, Math.min(100 - size.value.height, newTop))
    }
    
    e.preventDefault()
  }
}

const stopDrag = () => {
  isDragging = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 窗口大小调整功能
let isResizing = false
let resizeStartX = 0
let resizeStartY = 0
let initialWidth = 0
let initialHeight = 0

const startResize = (e) => {
  const containerEl = container.value
  if (!containerEl) return
  
  isResizing = true
  resizeStartX = e.clientX
  resizeStartY = e.clientY
  
  // 获取当前大小的像素值
  const rect = containerEl.getBoundingClientRect()
  initialWidth = rect.width
  initialHeight = rect.height
  
  document.addEventListener('mousemove', onResize)
  document.addEventListener('mouseup', stopResize)
  e.preventDefault()
}

const onResize = (e) => {
  if (isResizing) {
    const deltaX = e.clientX - resizeStartX
    const deltaY = e.clientY - resizeStartY
    
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    
    // 将像素大小转换为百分比
    const newWidth = ((initialWidth + deltaX) / viewportWidth) * 100
    const newHeight = ((initialHeight + deltaY) / viewportHeight) * 100
    
    // 更新大小，设置最小值和最大值
    size.value = {
      width: Math.max(20, Math.min(95, newWidth)),
      height: Math.max(20, Math.min(95, newHeight))
    }
    
    // 标记用户已手动调整过窗口大小
    userResized.value = true
    
    // 确保窗口不超出视口
    adjustPositionToViewport()
    
    e.preventDefault()
  }
}

const stopResize = () => {
  isResizing = false
  document.removeEventListener('mousemove', onResize)
  document.removeEventListener('mouseup', stopResize)
}

// 图片加载完成后处理
const onImageLoad = (event) => {
  if (isImageGallery.value) {
    // 只有当用户未手动调整过窗口大小时，才自动调整窗口大小
    if (!userResized.value) {
      adjustSizeToImage(currentImagePath.value)
    }
  } else if (isSingleImage.value) {
    // 单张图片模式
    adjustSizeToImage(filePath.value)
  }
}

onMounted(() => {
  // 添加事件监听
  emitter.on('open-file-view', handleOpenFileView)
  
  // 添加拖动事件监听
  document.addEventListener('mousedown', startDrag)
  
  // 监听键盘事件，支持左右箭头切换图片
  window.addEventListener('keydown', handleKeyDown)
})

// 添加键盘导航支持
const handleKeyDown = (e) => {
  if (!isVisible.value || !isImageGallery.value) return
  
  if (e.key === 'ArrowLeft' || e.key === 'Left') {
    showPrevImage()
    e.preventDefault()
  } else if (e.key === 'ArrowRight' || e.key === 'Right') {
    showNextImage()
    e.preventDefault()
  } else if (e.key === 'Escape') {
    closeViewer()
    e.preventDefault()
  }
}

onUnmounted(() => {
  // 移除事件监听
  emitter.off('open-file-view', handleOpenFileView)
  document.removeEventListener('mousedown', startDrag)
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('mousemove', onResize)
  document.removeEventListener('mouseup', stopResize)
  window.removeEventListener('keydown', handleKeyDown)
})
</script>

<style scoped>
.file-viewer-container {
  position: fixed;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 1000;
  /* 容器大小使用JS动态控制，使用百分比 */
}

.file-viewer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #1a4b91;
  color: white;
  cursor: move;
}

.file-viewer-header h3 {
  margin: 0;
  font-size: 16px;
  color: white;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-actions button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.header-actions button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.header-actions .close-btn:hover {
  background: rgba(255, 59, 48, 0.4);
}

.file-viewer-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.pdf-viewer {
  width: 100%;
  height: 100%;
  border: none;
}

.pdf-fallback {
  padding: 16px;
  text-align: center;
}

.resize-handle {
  position: absolute;
  width: 16px;
  height: 16px;
  cursor: nwse-resize;
  z-index: 2;
}

.resize-handle-se {
  bottom: 0;
  right: 0;
}

/* 图片浏览模式样式 */
.image-gallery-mode {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.gallery-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #f0f2f5;
  border-bottom: 1px solid #e8e8e8;
}

.nav-button {
  background: #1a4b91;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  transition: all 0.2s;
}

.nav-button:hover:not(:disabled) {
  background: #2a5ba1;
  transform: translateY(-1px);
}

.nav-button:disabled {
  background: #cccccc;
  cursor: not-allowed;
  opacity: 0.5;
}

.image-counter {
  font-weight: 500;
  color: #555;
}

.gallery-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  overflow: hidden;
  background: #f7f7f7;
}

.gallery-image {
  width: 100%;
  height: 100%;
  max-width: none;
  max-height: none;
  object-fit: contain;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  background: white;
}

/* 单张图片模式样式 */
.single-image-mode {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f7f7f7;
}

.single-image-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
  overflow: hidden;
}

.single-image {
  width: 100%;
  height: 100%;
  max-width: none;
  max-height: none;
  object-fit: contain;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  background: white;
}

.image-details {
  padding: 10px 15px;
  text-align: center;
  background-color: #f0f2f5;
  border-top: 1px solid #e8e8e8;
}

.image-title {
  font-weight: 500;
  color: #333;
}
</style> 