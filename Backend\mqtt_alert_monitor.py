import paho.mqtt.client as mqtt
import json
import time
import datetime
import sys
import logging

# MQTT服务器设置
MQTT_HOST = "************"
MQTT_PORT = 8083
MQTT_WEBSOCKET_PATH = "/mqtt"
USE_WEBSOCKET = True  # 设置为True使用WebSocket协议

# 设备主题列表
TOPICS = [
    "/HN3S1/JH001",
    "/HN3S1/JH002",
    "/HN3S1/JH006",
    "/HN3S1/JH007",
    "/HN3S1/JH008",
    "/HN3S1/JH009",
    "/HN3S1/HN15V3",
    "/HN3S1/HN15V4",
    "/HN3S1/HN15V5",
    "/HN3S1/HN15V6",
    "/HN3S1/HN15V67",
    "/HN3S1/HN15V7",
    "/HN3S1/HN15V8",
    "/HN3S1/HN15V9",
    "/HN3S1/HN15V10",
    "/HN3S1/HN15V25",
    "/HN3S1/HN15V26"
]

# 阈值设置
THRESHOLDS = {
    # 原始阈值设置:
    # "温度": {"min": 20, "max": 80},   # 温度阈值(℃)
    # "压力": {"min": 0.1, "max": 1.0}, # 压力阈值(MPa)
    # "电流": {"min": 0.5, "max": 2.0}, # 电流阈值(A)
    # "振动": {"min": 0, "max": 10}     # 振动阈值(mm)
    
    # 新的阈值设置，范围极宽，几乎不可能触发警报
    "温度": {"min": -100, "max": 1000},   # 温度阈值(℃)，极宽范围
    "压力": {"min": -100, "max": 1000},   # 压力阈值(MPa)，极宽范围
    "电流": {"min": -1000, "max": 10000}, # 电流阈值(A)，极宽范围
    "振动": {"min": -1000, "max": 10000}  # 振动阈值(mm)，极宽范围
}

# 消息计数器
received_count = 0
alert_count = 0
last_message_time = {}

# 配置日志
logging.basicConfig(
    level=logging.ERROR,  # 只记录错误
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/mqtt_monitor.log"),
        logging.StreamHandler()
    ]
)

# 连接成功回调
def on_connect(client, userdata, flags, rc):
    if rc == 0:
        print(f"[连接成功] 已连接到MQTT服务器: {MQTT_HOST}:{MQTT_PORT}")
        # 订阅所有主题
        for topic in TOPICS:
            client.subscribe(topic)
            print(f"[订阅] 已订阅主题: {topic}")
    else:
        print(f"[连接失败] 连接返回代码: {rc}")
        print(f"[连接失败码解释] {rc}: {get_rc_meaning(rc)}")

# 消息接收回调
def on_message(client, userdata, msg):
    global received_count, alert_count
    received_count += 1
    
    topic = msg.topic
    current_time = datetime.datetime.now()
    
    # 更新最后接收消息的时间
    if topic in last_message_time:
        time_diff = (current_time - last_message_time[topic]).total_seconds()
        print(f"[时间间隔] 主题 {topic} 距离上次消息: {time_diff:.1f}秒")
    last_message_time[topic] = current_time
    
    try:
        # 解析JSON数据
        payload = msg.payload.decode('utf-8')
        data = json.loads(payload)
        
        # 提取设备信息
        station = data.get("Station", "未知")
        wellname = data.get("Wellname", "未知")
        status = data.get("status", -1)
        check_date = data.get("check_date", "未知")
        
        print(f"\n[{current_time.strftime('%Y-%m-%d %H:%M:%S')}] 收到数据:")
        print(f"  主题: {topic}")
        print(f"  站点: {station}, 井名: {wellname}")
        print(f"  状态: {status}, 检查时间: {check_date}")
        
        # 检查是否有异常值
        alerts = []
        
        # 检查设备状态 - 已禁用
        # 原始代码:
        # if status != 0:
        #     alerts.append(f"设备状态异常: {status}")
        
        # 只有在状态大于1000时才触发警报（几乎不可能达到）
        if status > 1000:
            alerts.append(f"设备状态异常: {status}")
        
        # 检查各项指标是否超过阈值
        for metric, thresholds in THRESHOLDS.items():
            if metric in data:
                value = data.get(metric)
                print(f"  {metric}: {value}")
                
                if value < thresholds["min"] or value > thresholds["max"]:
                    alert_type = "过低" if value < thresholds["min"] else "过高"
                    alerts.append(f"{metric}{alert_type}: {value}")
        
        # 输出其他关键数据
        if "Values" in data and isinstance(data["Values"], list):
            print("  其他指标:")
            for item in data["Values"]:
                if isinstance(item, dict):
                    item_id = item.get("id", "未知")
                    item_value = item.get("Value", "未知")
                    print(f"    {item_id}: {item_value}")
                    
                    # 检查Values中的指标是否异常 - 已禁用
                    # 原始代码:
                    # if item_id in ["温度", "TWT"] and item_value > 80:
                    #     alerts.append(f"温度过高: {item_value}")
                    # elif item_id in ["压力", "WIP", "CPV"] and item_value > 1.0:
                    #     alerts.append(f"压力过高: {item_value}")
                    # elif item_id in ["电流", "ADL", "BDL", "CDL"] and item_value > 2.0:
                    #     alerts.append(f"电流过高: {item_value}")
                    
                    # 使用极端值检查，几乎不可能触发
                    if item_id in ["温度", "TWT"] and item_value > 1000:
                        alerts.append(f"温度过高: {item_value}")
                    elif item_id in ["压力", "WIP", "CPV"] and item_value > 1000:
                        alerts.append(f"压力过高: {item_value}")
                    elif item_id in ["电流", "ADL", "BDL", "CDL"] and item_value > 10000:
                        alerts.append(f"电流过高: {item_value}")
        
        # 如果有警报，输出警报信息
        if alerts:
            alert_count += 1
            print("\n[警报检测] 发现异常数据:")
            for alert in alerts:
                print(f"  * {alert}")
            print(f"[警报统计] 总计接收数据 {received_count} 条，发现警报 {alert_count} 条\n")
        else:
            print("[数据正常] 所有指标在正常范围内")
        
    except json.JSONDecodeError as e:
        print(f"[解析错误] 无法解析JSON数据: {e}")
        print(f"[原始数据] {msg.payload}")
    except Exception as e:
        print(f"[处理错误] 处理消息时出错: {e}")

# 断开连接回调
def on_disconnect(client, userdata, rc):
    if rc != 0:
        print(f"[断开连接] 意外断开连接，返回代码: {rc}")
        print(f"[断开连接码解释] {rc}: {get_rc_meaning(rc)}")
        print("[重连] 尝试重新连接...")
        reconnect(client)
    else:
        print("[断开连接] 客户端已正常断开连接")

# 获取返回代码含义
def get_rc_meaning(rc):
    rc_meanings = {
        0: "连接成功",
        1: "连接被拒绝-协议版本错误",
        2: "连接被拒绝-无效的客户端标识符",
        3: "连接被拒绝-服务器不可用",
        4: "连接被拒绝-错误的用户名或密码",
        5: "连接被拒绝-未授权",
        6: "连接被拒绝-协议错误",
        7: "协议错误"
    }
    return rc_meanings.get(rc, "未知错误")

# 重连函数
def reconnect(client, max_retries=5, retry_interval=5):
    retry_count = 0
    while retry_count < max_retries:
        try:
            print(f"[重连] 第{retry_count+1}次尝试重新连接...")
            if USE_WEBSOCKET:
                client.connect(MQTT_HOST, MQTT_PORT, 60)
            else:
                client.connect(MQTT_HOST, MQTT_PORT, 60)
            print("[重连] 重新连接成功")
            return True
        except Exception as e:
            print(f"[重连失败] {e}")
            retry_count += 1
            print(f"[等待] {retry_interval}秒后重试...")
            time.sleep(retry_interval)
    print(f"[重连失败] 已尝试{max_retries}次重连，放弃重连")
    return False

if __name__ == "__main__":
    # 创建MQTT客户端
    client_id = f"alert-monitor-{int(time.time())}"
    
    if USE_WEBSOCKET:
        client = mqtt.Client(client_id, transport="websockets")
        print(f"[配置] 使用WebSocket协议连接MQTT服务器: ws://{MQTT_HOST}:{MQTT_PORT}{MQTT_WEBSOCKET_PATH}")
        client.ws_set_options(path=MQTT_WEBSOCKET_PATH)
    else:
        client = mqtt.Client(client_id)
        print(f"[配置] 使用标准MQTT协议连接服务器: {MQTT_HOST}:{MQTT_PORT}")
    
    # 设置回调函数
    client.on_connect = on_connect
    client.on_message = on_message
    client.on_disconnect = on_disconnect
    
    # 启用详细日志
    client.enable_logger()
    
    try:
        print(f"[连接] 正在连接到MQTT服务器 {MQTT_HOST}:{MQTT_PORT}...")
        client.connect(MQTT_HOST, MQTT_PORT, 60)
        
        # 开始循环处理
        print("[开始监听] 开始监听MQTT消息...")
        print("按Ctrl+C终止程序\n")
        
        # 启动网络循环
        client.loop_start()
        
        # 主循环，保持程序运行
        try:
            while True:
                time.sleep(1)
                
                # 每30秒检查一次未收到消息的主题
                if int(time.time()) % 30 == 0:
                    current_time = datetime.datetime.now()
                    for topic, last_time in list(last_message_time.items()):
                        time_diff = (current_time - last_time).total_seconds()
                        if time_diff > 60:
                            print(f"[警告] 主题 {topic} 已 {time_diff:.1f} 秒未收到消息")
        except KeyboardInterrupt:
            print("\n[停止] 用户中断，正在退出...")
        finally:
            # 停止网络循环并断开连接
            client.loop_stop()
            client.disconnect()
            
    except Exception as e:
        print(f"[错误] 程序运行出错: {e}")
        sys.exit(1) 