<template>
  <div class="video-panel" :style="panelStyle" ref="panelRef">
    <!-- 面板标题和控制按钮 -->
    <div class="panel-header" @mousedown="startDrag">
      <div class="header-controls">
        <h3>视频监控</h3>
        <div class="panel-buttons">
          <button class="panel-btn minimize-btn" @click="minimizePanel" title="最小化">
            <span>—</span>
          </button>
          <button class="panel-btn close-btn" @click="closePanel" title="关闭">
            <span>×</span>
          </button>
        </div>
      </div>
    </div>

    <div v-if="!isMinimized" class="panel-content">
      <!-- 视频显示区域 -->
      <div class="video-container">
        <div class="video-display-area" :style="{ height: `${customSize.height - 80}px` }">
          <!-- 视频播放器容器 -->
          <div class="video-player" ref="videoPlayerRef">
            <!-- 视频将在这里渲染 -->
          </div>
          
          <!-- 加载中覆盖层 -->
          <div v-if="isVideoLoading" class="video-loading-overlay">
            <div class="loading-spinner"></div>
            <span>视频加载中...</span>
          </div>
        </div>
        
        <div class="video-controls">
          <button class="control-btn" @click="toggleVideoPlay">
            <i :class="isVideoPlaying ? 'pause-icon' : 'play-icon'"></i>
            {{ isVideoPlaying ? '暂停' : '播放' }}
          </button>
          <button class="control-btn" @click="toggleFullscreen">
            <i class="fullscreen-icon"></i>
            全屏
          </button>
        </div>
      </div>
    </div>
    
    <!-- 添加拉伸手柄 -->
    <div class="resize-handle resize-handle-right" @mousedown="startResize('right', $event)"></div>
    <div class="resize-handle resize-handle-bottom" @mousedown="startResize('bottom', $event)"></div>
    <div class="resize-handle resize-handle-corner" @mousedown="startResize('corner', $event)"></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import screenfull from 'screenfull'

const RTSP_PROXY_URL = 'http://localhost:8001'  // Python RTSP代理服务器地址

// 定义props和emits
const props = defineProps({
  position: {
    type: Object,
    default: () => ({ x: window.innerWidth - 480, y: 20 }) // 默认右上角位置
  },
  visible: {
    type: Boolean,
    default: false
  },
  initialSize: {
    type: Object,
    default: () => ({ width: 460, height: 360 }) // 修改默认大小
  }
})

const emit = defineEmits(['close'])

// 最小化和关闭控制
const isMinimized = ref(false)
const minimizePanel = () => {
  isMinimized.value = !isMinimized.value
}

const isStoppingStream = ref(false)  // 添加标志来防止重复停止

const closePanel = () => {
  if (!isStoppingStream.value) {  // 只有在没有停止过程中才调用stopVideoStream
    stopVideoStream()
  }
  emit('close')
}

// 拖动和大小相关
const panelRef = ref(null)
let isDragging = false
let isResizing = false
let resizeType = null
let dragStartX = 0
let dragStartY = 0
let initialX = 0
let initialY = 0
let initialWidth = 0
let initialHeight = 0
const customPosition = ref({ x: props.position.x, y: props.position.y })
const customSize = ref({ width: props.initialSize.width, height: props.initialSize.height })

// 开始拖动函数
const startDrag = (event) => {
  // 避免点击按钮时触发拖动
  if (event.target.closest('.panel-btn')) return
  
  isDragging = true
  dragStartX = event.clientX
  dragStartY = event.clientY
  initialX = customPosition.value.x
  initialY = customPosition.value.y
  
  document.addEventListener('mousemove', onDrag)
  document.addEventListener('mouseup', stopDrag)
}

// 拖动处理函数
const onDrag = (event) => {
  if (!isDragging) return
  
  const dx = event.clientX - dragStartX
  const dy = event.clientY - dragStartY
  
  customPosition.value = {
    x: initialX + dx,
    y: initialY + dy
  }
}

// 停止拖动函数
const stopDrag = () => {
  isDragging = false
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 开始调整大小
const startResize = (type, event) => {
  event.preventDefault()
  event.stopPropagation()
  
  isResizing = true
  resizeType = type
  dragStartX = event.clientX
  dragStartY = event.clientY
  initialWidth = customSize.value.width
  initialHeight = customSize.value.height
  initialX = customPosition.value.x
  initialY = customPosition.value.y
  
  document.addEventListener('mousemove', onResize)
  document.addEventListener('mouseup', stopResize)
}

// 调整大小处理函数
const onResize = (event) => {
  if (!isResizing) return
  
  const dx = event.clientX - dragStartX
  const dy = event.clientY - dragStartY
  
  // 最小尺寸限制
  const minWidth = 360
  const minHeight = 240
  
  if (resizeType === 'right' || resizeType === 'corner') {
    const newWidth = Math.max(initialWidth + dx, minWidth)
    customSize.value.width = newWidth
  }
  
  if (resizeType === 'bottom' || resizeType === 'corner') {
    const newHeight = Math.max(initialHeight + dy, minHeight)
    customSize.value.height = newHeight
  }
}

// 停止调整大小函数
const stopResize = () => {
  isResizing = false
  document.removeEventListener('mousemove', onResize)
  document.removeEventListener('mouseup', stopResize)
}

// 面板位置样式
const panelStyle = computed(() => {
  const width = customSize.value.width
  const height = isMinimized.value ? 50 : customSize.value.height
  
  // 确保面板不会超出屏幕边界
  let x = customPosition.value.x
  let y = customPosition.value.y
  
  if (x < 0) x = 0
  if (y < 0) y = 0
  if (x + width > window.innerWidth) x = window.innerWidth - width
  if (y + height > window.innerHeight) y = window.innerHeight - height
  
  return {
    left: `${x}px`,
    top: `${y}px`,
    width: `${width}px`,
    height: isMinimized.value ? '50px' : `${height}px`,
    display: props.visible ? 'block' : 'none'
  }
})

// 视频监控相关
const isVideoLoading = ref(false)
const isVideoPlaying = ref(false)
const videoPlayerRef = ref(null)
let videoPlayer = null

// 初始化视频播放器
const initVideoPlayer = async () => {
  console.log('开始初始化视频播放器')
  isVideoLoading.value = true
  
  // 确保视频容器有一个初始状态
  if (videoPlayerRef.value) {
    videoPlayerRef.value.innerHTML = '<div style="width:100%;height:100%;background-color:#000;"></div>';
  }
  
  try {
    // RTSP视频地址
    const rtspUrl = 'rtsp://admin:cyzy3q000@************/cam/realmonitor?channel=47&subtype=0&proto=Private3'
    console.log('使用RTSP地址:', rtspUrl)
    
    // 首选使用localhost作为代理服务器
    const proxyUrl = 'http://localhost:8001'
    
    try {
      console.log('检查代理服务器状态...')
      const response = await fetch(`${proxyUrl}/api/status`)
      const statusData = await response.json()
      
      if (statusData && statusData.status === 'ok') {
        console.log('RTSP代理服务正常运行')
        
        // 创建新的流
        // 使用时间戳确保名称唯一
        const streamName = `video_${Date.now()}`
        console.log('创建新的流:', streamName)
        
        try {
          const createResponse = await fetch(`${proxyUrl}/api/streams/create`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              url: rtspUrl,
              name: streamName,
              fps: 10,  // 使用较低的帧率以提高性能
              width: Math.min(640, customSize.value.width - 20),  // 根据面板宽度设置视频宽度
              height: Math.min(480, customSize.value.height - 100)  // 根据面板高度设置视频高度
            })
          })
          
          if (!createResponse.ok) {
            throw new Error(`创建流失败: ${createResponse.status}`)
          }
          
          const createData = await createResponse.json()
          
          if (createData && createData.success) {
            console.log('流创建成功')
            
            // 如果容器被销毁，直接返回
            if (!videoPlayerRef.value) {
              console.warn('视频容器已被销毁')
              return
            }
            
            // 创建img元素用于显示MJPEG流
            const imgUrl = `${proxyUrl}/api/stream/${streamName}/mjpeg?t=${Date.now()}`
            console.log('加载MJPEG流:', imgUrl)
            
            // 清空容器
            videoPlayerRef.value.innerHTML = ''
            
            // 创建新的img元素
            const imgElement = document.createElement('img')
            imgElement.src = imgUrl
            imgElement.style.width = '100%'
            imgElement.style.height = '100%'
            imgElement.style.objectFit = 'contain'
            
            // 添加到容器
            videoPlayerRef.value.appendChild(imgElement)
            
            // 设置视频状态
            videoPlayer = {
              streamName,
              proxyUrl,
              imgElement
            }
            
            // 图片加载事件
            imgElement.onload = () => {
              console.log('视频流加载成功')
              isVideoLoading.value = false
              isVideoPlaying.value = true
            }
            
            // 图片错误事件
            imgElement.onerror = (err) => {
              console.error('视频流加载失败:', err)
              
              // 尝试一次重连
              setTimeout(() => {
                if (imgElement && videoPlayerRef.value && videoPlayerRef.value.contains(imgElement)) {
                  imgElement.src = `${proxyUrl}/api/stream/${streamName}/mjpeg?t=${Date.now()}`
                }
              }, 1000)
              
              // 如果1秒后还是失败，显示错误
              setTimeout(() => {
                if (isVideoLoading.value) {
                  showVideoError('视频流加载失败', '请检查摄像头连接')
                }
              }, 2000)
            }
          } else {
            throw new Error('无法创建视频流')
          }
        } catch (error) {
          console.error('创建视频流出错:', error)
          showVideoError('无法创建视频流', '请检查摄像头连接')
        }
      } else {
        showVideoError('RTSP代理服务状态异常', '请检查代理服务是否正常运行')
      }
    } catch (error) {
      console.error('连接代理服务器出错:', error)
      showVideoError('RTSP代理服务未启动', '请先启动RTSP代理服务')
    }
  } catch (error) {
    console.error('视频初始化失败:', error)
    showVideoError('视频初始化失败', error.message || '未知错误')
  }
}

// 显示视频错误
const showVideoError = (title, message) => {
  console.error(`视频错误: ${title} - ${message}`)
  isVideoLoading.value = false
  
  if (videoPlayerRef.value) {
    videoPlayerRef.value.innerHTML = `
      <div class="video-error">
        <p>${title}</p>
        <p>${message}</p>
        <button id="retry-video-btn" class="retry-button">重试连接</button>
      </div>
    `
    
    const retryBtn = videoPlayerRef.value.querySelector('#retry-video-btn')
    if (retryBtn) {
      retryBtn.addEventListener('click', () => initVideoPlayer())
    }
  }
}

// 停止视频流
const stopVideoStream = async () => {
  console.log('停止视频流')
  
  // 如果已经在停止过程中，直接返回
  if (isStoppingStream.value) {
    console.log('视频流正在停止中，跳过重复调用')
    return
  }
  
  isStoppingStream.value = true
  isVideoPlaying.value = false
  
  try {
    if (!videoPlayer) {
      console.log('没有活动的视频播放器，无需停止')
      return
    }

    const currentVideoPlayer = videoPlayer // 保存当前实例的引用
    videoPlayer = null  // 立即清空全局引用，防止重复调用

    // 先移除图片元素的事件监听和src
    if (currentVideoPlayer.imgElement) {
      try {
        currentVideoPlayer.imgElement.onload = null
        currentVideoPlayer.imgElement.onerror = null
        currentVideoPlayer.imgElement.src = ''
      } catch (err) {
        console.warn('清理图片元素时出错:', err)
      }
    }

    // 停止RTSP流
    if (currentVideoPlayer.streamName) {
      try {
        const proxyUrl = currentVideoPlayer.proxyUrl || 'http://localhost:8001'
        console.log(`尝试停止流: ${currentVideoPlayer.streamName}`)
        const stopResponse = await fetch(`${proxyUrl}/api/streams/${currentVideoPlayer.streamName}/stop`, {
          method: 'POST'
        })
        
        if (stopResponse.ok) {
          console.log(`成功停止流: ${currentVideoPlayer.streamName}`)
        } else {
          console.warn(`停止流失败: ${stopResponse.status}`)
        }
      } catch (error) {
        console.warn('停止流时发生错误:', error)
        // 继续执行清理，即使停止流失败
      }
    }
    
    // 清理视频元素
    try {
      if (currentVideoPlayer.imgElement && videoPlayerRef.value) {
        const imgElement = videoPlayerRef.value.querySelector('img')
        if (imgElement) {
          imgElement.remove()
        }
      }
    } catch (err) {
      console.warn('移除视频元素时出错:', err)
    }
  } catch (error) {
    console.error('停止视频流过程中发生错误:', error)
  } finally {
    // 确保清除播放器内容
    try {
      if (videoPlayerRef.value) {
        videoPlayerRef.value.innerHTML = '<div style="width:100%;height:100%;background-color:#000;display:flex;align-items:center;justify-content:center;color:#999;font-size:14px;">点击播放按钮开始播放</div>'
      }
    } catch (err) {
      console.warn('重置播放器界面时出错:', err)
    }
    isStoppingStream.value = false  // 重置停止标志
  }
}

// 切换视频播放/暂停
const toggleVideoPlay = () => {
  console.log('切换视频播放状态')
  
  if (!videoPlayer) {
    console.log('视频播放器未初始化，现在初始化')
    // 如果没有视频播放器，则初始化
    isVideoPlaying.value = true
    initVideoPlayer()
    return
  }
  
  // 切换播放状态
  isVideoPlaying.value = !isVideoPlaying.value
  console.log('视频播放状态:', isVideoPlaying.value ? '播放' : '暂停')
  
  // 如果存在视频元素，根据播放状态切换显示
  if (videoPlayer && videoPlayer.imgElement) {
    if (isVideoPlaying.value) {
      // 从暂停到播放：显示视频并刷新流
      console.log('重新显示视频流')
      videoPlayer.imgElement.style.display = 'block'
      
      // 刷新视频流
      if (videoPlayer.proxyUrl && videoPlayer.streamName) {
        const refreshUrl = `${videoPlayer.proxyUrl}/api/stream/${videoPlayer.streamName}/mjpeg?t=${Date.now()}`
        console.log('刷新视频流URL:', refreshUrl)
        videoPlayer.imgElement.src = refreshUrl
      }
    } else {
      // 从播放到暂停：隐藏视频
      console.log('隐藏视频流')
      videoPlayer.imgElement.style.display = 'none'
    }
  } else {
    console.warn('视频播放器存在但没有图像元素')
  }
}

// 切换全屏
const toggleFullscreen = () => {
  if (!videoPlayerRef.value) return
  
  // 获取视频元素
  const video = videoPlayerRef.value.querySelector('video')
  const targetElement = video || videoPlayerRef.value
  
  try {
    if (!document.fullscreenElement) {
      // 进入全屏
      if (targetElement.requestFullscreen) {
        targetElement.requestFullscreen().catch(err => {
          console.error(`无法全屏: ${err.message}`)
        })
      } else if (targetElement.webkitRequestFullscreen) { // Safari
        targetElement.webkitRequestFullscreen()
      } else if (targetElement.msRequestFullscreen) { // IE11
        targetElement.msRequestFullscreen()
      }
    } else {
      // 退出全屏
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.webkitExitFullscreen) { // Safari
        document.webkitExitFullscreen()
      } else if (document.msExitFullscreen) { // IE11
        document.msExitFullscreen()
      }
    }
  } catch (err) {
    console.error('全屏切换失败:', err)
  }
}

// 生命周期
onMounted(() => {
  // 初始化自定义位置
  customPosition.value = { x: props.position.x, y: props.position.y }
  
  // 如果组件初始时是可见的，则启动视频播放
  if (props.visible && !isMinimized.value) {
    nextTick(() => {
      initVideoPlayer()
    })
  }
  
  // 添加窗口大小变化监听，确保面板位置不会超出屏幕
  window.addEventListener('resize', handleResize)
})

// 处理窗口大小变化
const handleResize = () => {
  const width = customSize.value.width
  const height = isMinimized.value ? 50 : customSize.value.height
  
  if (customPosition.value.x + width > window.innerWidth) {
    customPosition.value.x = window.innerWidth - width
  }
  
  if (customPosition.value.y + height > window.innerHeight) {
    customPosition.value.y = window.innerHeight - height
  }
}

onUnmounted(() => {
  // 清理拖动事件监听
  document.removeEventListener('mousemove', onDrag)
  document.removeEventListener('mouseup', stopDrag)
  
  // 清理调整大小事件监听
  document.removeEventListener('mousemove', onResize)
  document.removeEventListener('mouseup', stopResize)
  
  // 停止视频流
  stopVideoStream()
  
  // 清理视频相关资源
  if (videoPlayerRef.value) {
    videoPlayerRef.value.innerHTML = ''
  }
  
  // 移除窗口resize事件监听
  window.removeEventListener('resize', handleResize)
})

// 监听visible属性变化
watch(() => props.visible, (newValue) => {
  if (newValue && !videoPlayer && !isMinimized.value) {
    // 当面板变为可见状态且没有初始化视频时，初始化视频
    nextTick(() => {
      initVideoPlayer()
    })
  } else if (!newValue && videoPlayer) {
    // 当面板变为不可见状态且视频正在播放，停止视频
    stopVideoStream()
  }
})

// 监听面板尺寸变化
watch(() => customSize.value, (newSize) => {
  // 如果视频正在播放且面板大小变化，调整视频流尺寸
  if (videoPlayer && videoPlayer.imgElement) {
    // 根据新尺寸更新视频流容器样式，确保视频能适应新尺寸
    videoPlayer.imgElement.style.width = '100%'
    videoPlayer.imgElement.style.height = '100%'
    videoPlayer.imgElement.style.objectFit = 'contain'
  }
}, { deep: true })
</script>

<style scoped>
/* 面板整体布局 */
.video-panel {
  width: 360px;
  background-color: rgba(250, 250, 250, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  padding: 10px;
  color: #333;
  position: fixed;
  z-index: 1000;
  text-shadow: none;
  overflow: hidden;
  transition: all 0.3s ease;
  user-select: none;
}

/* 面板内容布局 */
.panel-content {
  display: flex;
  flex-direction: column;
  padding: 0 3px;
}

.panel-header {
  margin-bottom: 10px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  cursor: grab;
  padding-bottom: 5px;
}

.header-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.panel-buttons {
  display: flex;
  align-items: center;
}

.panel-btn {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  margin-left: 6px;
  cursor: pointer;
  font-weight: bold;
  font-size: 14px;
  color: white;
}

.minimize-btn {
  background-color: #faad14;
}

.close-btn {
  background-color: #f5222d;
}

.panel-header h3 {
  margin: 0;
  padding-bottom: 6px;
  color: #333;
  font-size: 1em;
}

/* 视频容器样式 */
.video-container {
  height: auto;
  padding: 0;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 4px;
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  z-index: 5;
}

/* 视频显示区域 */
.video-display-area {
  position: relative;
  width: 100%;
  height: 200px;
  background-color: #000;
  overflow: hidden;
}

/* 视频播放器 */
.video-player {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 2;
}

/* 视频加载中覆盖层 */
.video-loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  z-index: 3;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.video-controls {
  display: flex;
  padding: 8px;
  background-color: rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.control-btn {
  padding: 4px 10px;
  margin-right: 8px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  font-size: 12px;
}

.control-btn:hover {
  background-color: #40a9ff;
}

.play-icon, .pause-icon, .fullscreen-icon {
  display: inline-block;
  width: 12px;
  height: 12px;
  margin-right: 4px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.play-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTggNXYxNGwxMS03eiIvPjwvc3ZnPg==');
}

.pause-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTYgMTloNFY1SDZ2MTR6bTgtMTR2MTRoNFY1aC00eiIvPjwvc3ZnPg==');
}

.fullscreen-icon {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCI+PHBhdGggZmlsbD0iI2ZmZiIgZD0iTTcgMTRINXYyaDJ2LTJ6bTAtNEg1djJoMnYtMnptMC00SDV2MngyVjZ6bTQgMGgtMnYyaDJ2LTJ6bTAtNGgtM3YyaDJ2LTJ6bS0xNiA4SDN2MmgyVjEyem0wIDRIM3YyaDJ2LTJ6bTE2IDBIMTd2MmgyVjE2em0wLThoLTJ2MmgyVjh6bTAgOGgtMnYyaDJ2LTJ6TTMgNmgybTAgMGgzIi8+PC9zdmc+');
}

.video-error {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #ff4d4f;
  background-color: rgba(0, 0, 0, 0.85);
  text-align: center;
  padding: 10px;
}

.video-error p {
  margin: 4px 0;
  color: white;
}

.retry-button {
  margin-top: 10px;
  padding: 5px 12px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 13px;
}

.retry-button:hover {
  background-color: #40a9ff;
}

/* 添加拉伸手柄样式 */
.resize-handle {
  position: absolute;
  background-color: transparent;
  z-index: 100;
}

.resize-handle-right {
  right: 0;
  top: 0;
  width: 8px;
  height: 100%;
  cursor: e-resize;
}

.resize-handle-bottom {
  bottom: 0;
  left: 0;
  width: 100%;
  height: 8px;
  cursor: s-resize;
}

.resize-handle-corner {
  right: 0;
  bottom: 0;
  width: 15px;
  height: 15px;
  background-color: rgba(24, 144, 255, 0.2);
  border-radius: 0 0 4px 0;
  cursor: se-resize;
}

.resize-handle-corner:hover {
  background-color: rgba(24, 144, 255, 0.4);
}
</style> 