#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试演示TTS API
"""

import requests
import base64
import json
from pathlib import Path

# 服务配置
TTS_SERVICE_URL = "http://localhost:8002"

def test_health_check():
    """测试健康检查接口"""
    print("测试健康检查接口...")
    try:
        response = requests.get(f"{TTS_SERVICE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 健康检查通过: {data}")
            return True
        else:
            print(f"✗ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 健康检查异常: {e}")
        return False

def test_tts_api():
    """测试TTS API接口"""
    print("测试TTS API接口...")
    
    try:
        # 准备请求数据
        request_data = {
            "text": "你好，这是一个演示测试语音。",
            "voice_seed": 42,
            "temperature": 0.3,
            "top_p": 0.7,
            "top_k": 20,
            "refine_text": True
        }
        
        print("发送TTS请求...")
        
        response = requests.post(
            f"{TTS_SERVICE_URL}/tts",
            json=request_data,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✓ TTS请求成功")
            print(f"  - 消息: {data.get('message', '')}")
            print(f"  - 音频时长: {data.get('duration', 0):.2f}秒")
            print(f"  - 采样率: {data.get('sample_rate', 0)} Hz")
            
            # 保存音频文件
            if data.get('audio_base64'):
                audio_bytes = base64.b64decode(data['audio_base64'])
                output_file = Path("demo_test_output.wav")
                with open(output_file, "wb") as f:
                    f.write(audio_bytes)
                print(f"  - 演示音频已保存到: {output_file}")
                
            return True
        else:
            print(f"✗ TTS请求失败: {response.status_code}")
            print(f"  错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ TTS请求异常: {e}")
        return False

def test_demo_info():
    """测试演示信息接口"""
    print("测试演示信息接口...")
    try:
        response = requests.get(f"{TTS_SERVICE_URL}/demo/info")
        if response.status_code == 200:
            data = response.json()
            print(f"✓ 演示信息获取成功:")
            print(f"  - 模式: {data.get('mode', '')}")
            print(f"  - 描述: {data.get('description', '')}")
            print(f"  - 功能: {', '.join(data.get('features', []))}")
            print(f"  - 限制: {', '.join(data.get('limitations', []))}")
            return True
        else:
            print(f"✗ 演示信息获取失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ 演示信息获取异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("ChatTTS 演示服务测试")
    print("=" * 50)
    
    # 测试健康检查
    if not test_health_check():
        print("演示服务未正常运行，请检查服务状态")
        return
    
    print("\n" + "-" * 30)
    
    # 测试演示信息
    test_demo_info()
    
    print("\n" + "-" * 30)
    
    # 测试TTS API
    test_tts_api()
    
    print("\n" + "=" * 50)
    print("演示测试完成")
    print("注意: 这是演示版本，生成的是测试音频")
    print("要获得真实语音，请下载ChatTTS模型文件")

if __name__ == "__main__":
    main()
