# IOT Agent MVP

## 项目简介
这是一个基于 FastAPI 和 WebSocket 的物联网智能体系统，提供实时数据分析和智能交互功能。

## 系统架构

### 后端架构
- **FastAPI 服务器**：提供 RESTful API 和 WebSocket 支持
- **WebSocket 服务**：实现实时数据推送和双向通信
- **智能体系统**：
  - MySQL 智能体：处理数据库操作
  - 问题分类智能体：对用户输入进行分类
  - IoT 聊天智能体：处理物联网相关的对话
  - 多智能体系统：协调多个智能体的工作

### 目录结构
```
Backend/
├── app/
│   ├── agents/          # 智能体实现
│   ├── routes/          # API 路由
│   ├── websockets/      # WebSocket 实现
│   ├── utils/           # 工具函数
│   ├── core/            # 核心功能
│   └── main.py          # 主程序入口
├── uploads/             # 文件上传目录
└── requirements.txt     # Python 依赖
```

## 主要功能

### API 接口
- `/api/health`：系统健康检查
- `/api/toggle_logging`：日志级别控制
- WebSocket `/alerts`：实时警报推送

### 智能体功能
1. **MySQL 智能体**
   - 数据库查询和操作
   - 数据分析和处理

2. **问题分类智能体**
   - 用户输入分类
   - 意图识别

3. **IoT 聊天智能体**
   - 设备状态查询
   - 设备控制
   - 数据分析对话

4. **多智能体系统**
   - 智能体协调
   - 任务分配
   - 结果整合

## 开发环境设置

### 后端设置
1. 创建虚拟环境：
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
venv\Scripts\activate     # Windows
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 配置环境变量：
```bash
cp .env.example .env
# 编辑 .env 文件设置必要的环境变量
```

4. 启动服务：
```bash
python -m uvicorn app.main:app --reload
```

## 部署
项目包含 Docker 支持，可以使用以下命令构建和运行：
```bash
docker build -t iot-agent .
docker run -p 8000:8000 iot-agent
```

## 贡献指南
1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证
MIT License

# 物联网智能体增强应用最小需求分析

物联网 AI Agent 智能体最小可行性产品

## 1. 项目概述

### 1.1 项目目标

构建基于LLM的物联网智能交互系统，实现：
- 自然语言驱动的设备状态查询与分析
- RAG增强的运维知识检索
- 三维数字孪生场景的智能联动

### 1.2 项目范围

项目分为三个主要功能模块
- 前端展示模块：VUE3 开发，显示简易数字孪生场景，左侧加入聊天窗口
- 后端接口模块：基于 FastAPI，开发接口，连接前端模块和RAG模块
- RAG模块：基于 LangChain，实现索引、检索、生成三个主要流程

### 1.3 技术栈

* 后端服务框架: Fastapi、LangChain
* LLM 提供服务: Ollama
* LLM 模型: qwen2.5:32b
* 向量数据库: milvus
* 向量化模型: bge-m3
* MQTT Broker: emqx
* 前端页面: Vue3
* 数字孪生: Threejs
* 关系数据库：MySQL

## 2. 技术架构

```mermaid
flowchart LR
    C["用户语料"]
    A[终端设备] <-->|MQTT| B(EMQX Broker)
    C --> D[VUE3界面]
    E["`用户提问
    用户操作`"]    
    E --> D
    E --> F(Threejs引擎)
    B <--> G{FastAPI后端服务}
    D --> G
    F --> G
    G <---> H[(MySQL数据库)]
    G <--> RAG服务
    subgraph RAG服务
        direction LR
        i1[RAG索引]
        i2[RAG检索]
        i3[RAG生成]
    end
    RAG服务 <--> I[(Milvus向量库)]
    RAG服务 <--> J[[Ollama服务]]
```

## 3. 数据流示意图

```mermaid
sequenceDiagram
    设备->>EMQX: 发布实时数据(JSON)
    EMQX->>FastAPI: 转发数据包
    FastAPI->>MySQL: 存储历史数据
    Vue界面->>FastAPI: 提交用户问题
    FastAPI->>Milvus: 向量检索
    Milvus-->>FastAPI: 返回上下文
    FastAPI->>Ollama: 生成回答
    Ollama-->>Vue界面: 返回格式化结果
```

## 4. 核心模块功能需求

### 4.1 RAG服务功能需求

- 实现为上传文档创建索引
  - 支持常见的办公文档格式
  - 文档分块方式可以选择，最大限度保证语意完整性
- 实现以用户的提问进行检索，找到相关文档
- 实现用检索到的相关文档与用户的原问题进行合并向大语言模型提问
- 实现AI Agent的基础架构，具有自动规划能力，可用的智能体工具至少包括数据库查询、mqtt实时数据读取、EChart图表生成、Restful API接口调用等

### 4.2 前端页面及三维场景功能需求

- 使用 VUE3 开发，使用 Threejs 开发数字孪生场景。
- 在数字孪生场景中显示实时数据和功图信息
- 在数字孪生场景中打开大语言模型聊天，可以展示实时数据和功图信息，同时控制场景中的视图指向谈话主题设备。
- 提供上传语料文档的数据入口

### 4.3 FastAPI后端服务功能需求

- 使用 FastAPI 开发中转服务，提供 API 接口，连接 MQTT、RAG和前端VUE3页面，支持AI Agent中的流式输出SSE；
- 使用 LangChain 开发AI Agent，使用本地Ollama中的qwen2.5:32b模型，并开发必要的智能体工具；



## 5. 界面描述

- 页面样式说明
  - 三维场景：整体使用深蓝色具有科技感的天空盒做背景
  - 三维场景：画布中心为一个长方体平台上方放置五个正方体，颜色分别是红、蓝、绿、橙、紫。每个正方体前方分别有个长方体牌子，分别写着"井01"、"井02"、"井03"、"井04"、"井05"；
  - 信息面板：点击正方体后，出现信息面板，从上到下分为两个区域，上方为数据展示区，下方为图表展示区。点击空白区域或者按ESC后实时数据面板关闭。
  - 信息面板：数据展示区显示该井的实时数据，数据格式参考如下：{"井号": "井01", "温度": "25", "压力": "60", "产量": "101.3", "流量": "1000"}，数据每隔20秒更新一次
  - 信息面板：图表展示区显示油井的功图信息。在实时数据的下方有一张由 200个点生成的功图曲线，后端发送x，y 轴数值列表，前端绘制并展示。
  - 聊天面板：画面左下角有一聊天按钮，点击后弹出聊天面板，再次点击聊天按钮聊天面板可收回
  - 聊天面板：聊天面板中从上到下分别是滚动历史聊天记录区、消息发送区
  - 聊天面板：历史聊天记录中要能够渲染信息面板中的功图曲线信息和后端生成的其他图片
  - RAG文档管理面板：从上到下包括三个部分，上方为文件列表区域，显示已经向量化并存入向量库的文件
  - RAG文档管理面板：中间为上传文件选择框，可以通过拖拽上传，点击"向量化"按钮后开始上传
  - RAG文档管理面板：下方为信息栏，展示Milvus向量库中的记录条数等基本信息
  - 聊天面板、信息面板和RAG文档管理面板背景不透明度均为70%
- 页面交互功能说明
  - 实现鼠标对三维场景的常规操作，如左键可以旋转视角或选中正方体，用滚轮可以调整视图大小，鼠标右键可以平移画面等
  - 点击正方体后，正方体变外发光特效，同时右侧显示信息面板，点击空白区域或者按ESC后信息面板关闭。高亮特效消失。
  - 信息面板中实时数据直接使用MQTT协议从EMQX中读取。
  - 聊天面板中可以进行简单的问答，问答内容与数字孪生系统联动，随着对话的主题进行切换视图中的主要目标。如聊天信息中询问"井01"的信息，画布视图自动旋转并移动到以"井01"为中心。
  - 上传文件完成 RAG 索引后，有弹窗提示创建索引成功


## 6. 开发计划
| 阶段   | 周期   | 交付物                          |
|--------|--------|---------------------------------|
| v0.01  | 1天    | 环境部署：docker-compose.yml部署相关服务，最小可运行的三个服务  |
| v0.1   | 2天    | 前端页面：前端界面满足要求         |
| v0.2   | 2天    | RAG 功能：实现通过前端上传文档向量化存储，把通过聊天窗口发过去的文字向量化进行检索  |
| v0.3   | 2天    | Agent功能：开发智能体工具，利用大语言模型自动调用工具完成全流程任务  |
| v0.4   | 1天    | 更新发布流程，快速部署，对需要的语料提出要求  |






## 7. 附录
###  7.1 术语表
| 术语 | 定义 |
|------|------|
| RAG | 检索增强生成，结合知识库的AI问答技术 |
| MQTT | 轻量级物联网通信协议 |
| WebGL | 网页3D渲染标准 |


### 7.2 MQTT报文定义

MQTT 通过 `ws://` 协议进行访问
访问地址为 `ws://192.168.66.16:/mqtt` 
订阅 Topic 格式为 `场站/设备名称`
如 1#站，HN15-25井访问 Topic 为 `/HN3S1/HN15V23`
返回信息为 JSON 格式，示例如下：

```json
{
  "Station": "HN3S1",
  "Wellname": "HN15V25",
  "Status": 0,
  "Check_date": "2025-03-06 10:30:52",
  "Dyna_points": 200,
  "Displacement": "0.00|0.05|0.10|0.14|0.19|0.24|0.29|0.33|0.38|0.43|0.48|0.53|0.57|0.62|0.67|0.72|0.77|0.81|0.86|0.91|0.96|1.00|1.05|1.10|1.15|1.20|1.24|1.29|1.34|1.39|1.44|1.48|1.53|1.58|1.63|1.67|1.72|1.77|1.82|1.87|1.91|1.96|2.01|2.06|2.11|2.15|2.20|2.25|2.30|2.34|2.39|2.44|2.49|2.54|2.58|2.63|2.68|2.73|2.78|2.82|2.87|2.92|2.97|3.01|3.06|3.11|3.16|3.21|3.25|3.30|3.35|3.40|3.44|3.49|3.54|3.59|3.64|3.68|3.73|3.78|3.83|3.88|3.92|3.97|4.02|4.07|4.11|4.16|4.21|4.26|4.31|4.35|4.40|4.45|4.50|4.55|4.59|4.64|4.69|4.74|4.78|4.74|4.69|4.64|4.59|4.55|4.50|4.45|4.40|4.35|4.31|4.26|4.21|4.16|4.11|4.07|4.02|3.97|3.92|3.88|3.83|3.78|3.73|3.68|3.64|3.59|3.54|3.49|3.44|3.40|3.35|3.30|3.25|3.21|3.16|3.11|3.06|3.01|2.97|2.92|2.87|2.82|2.78|2.73|2.68|2.63|2.58|2.54|2.49|2.44|2.39|2.34|2.30|2.25|2.20|2.15|2.11|2.06|2.01|1.96|1.91|1.87|1.82|1.77|1.72|1.67|1.63|1.58|1.53|1.48|1.44|1.39|1.34|1.29|1.24|1.20|1.15|1.10|1.05|1.00|0.96|0.91|0.86|0.81|0.77|0.72|0.67|0.62|0.57|0.53|0.48|0.43|0.38|0.33|0.29|0.24|0.19|0.14|0.10|0.05",
  "Disp_current": "86.65245|89.41089|89.54511|92.37818|96.02876|96.40621|100.10275|102.99226|102.10249|105.97499|107.97178|109.03003|109.31042|112.07773|111.96524|115.36522|113.71398|115.04466|116.25529|117.21270|115.72578|117.59450|116.04361|117.46662|119.13292|118.94120|117.98051|120.19810|119.66049|120.48697|119.23245|118.93317|120.34371|120.62059|117.67462|116.50255|114.74705|115.58226|116.34857|116.38421|119.23648|118.13560|119.04885|115.21961|113.81775|120.67252|114.80422|115.12424|119.74660|115.87853|113.99456|113.97571|116.11832|121.09436|112.83834|112.47689|117.15942|113.56528|113.53240|115.16952|115.87398|112.69277|113.49763|115.60369|117.55047|118.61771|114.38405|120.68270|115.69131|116.74481|116.07962|115.91808|117.79107|113.99340|112.85058|118.40161|113.31321|114.56973|120.85883|120.31032|112.71429|115.56896|117.77258|117.83601|120.42852|114.33712|119.02291|113.76293|117.88493|118.89716|119.18688|118.07969|114.73109|116.04229|116.17412|115.23702|114.08302|117.84862|112.99094|116.77490|116.29283|114.76489|113.40054|111.06264|110.91768|110.29586|108.88211|106.77701|107.71714|104.50823|104.47207|102.13602|101.90686|101.16002|99.45827|98.42348|96.30860|96.63858|95.90879|95.65155|93.68670|90.89272|93.82506|90.72073|89.21203|90.23137|89.91252|90.86825|88.58901|86.09774|86.10958|87.39984|87.95387|85.50995|87.29682|87.61459|89.28320|89.83417|88.72925|87.99857|90.56512|90.76817|92.34642|89.69784|93.17757|91.77647|92.48059|88.56553|91.28007|92.56759|93.63791|88.94797|91.16528|85.86136|83.88958|81.55531|85.99428|84.32862|82.38772|82.63513|80.78985|83.91337|85.32028|81.87335|92.88389|85.28589|85.21667|92.13628|87.45871|84.12603|90.06597|80.04494|92.51986|87.07036|92.86554|90.84842|87.25225|91.77610|89.37804|92.02671|93.00990|92.79125|83.97987|87.30135|82.45367|89.44938|91.02934|88.89072|91.05132|91.28934|88.86162|88.60623|85.09749|92.88701|91.07796|91.57237|93.84454|82.11866|81.58309|83.18874",
  "Disp_load": "20.09535|22.19995|23.19562|24.11038|22.79975|23.52817|26.88626|25.14899|25.84206|28.54004|26.73919|28.52115|28.64329|31.48348|32.60160|32.71850|31.17588|33.24413|32.97420|34.87505|34.66767|34.16426|32.91069|34.70399|33.52750|33.74675|36.18416|34.47698|37.46952|38.24786|37.56958|36.73412|34.92170|36.38715|38.65944|35.75727|38.90581|38.37181|38.52770|38.71291|41.07069|39.34444|39.71013|38.49731|40.68066|38.40661|41.58210|39.45400|41.42436|35.20801|40.69019|33.83391|38.70542|37.51845|37.76690|39.03195|33.68493|33.78744|38.59501|33.51789|34.34168|39.75661|37.66265|32.17851|35.52179|36.45081|32.93446|33.99553|37.23950|38.41159|32.30206|34.63578|38.01249|41.05482|35.84463|37.20214|40.15303|40.17089|34.93230|33.14093|36.38519|38.28615|37.76904|41.18091|39.68782|35.05088|35.78066|38.77395|38.15832|34.49275|38.99500|39.84788|34.68707|38.15775|34.63449|40.12983|32.91873|36.38668|37.99005|41.39836|40.05391|37.32391|37.56657|36.11502|32.68206|33.86826|31.25074|30.59249|28.93105|25.72957|25.98888|23.56774|24.19833|24.78228|22.78574|20.94316|19.48564|19.09385|20.48001|20.20152|21.64617|20.03094|19.88442|23.35923|24.49516|25.63449|24.87110|26.00836|23.31927|26.29057|25.57965|24.10723|21.45799|22.46062|25.92147|22.23899|14.88908|14.48439|13.21046|20.47244|20.39366|22.40776|21.08957|21.60137|20.15502|20.44925|15.67359|23.91737|16.54768|16.38480|20.96959|16.43569|15.92673|16.71293|14.44912|22.14193|16.05402|13.97515|24.48563|15.50045|23.18685|22.85680|14.14540|24.46747|18.23198|16.74112|19.38771|20.48926|19.89648|23.83828|21.21199|17.28769|23.82902|26.60581|13.98452|24.19026|24.23411|25.24608|14.89033|22.76720|25.65045|24.78927|24.03799|22.59282|22.79503|24.09142|13.59279|23.04343|14.14122|20.54382|23.35220|16.88639|16.67560|18.14380|22.18013|14.41042|20.54908|20.84810|16.68745|19.52411",
  "Values": [
    {
      "id": "ADL",
      "Value": 98.87499999999999
    },
    {
      "id": "BDL",
      "Value": 30.875
    },
    {
      "id": "CDL",
      "Value": 81.048
    },
    {
      "id": "ADY",
      "Value": 317.34
    },
    {
      "id": "BDY",
      "Value": 357.2
    },
    {
      "id": "CDY",
      "Value": 384.15999999999997
    },
    {
      "id": "SLV",
      "Value": 4.183
    },
    {
      "id": "CHC",
      "Value": 5.05
    },
    {
      "id": "GYS",
      "Value": 0.7878000000000001
    },
    {
      "id": "ZYG",
      "Value": 3191.54
    },
    {
      "id": "ZWG",
      "Value": 1656.0
    },
    {
      "id": "UWL",
      "Value": 50.096000000000004
    },
    {
      "id": "DWL",
      "Value": 26.52
    },
    {
      "id": "WBR",
      "Value": 70.68
    },
    {
      "id": "YGL",
      "Value": 619941.8600000001
    },
    {
      "id": "CPV",
      "Value": 1.482
    },
    {
      "id": "WIP",
      "Value": 1.456
    },
    {
      "id": "TWT",
      "Value": 53.58
    }
  ]
}
```
其中，key对应的含义如下：

| key        | 含义     |    描述                          |
|------------|----------|---------------------------------|
| Station    | 所属场站  |  如`HN3S1`代表海南三区1号站 |
| Wellname   | 通用井名  | 与 A2 系统对应的井名        |
| Status     | 在线状态  | 0：表示通讯正常 非零其他数值均代码通讯异常的故障码  |
| Check_date   | 功图采集时间 | 格式：yyyy-mm-dd hh:mm:ss  |
| Dyna_points  | 功图点数 |   |
| Disp_load    | 载荷    | 功图y轴数据，一次完整行程中载荷的记录点，用`|`进行分割  |
| Disp_current | 电流    | 功图y轴数据，一次完整行程中电流的记录点，用`|`进行分割   |
| Displacement | 位移    | 功图x轴数据，一次完整行程中位移的记录点，用`|`进行分割   |
| Values     | 数据集合  | 包含所有仪表采集到的实时数据   |
| Values/TWT | 井口温度   | 单位 ℃  |
| Values/WIP | 井口压力   | 单位 Mpa  |
| Values/CPV | 套压       | 单位 Mpa  |
| Values/SLV | 冲程      | 单位 m     |
| Values/CHC | 冲次      | 单位 次/分 |
| Values/UWL | 最大载荷  | 单位 KN |
| Values/DWL | 最小载荷  | 单位 KN |
| Values/WBR | 平衡度    |         |
| Values/ADL | A 相电流  | 单位 A   |
| Values/BDL | B 相电流  | 单位 A   |
| Values/CDL | C 相电流  | 单位 A   |
| Values/ADY | A 相电压  | 单位 V   |
| Values/BDY | B 相电压  | 单位 V   |
| Values/CDY | C 相电压  | 单位 V   |
| Values/GYS | 功率因数  |             |
| Values/ZYG | 总有功功率 |  单位 KW    |
| Values/ZWG | 总无功功率 |  单位 KVar  |
| Values/YGL | 有功用电量 | 单位 KWh    |

