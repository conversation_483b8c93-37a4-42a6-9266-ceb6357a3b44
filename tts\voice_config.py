#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS音色和参数配置文件
"""

# 音色配置
VOICE_CONFIGS = {
    "serious_male": {
        "voice_seed": 42,
        "audio_seed": 123,
        "temperature": 0.1,
        "top_p": 0.5,
        "top_k": 10,
        "description": "严肃、智慧的男性声音"
    },
    "gentle_female": {
        "voice_seed": 88,
        "audio_seed": 456,
        "temperature": 0.2,
        "top_p": 0.6,
        "top_k": 15,
        "description": "温和的女性声音"
    },
    "professional_male": {
        "voice_seed": 156,
        "audio_seed": 789,
        "temperature": 0.05,
        "top_p": 0.4,
        "top_k": 8,
        "description": "专业播音员男性声音"
    },
    "calm_narrator": {
        "voice_seed": 234,
        "audio_seed": 321,
        "temperature": 0.15,
        "top_p": 0.55,
        "top_k": 12,
        "description": "平静的叙述者声音"
    }
}

# 默认使用的音色配置
DEFAULT_VOICE = "serious_male"

# 文本预处理配置
TEXT_PREPROCESSING_CONFIG = {
    "remove_markdown": True,
    "normalize_whitespace": True,
    "add_pause_markers": True,
    "handle_english_words": True,
    "handle_numbers": True,
    "remove_html_tags": True
}

# 停顿标记配置
PAUSE_MARKERS = {
    "sentence_end": " [pause] ",  # 句号、问号、感叹号后的停顿
    "comma": " [short_pause] ",   # 逗号、分号后的短停顿
    "paragraph": " [long_pause] " # 段落间的长停顿
}

def get_voice_config(voice_name: str = None) -> dict:
    """
    获取指定音色的配置
    
    Args:
        voice_name: 音色名称，如果为None则使用默认音色
        
    Returns:
        dict: 音色配置字典
    """
    if voice_name is None:
        voice_name = DEFAULT_VOICE
    
    if voice_name not in VOICE_CONFIGS:
        raise ValueError(f"未知的音色配置: {voice_name}，可用配置: {list(VOICE_CONFIGS.keys())}")
    
    return VOICE_CONFIGS[voice_name].copy()

def list_available_voices() -> dict:
    """
    列出所有可用的音色配置
    
    Returns:
        dict: 音色名称和描述的字典
    """
    return {name: config["description"] for name, config in VOICE_CONFIGS.items()}

def validate_voice_config(config: dict) -> bool:
    """
    验证音色配置的有效性
    
    Args:
        config: 音色配置字典
        
    Returns:
        bool: 配置是否有效
    """
    required_keys = ["voice_seed", "audio_seed", "temperature", "top_p", "top_k"]
    
    for key in required_keys:
        if key not in config:
            return False
    
    # 验证参数范围
    if not (0.0 <= config["temperature"] <= 1.0):
        return False
    if not (0.0 <= config["top_p"] <= 1.0):
        return False
    if not (1 <= config["top_k"] <= 100):
        return False
    
    return True

# 预定义的测试文本
TEST_TEXTS = {
    "simple": "这是一个简单的测试文本。",
    "mixed": "这段文本包含中文、English words和数字123。",
    "markdown": """
# 标题测试

这是一段包含**粗体**和*斜体*的文本。

- 列表项目1
- 列表项目2

[链接文本](https://example.com)

`代码片段`

> 引用文本

包含多种格式的复杂文本。
""",
    "technical": "在深度学习中，Transformer架构使用self-attention机制，其计算复杂度为O(n²)。",
    "numbers": "今天的温度是25.6摄氏度，湿度为78%，风速为3.2米每秒。",
    "punctuation": "你好！这是测试文本。包含各种标点符号：逗号，分号；问号？感叹号！"
}

def get_test_text(text_type: str = "simple") -> str:
    """
    获取测试文本
    
    Args:
        text_type: 文本类型
        
    Returns:
        str: 测试文本
    """
    if text_type not in TEST_TEXTS:
        raise ValueError(f"未知的测试文本类型: {text_type}，可用类型: {list(TEST_TEXTS.keys())}")
    
    return TEST_TEXTS[text_type]
