import paho.mqtt.client as mqtt
import json
import logging


class MQTTClient:
    def __init__(self, host="************", port=8083, path="/mqtt"):
        self.client = mqtt.Client(transport="websockets")
        self.host = host
        self.port = port
        self.path = path

        # 设置websocket路径
        self.client.ws_set_options(path=self.path)

        # 设置回调
        self.client.on_connect = self._on_connect
        self.client.on_disconnect = self._on_disconnect
        self.client.on_publish = self._on_publish

        self.connected = False

    def _on_connect(self, client, userdata, flags, rc):
        """连接回调"""
        if rc == 0:
            self.connected = True
            logging.info("已成功连接到MQTT服务器")
        else:
            self.connected = False
            logging.error(f"连接MQTT服务器失败,返回码: {rc}")

    def _on_disconnect(self, client, userdata, rc):
        """断开连接回调"""
        self.connected = False
        if rc != 0:
            logging.warning("意外断开MQTT连接")
        else:
            logging.info("已断开MQTT连接")

    def _on_publish(self, client, userdata, mid):
        """发布消息回调"""
        logging.debug(f"消息发布确认 - 消息ID: {mid}")

    def _on_message(self, client, userdata, message):
        """接收消息回调"""
        try:
            payload = json.loads(message.payload.decode())
            logging.info(f"收到消息 - 主题: {message.topic}, QoS: {message.qos}")
            logging.debug(f"消息内容: {payload}")
        except json.JSONDecodeError:
            logging.warning(f"收到的消息不是有效的JSON格式 - 主题: {message.topic}")
            logging.debug(f"原始消息: {message.payload}")

    def connect(self):
        """连接到MQTT服务器"""
        try:
            # 设置接收消息回调
            self.client.on_message = self._on_message
            
            # 连接服务器
            self.client.connect(self.host, self.port, 60)
            self.client.loop_start()
        except Exception as e:
            logging.error(f"连接MQTT服务器时出错: {e}")
            return False
        return True

    def subscribe(self, topic, qos=1):
        """订阅指定主题
        
        Args:
            topic: 要订阅的主题
            qos: 服务质量等级(0, 1, 2)
            
        Returns:
            bool: 订阅是否成功
        """
        if not self.connected:
            logging.error("未连接到MQTT服务器,无法订阅主题")
            return False
            
        try:
            logging.info(f"准备订阅主题: {topic}, QoS: {qos}")
            result, mid = self.client.subscribe(topic, qos)
            
            if result != mqtt.MQTT_ERR_SUCCESS:
                logging.error(f"订阅主题失败: {mqtt.error_string(result)}")
                return False
                
            logging.info(f"成功订阅主题: {topic}")
            return True
            
        except Exception as e:
            logging.error(f"订阅主题时出错: {str(e)}")
            return False

    def disconnect(self):
        """断开MQTT连接"""
        try:
            self.client.loop_stop()
            self.client.disconnect()
            logging.info("已断开MQTT连接")
        except Exception as e:
            logging.error(f"断开连接时出错: {str(e)}")

    def publish_data(self, topic, payload, qos=1, retain=True):
        """发布数据到指定主题
        
        Args:
            topic: 发布主题
            payload: 要发布的数据(字典格式)
            qos: 服务质量等级(0, 1, 2)
            retain: 是否保留消息
        
        Returns:
            bool: 发布是否成功
        """
        if not self.connected:
            logging.error("未连接到MQTT服务器")
            return False

        try:
            message = json.dumps(payload)
            logging.info(f"准备发布消息到主题: {topic}")
            logging.debug(f"消息内容: {payload}")
            logging.debug(f"QoS: {qos}, Retain: {retain}")

            result = self.client.publish(topic, message, qos=qos, retain=retain)
            
            if result.rc != mqtt.MQTT_ERR_SUCCESS:
                logging.error(f"发布消息失败: {mqtt.error_string(result.rc)}")
                return False
                
            # 等待消息发送完成
            result.wait_for_publish()
            logging.info(f"消息成功发布到主题: {topic}")
            return True
            
        except Exception as e:
            logging.error(f"发布消息时出错: {str(e)}")
            return False
