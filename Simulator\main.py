import sys
import logging
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer
from gui import MainWindow
from mqtt_client import MQTT<PERSON>lient
from data_generator import DataGenerator

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)


class DevicePublisher:
    """设备发布器"""

    def __init__(self, mqtt_client, station, wellname):
        self.mqtt_client = mqtt_client
        self.station = station
        self.wellname = wellname
        self.data_generator = DataGenerator()
        self.topic = f"/{station}/{wellname}"

    def publish(self):
        """发布设备数据"""
        try:
            data = self.data_generator.generate_data(
                station=self.station,
                wellname=self.wellname
            )
            return self.mqtt_client.publish_data(self.topic, data), data
        except Exception as e:
            logging.error(f"发布数据失败: {e}")
            return False, None


class Simulator:
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.window = MainWindow()
        self.mqtt_client = None
        self.publishers = {}  # 存储设备发布器

        # 连接信号
        self.window.connect_btn.clicked.connect(self.toggle_connection)
        self.window.start_btn.clicked.connect(self.start_publishing)
        self.window.stop_btn.clicked.connect(self.stop_publishing)
        self.window.start_device.connect(self.start_device_publishing)
        self.window.stop_device.connect(self.stop_device_publishing)

    def toggle_connection(self):
        """处理连接/断开操作"""
        if self.mqtt_client is None or not self.mqtt_client.connected:
            # 获取配置并连接
            config = self.window.get_mqtt_config()
            self.mqtt_client = MQTTClient(**config)
            if self.mqtt_client.connect():
                self.window.connect_btn.setText("断开")
                # 启用设备管理控件
                self.window.start_btn.setEnabled(True)
                # 启用批量控制按钮
                self.window.start_all_btn.setEnabled(True)
                self.window.stop_all_btn.setEnabled(True)
            else:
                logging.error("连接失败")
        else:
            # 停止所有设备发布
            self.stop_all_publishing()
            # 断开连接
            self.mqtt_client.disconnect()
            self.mqtt_client = None
            self.window.connect_btn.setText("连接")
            # 禁用设备管理控件
            self.window.start_btn.setEnabled(False)
            self.window.stop_btn.setEnabled(False)
            # 禁用批量控制按钮
            self.window.start_all_btn.setEnabled(False)
            self.window.stop_all_btn.setEnabled(False)

    def create_publisher(self, station, wellname):
        """创建设备发布器"""
        if not self.mqtt_client or not self.mqtt_client.connected:
            logging.error("未连接到MQTT服务器")
            return None

        key = (station, wellname)
        if key not in self.publishers:
            self.publishers[key] = DevicePublisher(
                self.mqtt_client, station, wellname
            )
        return self.publishers[key]

    def start_device_publishing(self, station, wellname):
        """开始发布指定设备的数据"""
        publisher = self.create_publisher(station, wellname)
        if not publisher:
            return

        # 获取定时器
        timer = self.window.device_timers.get((station, wellname))
        if timer and not timer.isActive():
            # 设置定时器回调
            timer.timeout.connect(
                lambda: self.publish_device_data(station, wellname)
            )
            # 启动定时器
            interval = self.window.get_publish_interval() * 1000
            timer.start(interval)
            # 更新设备状态
            self.window.update_device_status(station, wellname, 1)  # 发布中
            # 立即发布一次
            self.publish_device_data(station, wellname)

    def stop_device_publishing(self, station, wellname):
        """停止发布指定设备的数据"""
        # 停止定时器
        timer = self.window.device_timers.get((station, wellname))
        if timer and timer.isActive():
            timer.stop()
            # 更新设备状态
            self.window.update_device_status(station, wellname, 0)  # 已停止

    def start_publishing(self):
        """开始发布选中设备的数据"""
        devices = self.window.get_selected_devices()
        if not devices:
            logging.error("未选择设备")
            return

        for station, wellname in devices:
            self.start_device_publishing(station, wellname)
            
        # 更新按钮状态
        self.window.start_btn.setEnabled(False)
        self.window.stop_btn.setEnabled(True)

    def stop_publishing(self):
        """停止发布选中设备的数据"""
        devices = self.window.get_selected_devices()
        if not devices:
            return

        for station, wellname in devices:
            self.stop_device_publishing(station, wellname)
            
        # 更新按钮状态
        self.window.start_btn.setEnabled(True)
        self.window.stop_btn.setEnabled(False)

    def stop_all_publishing(self):
        """停止所有设备的发布"""
        for (station, wellname), timer in self.window.device_timers.items():
            if timer.isActive():
                timer.stop()
                self.window.update_device_status(station, wellname, 0)  # 已停止

    def publish_device_data(self, station, wellname):
        """发布指定设备的数据"""
        publisher = self.publishers.get((station, wellname))
        if publisher:
            success, data = publisher.publish()
            if success:
                # 更新为已发布状态
                self.window.update_device_status(station, wellname, 2)  # 已发布
                if (station, wellname) == self.window.get_selected_device():
                    self.window.update_preview(data)
                # 延迟更新回发布中状态（改为3秒，让状态更容易看到）
                QTimer.singleShot(3000, lambda: self.window.update_device_status(station, wellname, 1))
            else:
                self.stop_publishing()

    def run(self):
        """运行应用程序"""
        self.window.show()
        return self.app.exec()


if __name__ == "__main__":
    simulator = Simulator()
    sys.exit(simulator.run())
