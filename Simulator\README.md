# MQTT设备数据仿真器

## 1. 功能说明

### 1.1 主要功能

- 多设备数据仿真
  - 支持同时模拟多个设备数据
  - 独立配置和控制每个设备
  - 实时显示设备发布状态

- MQTT数据发布
  - 使用WebSocket连接MQTT Broker
  - 支持自定义发布频率(默认20秒)
  - 自动重连机制

- 数据生成
  - 符合规范的JSON格式数据
  - 动态生成功图曲线数据
  - 模拟真实设备参数波动

### 1.2 界面功能

- 设备管理
  - 设备列表显示和管理
  - 支持添加/删除设备
  - 设备状态实时更新

- 参数配置
  - MQTT服务器连接配置
  - 设备参数配置(场站/井名)
  - 发布频率调整

- 数据预览
  - JSON格式实时数据预览
  - 设备发布状态显示

## 2. 目录结构

```
Simulator/
├── requirements.txt    # 项目依赖
├── main.py            # 主程序入口
├── gui.py             # PyQt6界面实现
├── mqtt_client.py     # MQTT客户端封装
├── data_generator.py  # 数据生成器
└── README.md          # 说明文档
```

## 3. 数据格式说明

### 3.1 MQTT主题格式

```
/{station}/{wellname}
```

示例: `/HN3S1/HN15V25`

### 3.2 数据包格式

```json
{
    "Station": "HN3S1",        // 场站编号
    "Wellname": "HN15V25",     // 井名
    "status": 0,               // 设备状态(0:正常)
    "check_date": "2024-03-05 16:30:00",  // 数据采集时间
    "dyna_points": 128,        // 功图点数
    "Payload": "x1|x2|...|xn", // 功图x轴数据
    "Offset": "y1|y2|...|yn",  // 功图y轴数据
    "Values": [                 // 设备参数数组
        {
            "id": "ADL",        // 参数ID
            "Value": 98         // 参数值
        },
        // ... 其他参数
    ]
}
```

## 4. 使用说明

### 4.1 环境配置

```bash
# 安装依赖
pip install -r requirements.txt
```

### 4.2 启动程序

```bash
python main.py
```

### 4.3 操作流程

1. 配置MQTT连接
   - 输入服务器地址(默认: *************)
   - 输入WebSocket端口(默认: 80)
   - 输入WebSocket路径(默认: /mqtt)
   - 点击"连接"按钮

2. 添加设备
   - 输入场站编号和井名
   - 点击"添加设备"按钮
   - 在设备列表中选择设备

3. 控制发布
   - 设置发布间隔(1-60秒)
   - 点击"开始发布"开始发送数据
   - 点击"停止发布"停止发送数据

## 5. 实现细节

### 5.1 数据生成算法

- 功图曲线生成
  - 使用正弦函数生成基础曲线
  - 添加随机波动模拟真实数据
  - 保持上行程和下行程的连续性

- 设备参数生成
  - 基于基准值添加随机波动
  - 确保数值在合理范围内
  - 模拟参数间的关联关系

### 5.2 MQTT通信

- WebSocket连接
  - 支持断线自动重连
  - 异常状态监控和恢复
  - 支持多设备并发发布

- 数据发布机制
  - 独立的设备定时器
  - 错误重试机制
  - 发布状态实时反馈

### 5.3 界面实现

- 多设备管理
  - 设备状态独立维护
  - 支持批量操作
  - 实时状态更新

- 数据预览
  - 格式化JSON显示
  - 自动滚动更新
  - 错误状态提示

## 6. API说明

### 6.1 设备参数ID对照表

| 参数ID       | 说明     |    描述                          |
|------------|----------|---------------------------------|
| Station    | 所属场站  |  如`HN3S1`代表海南三区1号站 |
| Wellname   | 通用井名  | 与 A2 系统对应的井名        |
| status     | 在线状态  | 0：表示通讯正常 非零其他数值均代码通讯异常的故障码  |
| check_date   | 功图采集时间 | 格式：yyyy-mm-dd hh:mm:ss  |
| dyna_points  | 功图点数 |   |
| disp_load    | 载荷    | 功图y轴数据，一次完整行程中载荷的记录点，用`|`进行分割  |
| disp_current | 电流    | 功图y轴数据，一次完整行程中电流的记录点，用`|`进行分割   |
| displacement | 位移    | 功图x轴数据，一次完整行程中位移的记录点，用`|`进行分割   |
| Values     | 数据集合  | 包含所有仪表采集到的实时数据   |
| Values/TWT | 井口温度   | 单位 ℃  |
| Values/WIP | 井口压力   | 单位 Mpa  |
| Values/CPV | 套压       | 单位 Mpa  |
| Values/SLV | 冲程      | 单位 m     |
| Values/CHC | 冲次      | 单位 次/分 |
| Values/UWL | 最大载荷  | 单位 KN |
| Values/DWL | 最小载荷  | 单位 KN |
| Values/WBR | 平衡度    |         |
| Values/ADL | A 相电流  | 单位 A   |
| Values/BDL | B 相电流  | 单位 A   |
| Values/CDL | C 相电流  | 单位 A   |
| Values/ADY | A 相电压  | 单位 V   |
| Values/BDY | B 相电压  | 单位 V   |
| Values/CDY | C 相电压  | 单位 V   |
| Values/GYS | 功率因数  |             |
| Values/ZYG | 总有功功率 |  单位 KW    |
| Values/ZWG | 总无功功率 |  单位 KVar  |
| Values/YGL | 有功用电量 | 单位 KWh    |
