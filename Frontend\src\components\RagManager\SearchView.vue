<template>
  <div class="search-view" style="background: white; color: #333;">
    <!-- 搜索框和功能区域 -->
    <div class="search-container" style="background: white; border: 1px solid #e5e5e5; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); padding: 16px; border-radius: 8px; margin-bottom: 16px;">
      <div class="search-header">
        <h3 style="color: #1890ff;">文档检索</h3>
        <div class="search-description" style="color: #666;">从已上传的文档中搜索相关内容，支持模糊匹配和精确关键词搜索</div>
      </div>

      <div class="search-box">
        <!-- 搜索主体区域 -->
        <div class="search-main">
          <!-- 搜索输入框行 -->
          <div class="search-input-row">
            <input 
              type="text" 
              v-model="searchQuery" 
              placeholder="请输入搜索关键词..." 
              class="search-input"
              @keyup.enter="performSearch"
              style="background: #f5f5f5; color: #333; border: 1px solid #d9d9d9;"
            >
          </div>

          <!-- 相似度阈值和关键词搜索开关 -->
          <div class="search-options-row">
            <div class="similarity-slider">
              <div class="similarity-controls">
                <label class="similarity-label" style="color: #333;">相似度阈值</label>
                <div class="similarity-input-group">
                  <input 
                    type="number" 
                    v-model.number="similarity"
                    min="0"
                    max="100"
                    step="1"
                    class="similarity-input"
                    @input="validateSimilarity"
                    style="background: #f5f5f5; color: #333; border: 1px solid #d9d9d9;"
                  >
                  <span class="similarity-unit" style="color: #333;">%</span>
                </div>
              </div>
              <input 
                type="range" 
                v-model.number="similarity" 
                min="0" 
                max="100" 
                step="1"
                class="slider"
              >
            </div>

            <!-- 关键词搜索开关 -->
            <div class="search-option">
              <input type="checkbox" v-model="enableKeyword" id="keyword-toggle">
              <label for="keyword-toggle" class="option-label" style="color: #666;">启用关键词匹配</label>
            </div>
          </div>

          <!-- 文档选择和搜索按钮 -->
          <div class="doc-search-row">
            <!-- 文档ID筛选 -->
            <div class="document-filter">
              <div class="doc-selector" :class="{ 'active': showDocumentSelector }" @click="showDocumentSelector = !showDocumentSelector" style="background: #f5f5f5; color: #333; border: 1px solid #d9d9d9;">
                <div class="doc-selector-value">
                  <span>{{ selectedDocumentIds.length ? `已选择 ${selectedDocumentIds.length} 个文档` : '选择文档' }}</span>
                  <span class="selector-arrow">▼</span>
                </div>

                <!-- 下拉选择框 -->
                <div v-if="showDocumentSelector" class="doc-dropdown" @click.stop style="background: white; border: 1px solid #e5e5e5; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
                  <div class="dropdown-header" @click.stop style="background: #f5f5f5; border-bottom: 1px solid #e5e5e5;">
                    <div class="dropdown-title" style="color: #333;">选择文档（不选默认搜索全部）</div>
                    <div class="dropdown-search">
                      <input 
                        type="text" 
                        placeholder="搜索文档..." 
                        v-model="docSearchQuery" 
                        @input="filterDocuments"
                        @click.stop
                        class="dropdown-search-input"
                        style="background: white; color: #333; border: 1px solid #d9d9d9;"
                      >
                    </div>
                  </div>
                  
                  <div class="dropdown-content">
                    <div v-if="documentListLoading" class="dropdown-loading" @click.stop style="color: #666;">
                      <span class="loading-icon spinning">🔄</span> 加载中...
                    </div>
                    <div v-else-if="filteredDocumentList.length === 0" class="dropdown-empty" @click.stop style="color: #666;">
                      未找到匹配的文档
                    </div>
                    <div v-else class="dropdown-items" @click.stop>
                      <div 
                        v-for="doc in filteredDocumentList" 
                        :key="doc.id" 
                        class="dropdown-item"
                        :class="{ 'selected': selectedDocumentIds.includes(doc.id) }"
                        @click.stop="toggleDocumentSelection(doc.id)"
                        style="border-bottom: 1px solid #f0f0f0;"
                      >
                        <input 
                          type="checkbox" 
                          :checked="selectedDocumentIds.includes(doc.id)" 
                          @click.stop
                          @change="toggleDocumentSelection(doc.id)"
                        >
                        <div class="item-info">
                          <div class="item-name" :title="doc.name" style="color: #333;">{{ doc.name }}</div>
                          <div class="item-meta">
                            <span class="item-type" style="background: #1890ff; color: white;">{{ doc.type.toUpperCase() }}</span>
                            <span class="item-status" :class="getDocStatusClass(doc.status)">
                              {{ getDocStatusText(doc.status) }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div class="dropdown-footer" @click.stop style="background: #f5f5f5; border-top: 1px solid #e5e5e5;">
                    <button 
                      class="dropdown-btn apply" 
                      @click.stop="handleDocumentSelection"
                      style="background: #1890ff; color: white; border: none;"
                    >
                      确认选择 ({{ selectedDocumentIds.length }})
                    </button>
                    <button 
                      class="dropdown-btn cancel" 
                      @click.stop="showDocumentSelector = false"
                      style="background: white; color: #333; border: 1px solid #d9d9d9;"
                    >
                      取消
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- 已选文档标签 -->
              <div v-if="selectedDocumentIds.length > 0" class="selected-docs">
                <div 
                  v-for="docId in selectedDocumentIds.slice(0, 2)" 
                  :key="docId" 
                  class="doc-tag"
                  style="background: #e6f7ff; color: #1890ff; border: 1px solid #91d5ff;"
                >
                  <span class="tag-text">{{ getDocumentName(docId) }}</span>
                  <span class="remove-tag" @click.stop="removeDocumentSelection(docId)">×</span>
                </div>
                <div v-if="selectedDocumentIds.length > 2" class="doc-tag more" style="background: #f5f5f5; color: #666; border: 1px solid #d9d9d9;">
                  +{{ selectedDocumentIds.length - 2 }} 个文档
                </div>
                <div class="clear-tags" @click.stop="clearDocumentSelection" style="color: #1890ff;">清空</div>
              </div>
            </div>

            <!-- 搜索按钮 -->
            <button @click="performSearch" class="search-btn" :disabled="loading || !searchQuery.trim()" style="background: #1890ff; color: white; border: none;">
              <span class="search-icon" :class="{ 'rotating': loading }">🔍</span>
              {{ loading ? '搜索中...' : '搜索' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索结果区域 -->
    <div class="search-results" v-if="hasSearched" style="background: white; color: #333;">
      <!-- AI回答区域 -->
      <div class="ai-response" v-if="!loading && !error && searchResults.length > 0" :class="{ 'collapsed': aiCollapsed }" style="background: white; border: 1px solid #e5e5e5; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
        <div class="ai-response-header" style="background: #f5f5f5; border-bottom: 1px solid #e5e5e5;">
          <div class="ai-icon">🤖</div>
          <h4 style="color: #333;">AI 智能回答</h4>
          <div class="ai-controls">
            <div class="ai-status-container">
              <div class="ai-status" v-if="aiLoading" style="color: #666;">
                <span class="loading-dots">生成中</span>
                <button @click="stopAiGeneration" class="stop-btn" style="background: #f5222d; color: white; border: none;">停止</button>
              </div>
              <div class="ai-status-placeholder" v-else></div>
            </div>
            <div class="markdown-toggle" v-if="!aiCollapsed">
              <input type="checkbox" id="markdown-toggle" v-model="useMarkdown">
              <label for="markdown-toggle" style="color: #666;">Markdown 显示</label>
            </div>
            <button class="collapse-btn" @click="toggleAiCollapse" style="background: #f5f5f5; color: #666; border: 1px solid #d9d9d9;">
              <span class="collapse-icon">{{ aiCollapsed ? '▼' : '▲' }}</span>
              {{ aiCollapsed ? '展开' : '折叠' }}
            </button>
          </div>
        </div>
        <div class="ai-response-content" v-if="aiResponse && !aiCollapsed" style="background: white; color: #333;">
          <div class="ai-text" v-html="useMarkdown ? markdownResponse : plainTextResponse"></div>
        </div>
        <div class="ai-response-loading" v-else-if="aiLoading && !aiCollapsed" style="background: white; color: #666;">
          <div class="ai-loading-animation">
            <div class="dot"></div>
            <div class="dot"></div>
            <div class="dot"></div>
          </div>
        </div>
        <div class="ai-error" v-else-if="aiError && !aiCollapsed" style="color: #f5222d;">
          {{ aiError }}
          <button @click="generateAiResponse" class="retry-btn" style="background: #1890ff; color: white; border: none;">重试</button>
        </div>
      </div>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-state" style="background: white; color: #666;">
        <div class="loading-spinner"></div>
        <p>正在搜索...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-state is-error" style="background: #fff1f0; color: #f5222d; border: 1px solid #ffa39e;">
        <p class="message">{{ error }}</p>
      </div>

      <!-- 空结果状态 -->
      <div v-else-if="!searchResults.length" class="error-state is-empty" style="background: #f5f5f5; color: #666; border: 1px solid #d9d9d9;">
        <p class="message">未找到相关内容</p>
        <div class="suggestions">
          <div class="suggestion-list" style="color: #666;">
            <p>• 降低相似度阈值（当前：{{ similarity }}%）</p>
            <p>• 尝试使用不同的关键词</p>
            <p>• 确保已上传相关文档</p>
          </div>
          <button @click="performSearch" style="background: #1890ff; color: white; border: none;">重试</button>
        </div>
      </div>

      <!-- 搜索结果列表 -->
      <div v-else class="results-section" style="background: white; color: #333; border: 1px solid #e5e5e5;">
        <h3 class="results-title" style="color: #333; border-bottom: 1px solid #e5e5e5;">搜索结果</h3>
        <div class="results-list">
          <div v-for="(result, index) in searchResults" :key="result.id" class="result-item" style="border-bottom: 1px solid #f0f0f0;">
            <div class="result-header" style="background: #f5f5f5;">
              <div class="result-number" style="color: #1890ff;">#{{ (currentPage - 1) * resultsPerPage + index + 1 }}</div>
              <div class="result-meta">
                <span class="result-score" :class="getScoreClass(result.similarity)" style="background: #e6f7ff; color: #1890ff; border: 1px solid #91d5ff;">
                  相似度: {{ Math.round(result.similarity * 100) }}%
                </span>
                <span class="file-info" style="color: #666;">
                  <span class="file-name">{{ getSearchResultDocName(result) }}</span>
                </span>
              </div>
            </div>

            <div class="result-content" style="background: white;">
              <!-- 修改关键词显示逻辑 -->
              <div class="content-wrapper">
                <span v-if="result.important_keywords" class="chunk-keywords" style="color: #1890ff;">
                  关键词: {{ Array.isArray(result.important_keywords) ? result.important_keywords.join(', ') : (typeof result.important_keywords === 'string' ? result.important_keywords : '') }}
                </span>
                <p class="content-text" v-html="result.highlight || result.content"></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination" style="background: #f5f5f5; border-top: 1px solid #e5e5e5;">
        <button 
          @click="prevPage" 
          :disabled="currentPage <= 1"
          class="page-btn"
          style="background: white; color: #333; border: 1px solid #d9d9d9;"
        >
          上一页
        </button>
        <span class="page-info" style="color: #666;">{{ currentPage }} / {{ totalPages }}</span>
        <button 
          @click="nextPage" 
          :disabled="currentPage >= totalPages"
          class="page-btn"
          style="background: white; color: #333; border: 1px solid #d9d9d9;"
        >
          下一页
        </button>
        <div class="page-size-selector">
          <span style="color: #666;">每页显示:</span>
          <select v-model="resultsPerPage" class="page-size-select" style="background: white; color: #333; border: 1px solid #d9d9d9;">
            <option value="10">10</option>
            <option value="20">20</option>
            <option value="50">50</option>
          </select>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, inject } from 'vue'
import { debounce } from 'lodash-es'
import http from '@/utils/axios'
import { ollamaService } from '@/utils/ollama'
import { v4 as uuidv4 } from 'uuid'
import { marked } from 'marked'
import katex from 'katex'
import 'katex/dist/katex.min.css'

// 配置 marked 以支持 KaTeX 数学公式
const renderer = new marked.Renderer()
const originalCodeRenderer = renderer.code.bind(renderer)

// 自定义代码块渲染器，处理数学公式
renderer.code = (code, language) => {
  if (language === 'math' || language === 'tex') {
    try {
      return katex.renderToString(code, {
        displayMode: true,
        throwOnError: false
      })
    } catch (error) {
      console.error('KaTeX 渲染错误:', error)
      return `<pre>${code}</pre>`
    }
  }
  return originalCodeRenderer(code, language)
}

// 配置行内代码渲染器，处理行内数学公式
const originalInlineCodeRenderer = renderer.codespan.bind(renderer)
renderer.codespan = (code) => {
  if (code.startsWith('$') && code.endsWith('$')) {
    try {
      return katex.renderToString(code.slice(1, -1), {
        displayMode: false,
        throwOnError: false
      })
    } catch (error) {
      console.error('KaTeX 行内公式渲染错误:', error)
      return `<code>${code}</code>`
    }
  }
  return originalInlineCodeRenderer(code)
}

// 配置 marked 选项
marked.setOptions({
  renderer: renderer,
  highlight: function(code, lang) {
    return code
  },
  pedantic: false,
  gfm: true,
  breaks: true,
  sanitize: false,
  smartypants: false,
  xhtml: false
})

// 添加这段代码进一步配置marked输出紧凑HTML
// 自定义紧凑化处理函数 - 清除多余换行和空白
const compactHtml = (html) => {
  if (!html) return '';
  
  // 替换连续多个空行为单个空行，并进行更彻底的处理
  return html
    // 删除多余的换行符
    .replace(/\n\s*\n\s*\n/g, '\n')
    // 删除标签间的多余空白
    .replace(/>\s+</g, '><')
    // 将换行标签后面的空格删除
    .replace(/<br\s*\/?>\s+/g, '<br>')
    // 替换换行符为空格
    .replace(/\n/g, ' ')
    // 删除所有HTML标签中的换行符
    .replace(/(<[^>]+>)\s+/g, '$1')
    // 删除连续的空格
    .replace(/\s{2,}/g, ' ')
    .trim();
};

// 搜索相关的响应式变量
const searchQuery = ref('')
const similarity = ref(30)
const resultsPerPage = ref(10)
const currentPage = ref(1)
const enableKeyword = ref(false)
const documentIds = ref('')

// 添加文档列表相关变量
const documentList = ref([])
const documentListLoading = ref(false)
const selectedDocumentIds = ref([])
const showDocumentSelector = ref(false)
const docSearchQuery = ref('')
const filteredDocumentList = ref([])

// 状态变量
const loading = ref(false)
const loadingMessage = ref('')
const searchProgress = ref(0)
const error = ref('')
const hasSearched = ref(false)
const searchResults = ref([])
const totalResults = ref(0)
const searchId = ref('')

// 添加AI相关的响应式变量
const aiResponse = ref('')
const aiLoading = ref(false)
const aiError = ref('')
const aiController = ref(null)
const useMarkdown = ref(true) // 默认使用Markdown显示
const aiCollapsed = ref(false) // 新增：AI回答折叠状态

// RagFlow配置
const datasetId = ref('4afb1382475c11f0ac3e345a603cb29c')
const apiKey = ref('ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG')

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(totalResults.value / resultsPerPage.value)
})

// 计算属性 - 将 AI 回答转换为 Markdown
const markdownResponse = computed(() => {
  if (!aiResponse.value) return ''
  
  try {
    // 将<br>转换回换行符，以便 marked 正确处理
    const content = aiResponse.value.replace(/<br\s*\/?>/g, '\n')
    
    // 清理内容中的多余空行，保留必要的单行换行
    const cleanContent = content.replace(/\n{3,}/g, '\n\n').trim()
    
    // 使用 marked 渲染 Markdown
    const rendered = marked(cleanContent)
    
    // 简化处理：移除段落之间的多余空行但保留段落结构
    return rendered.replace(/<\/p>\s*<p>/g, '</p><p>')
  } catch (error) {
    console.error('Markdown 渲染错误:', error)
    return aiResponse.value
  }
})

// 计算属性 - 纯文本显示（只处理换行符）
const plainTextResponse = computed(() => {
  if (!aiResponse.value) return ''
  
  // 清理内容中的多余空行，保留必要的单行换行
  const cleanContent = aiResponse.value.replace(/\n{3,}/g, '\n\n').trim()
  
  // 只处理换行符，转换为行内显示
  return cleanContent.replace(/\n/g, ' ')
})

// 执行搜索
const performSearch = async () => {
  if (!searchQuery.value.trim() || loading.value) return
  
  // 重置状态
  loading.value = true
  hasSearched.value = true
  error.value = ''
  searchProgress.value = 0
  loadingMessage.value = '准备搜索...'
  searchResults.value = []
  
  try {
    // 验证并调整相似度阈值
    const minScore = Math.max(0, Math.min(similarity.value / 100, 0.99))
    
    // 构建请求体
    const requestBody = {
      question: searchQuery.value,
      dataset_ids: [datasetId.value],
      page: currentPage.value,
      page_size: resultsPerPage.value,
      similarity_threshold: minScore,
      highlight: true,
      vector_similarity_weight: 0.3,
      top_k: 1024,
      keyword: enableKeyword.value
    }

    // 处理文档ID
    if (selectedDocumentIds.value.length > 0) {
      requestBody.document_ids = selectedDocumentIds.value
    } else if (documentIds.value.trim()) {
      // 兼容手动输入的文档ID
      requestBody.document_ids = documentIds.value.split(',').map(id => id.trim())
    }

    // 添加请求头
    const headers = {
      'Authorization': `Bearer ${apiKey.value}`,
      'Content-Type': 'application/json'
    }

    console.log('发送搜索请求:', requestBody)

    // 发送请求到RagFlow API，修改API路径前缀为/ragflow/api/v1
    const response = await http.post('/ragflow/api/v1/retrieval', requestBody, { headers })

    console.log('完整响应:', response)
    
    // 处理响应数据
    if (response && response.code === 0) {
      const responseData = response.data
      // 处理搜索结果，增强高亮标记
      searchResults.value = (responseData.chunks || []).map(chunk => {
        // 处理高亮内容
        let highlightedContent = chunk.content || ''
        
        // 1. 处理已有的高亮内容
        if (chunk.highlight) {
          // 如果后端返回的高亮内容中已经包含了<em>标签，将其转换为我们的mark标签
          highlightedContent = chunk.highlight.replace(/<em>(.*?)<\/em>/g, '<mark class="search-highlight">$1</mark>')
        } else if (chunk.content) {
          // 2. 处理关键词高亮
          if (enableKeyword.value && chunk.important_keywords) {
            const keywords = Array.isArray(chunk.important_keywords) 
              ? chunk.important_keywords 
              : typeof chunk.important_keywords === 'string'
                ? chunk.important_keywords.split(',')
                : []
            
            keywords.forEach(keyword => {
              if (keyword && keyword.trim()) {
                const escapedKeyword = keyword.trim().replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
                const regex = new RegExp(`(${escapedKeyword})`, 'gi')
                highlightedContent = highlightedContent.replace(regex, '<mark class="keyword-highlight">$1</mark>')
              }
            })
          }
          
          // 3. 处理搜索词高亮
          const searchTerms = searchQuery.value
            .split(/\s+/)
            .filter(term => term.length > 1)
            .map(term => term.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))
          
          if (searchTerms.length > 0) {
            const searchRegex = new RegExp(`(${searchTerms.join('|')})`, 'gi')
            highlightedContent = highlightedContent.replace(searchRegex, '<mark class="search-highlight">$1</mark>')
          }
        }

        // 4. 处理换行符，确保正确显示
        highlightedContent = highlightedContent.replace(/\n/g, '<br>')
        
        return {
          ...chunk,
          highlight: highlightedContent,
          content: chunk.content // 保留原始内容
        }
      })
      totalResults.value = responseData.total || 0
      error.value = ''

      console.log('处理后的搜索结果:', searchResults.value)

      // 搜索成功后生成AI回答
      if (searchResults.value.length > 0) {
        generateAiResponse()
      }
    } else {
      console.log('搜索失败:', response)
      searchResults.value = []
      totalResults.value = 0
      error.value = `搜索失败：${response.message || '服务器返回错误'}`
    }

  } catch (err) {
    console.error('搜索出错:', err)
    console.error('错误详情:', {
      response: err.response,
      message: err.message,
      stack: err.stack
    })
    
    searchResults.value = []
    totalResults.value = 0
    
    // 构建错误消息
    if (err.response?.status === 500) {
      error.value = `搜索失败: ${err.response.data?.message || err.response.data?.detail || '服务器内部错误'}`
    } else if (err.response?.status === 401) {
      error.value = '搜索失败: 认证失败，请检查API密钥'
    } else if (err.message) {
      error.value = `搜索失败: ${err.message}`
    } else {
      error.value = '搜索失败: 未知错误'
    }
  } finally {
    loading.value = false
  }
}

// 分页处理
const changePage = (page) => {
  if (page < 1 || page > totalPages.value) return
  currentPage.value = page
  performSearch()  // 切换页面后重新搜索
}

// 生命周期钩子
onMounted(() => {
  // 获取文档列表
  fetchDocumentList()
  
  // 添加点击外部关闭下拉框的事件监听
  document.addEventListener('click', handleOutsideClick)
})

onUnmounted(() => {
  // 移除事件监听
  document.removeEventListener('click', handleOutsideClick)
})

// 处理点击外部关闭下拉框
const handleOutsideClick = (event) => {
  const dropdown = document.querySelector('.doc-dropdown')
  const selector = document.querySelector('.doc-selector')
  const searchInput = document.querySelector('.dropdown-search-input')
  
  // 如果点击的是搜索输入框，不关闭下拉框
  if (searchInput && (searchInput === event.target || searchInput.contains(event.target))) {
    return
  }
  
  // 如果点击的不是下拉框和选择器，则关闭下拉框
  if (dropdown && selector && !dropdown.contains(event.target) && !selector.contains(event.target)) {
    showDocumentSelector.value = false
  }
}

// 尝试从父组件获取文档列表
const tryGetDocumentsFromParent = () => {
  try {
    // 获取父组件中的文档列表
    const parentElement = document.querySelector('.document-list-view')
    if (parentElement) {
      // 如果找到了文档列表组件，可以通过事件通信获取数据
      const event = new CustomEvent('request-documents', {
        detail: { callback: updateDocumentList },
        bubbles: true
      })
      parentElement.dispatchEvent(event)
      return true
    }
  } catch (err) {
    console.error('从父组件获取文档列表失败:', err)
  }
  return false
}

// 更新文档列表
const updateDocumentList = (docs) => {
  if (Array.isArray(docs) && docs.length > 0) {
    documentList.value = docs.map(doc => ({
      id: doc.id,
      name: doc.file_name || doc.name || '未命名文档',
      type: doc.file_type || doc.type || 'unknown',
      status: doc.process_status || doc.run || 'UNSTART'
    }))
    
    // 初始化过滤后的文档列表
    filteredDocumentList.value = [...documentList.value]
    console.log('从父组件成功获取文档列表:', documentList.value)
  }
}

// 获取文档列表
const fetchDocumentList = async (retryCount = 0) => {
  // 先尝试从父组件获取
  if (tryGetDocumentsFromParent()) {
    return
  }
  
  documentListLoading.value = true
  try {
    const response = await http.get(`/ragflow/api/v1/datasets/${datasetId.value}/documents`, {
      params: {
        page: 1,
        page_size: 100,  // 获取足够多的文档
        orderby: 'create_time',
        desc: true
      },
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey.value}`
      }
    })

    console.log('文档列表响应:', response)

    // 处理不同的响应结构
    let docsData = [];
    
    // 检查响应结构
    if (response && response.code === 0) {
      // 标准响应结构
      if (response.data && response.data.docs) {
        docsData = response.data.docs;
      }
    } else if (Array.isArray(response.data)) {
      // 直接返回数组的情况
      docsData = response.data;
    } else if (response.data && response.data.docs) {
      // 直接在data中包含docs的情况
      docsData = response.data.docs;
    }
    
    if (docsData.length > 0) {
      documentList.value = docsData.map(doc => ({
        id: doc.id,
        name: doc.name || '未命名文档',
        type: doc.type || doc.file_type || 'unknown',
        status: doc.run || doc.process_status || 'UNSTART'
      }))
      
      // 初始化过滤后的文档列表
      filteredDocumentList.value = [...documentList.value]
      console.log('成功获取文档列表:', documentList.value)
    } else {
      console.error('获取文档列表失败: 没有文档数据', response)
      
      // 如果没有数据但API调用成功，显示空列表
      documentList.value = []
      filteredDocumentList.value = []
    }
  } catch (err) {
    console.error('获取文档列表出错:', err)
    console.error('错误详情:', {
      response: err.response,
      message: err.message,
      stack: err.stack
    })
    
    // 重试机制
    if (retryCount < 2) {
      console.log(`重试获取文档列表 (${retryCount + 1}/2)...`)
      setTimeout(() => {
        fetchDocumentList(retryCount + 1)
      }, 1000)
      return
    } else {
      // 最终失败，显示空列表
      documentList.value = []
      filteredDocumentList.value = []
    }
  } finally {
    documentListLoading.value = false
    
    // 如果文档列表为空，尝试使用手动输入的ID
    if (documentList.value.length === 0 && documentIds.value) {
      addManualDocumentId()
    }
  }
}

// 如果文档列表为空，添加手动输入的ID
const addManualDocumentId = () => {
  if (documentIds.value && documentList.value.length === 0) {
    const ids = documentIds.value.split(',').map(id => id.trim()).filter(id => id)
    if (ids.length > 0) {
      documentList.value = ids.map(id => ({
        id: id,
        name: `文档 ${id.substring(0, 8)}...`,
        type: 'unknown',
        status: 'UNKNOWN'
      }))
      filteredDocumentList.value = [...documentList.value]
      selectedDocumentIds.value = [...ids]
    }
  }
}

// 处理文档选择
const handleDocumentSelection = () => {
  documentIds.value = selectedDocumentIds.value.join(',')
  showDocumentSelector.value = false
}

// 添加验证函数
const validateSimilarity = () => {
  // 确保值在0-100之间
  if (similarity.value > 100) similarity.value = 100
  if (similarity.value < 0) similarity.value = 0
  // 确保是整数
  similarity.value = Math.round(similarity.value)
}

// 添加相似度评分样式计算函数
const getScoreClass = (score) => {
  const percentage = score * 100;
  if (percentage >= 90) return 'score-high';
  if (percentage >= 70) return 'score-medium';
  return 'score-low';
}

// 过滤文档列表
const filterDocuments = () => {
  if (!docSearchQuery.value) {
    filteredDocumentList.value = documentList.value
    return
  }
  
  const query = docSearchQuery.value.toLowerCase()
  filteredDocumentList.value = documentList.value.filter(doc => 
    doc.name.toLowerCase().includes(query) || 
    doc.id.toLowerCase().includes(query)
  )
}

// 添加文档选择和移除文档选择的逻辑
const toggleDocumentSelection = (id) => {
  if (selectedDocumentIds.value.includes(id)) {
    selectedDocumentIds.value = selectedDocumentIds.value.filter(i => i !== id)
  } else {
    selectedDocumentIds.value.push(id)
  }
}

const removeDocumentSelection = (id) => {
  selectedDocumentIds.value = selectedDocumentIds.value.filter(i => i !== id)
}

const clearDocumentSelection = () => {
  selectedDocumentIds.value = []
}

// 获取文档名称
const getDocumentName = (id) => {
  const doc = documentList.value.find(d => d.id === id)
  return doc ? doc.name : '未知文档'
}

// 获取文档状态类
const getDocStatusClass = (status) => {
  const statusMap = {
    'UNSTART': 'unstarted',
    'RUNNING': 'running',
    'DONE': 'done',
    'FAIL': 'failed',
    'CANCEL': 'cancelled',
    '0': 'unstarted',
    '1': 'unstarted',
    '2': 'running',
    '3': 'done',
    '4': 'failed',
    '5': 'cancelled'
  }
  return statusMap[status] || 'unknown'
}

// 获取文档状态文本
const getDocStatusText = (status) => {
  const statusMap = {
    'UNSTART': '未开始',
    'RUNNING': '处理中',
    'DONE': '已完成',
    'FAIL': '失败',
    'CANCEL': '已取消',
    '0': '未开始',
    '1': '未开始',
    '2': '处理中',
    '3': '已完成',
    '4': '失败',
    '5': '已取消'
  }
  return statusMap[status] || '未知'
}

// 修改获取搜索结果文档名称的方法
const getSearchResultDocName = (result) => {
  // 首先尝试从文档列表中获取名称
  const doc = documentList.value.find(d => d.id === result.document_id)
  if (doc?.name) {
    return doc.name
  }
  
  // 如果文档列表中没有，则使用搜索结果中的文档名称
  if (result.docnm_kwd) {
    return result.docnm_kwd
  }
  
  // 如果都没有，则返回"未知文档"
  return '未知文档'
}

// 生成AI回答的函数
const generateAiResponse = async () => {
  if (!searchResults.value.length) return
  
  aiLoading.value = true
  aiError.value = ''
  aiResponse.value = ''
  
  // 创建新的AbortController
  aiController.value = new AbortController()
  
  try {
    // 获取相似度排序后的前20条结果
    const top20Results = [...searchResults.value]
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, 20)
    
    // 构建提示词
    const prompt = `请根据以下搜索结果生成一个简洁的总结回答：
    
搜索问题：${searchQuery.value}

相关文档内容：
${top20Results.map((result, index) => `
${index + 1}. ${result.content}`).join('\n')}

请用中文回答，并确保回答简洁、准确、易懂。如果内容中包含代码，请使用 Markdown 格式的代码块（\`\`\`）来包裹代码。
可以适当使用 Markdown 格式来增强回答的可读性，包括：
- 使用 # ## ### 等标题
- 使用 **粗体** 和 *斜体* 强调重要内容
- 使用列表和表格组织信息
- 如果需要展示数学公式，可以使用 \`\`\`math 公式内容 \`\`\` 或行内公式 \`$公式内容$\`
`

    console.log('发送到Ollama的提示词:', prompt)

    // 使用流式响应
    let fullResponse = ''
    await ollamaService.streamChat(
      prompt,
      (message) => {
        fullResponse += message
        // 保存原始响应，不进行HTML转换，由markdownResponse计算属性处理
        aiResponse.value = fullResponse
      },
      (error) => {
        // 如果是用户主动取消，不显示错误
        if (error.name === 'AbortError') {
          console.log('用户停止了AI生成')
          return
        }
        console.error('Ollama调用错误:', error)
        aiError.value = '生成回答时出错：' + (error.message || '未知错误')
      },
      {
        temperature: 0.7, // 控制创造性
        top_p: 0.9,      // 控制输出多样性
        model: 'qwen2.5:7b', // 使用默认模型  qwq:latest  qwen2.5:32b
        signal: aiController.value.signal // 传递AbortController的信号
      }
    )
  } catch (error) {
    // 如果是用户主动取消，不显示错误
    if (error.name === 'AbortError') {
      console.log('用户停止了AI生成')
      return
    }
    console.error('AI生成错误:', error)
    aiError.value = '生成回答时出错，请重试'
  } finally {
    aiLoading.value = false
    aiController.value = null
  }
}

// 停止AI生成的函数
const stopAiGeneration = () => {
  if (aiController.value) {
    aiController.value.abort()
    aiLoading.value = false
    console.log('已停止AI生成')
  }
}

// 切换AI回答折叠状态
const toggleAiCollapse = () => {
  aiCollapsed.value = !aiCollapsed.value
}
</script>

<style scoped>
.search-view {
  padding: 16px;  /* 减小外边距 */
  max-width: 1600px;
  margin: 0 auto;
  width: 95%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.search-header {
  margin-bottom: 12px;  /* 减小底部间距 */
  flex-shrink: 0;
  padding: 0 16px;
}

.search-header h3 {
  margin-bottom: 12px;
  color: var(--primary-light);
  font-size: 1.4em;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.search-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;  /* 减小控件之间的间距 */
  align-items: start;
  background: var(--bg-light);
  padding: 12px;  /* 减小内边距 */
  border-radius: 8px;
  border: 1px solid var(--border);
}

.search-input-group {
  width: 100%;
  display: flex;
  gap: 8px;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--bg-light);
  color: var(--text);
  min-width: 300px;
}

/* 搜索选项行 */
.search-options-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 12px;  /* 减小间距 */
  margin: 4px 0;  /* 添加小的垂直间距 */
}

/* 文档选择和搜索按钮行 */
.doc-search-row {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;  /* 减小间距 */
  margin: 4px 0;  /* 添加小的垂直间距 */
}

/* 相似度滑动条容器 */
.similarity-slider {
  flex: 1; /* 占据更多空间 */
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 200px;
  padding: 10px 0;
}

/* 关键词搜索开关样式 */
.search-option {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap; /* 防止文字换行 */
  margin-left: 16px; /* 与滑动条保持一定距离 */
}

.option-label {
  color: var(--text-secondary);
  font-size: 0.9em;
  cursor: pointer;
}

/* 文档ID筛选样式 */
.document-filter {
  flex: 1;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.doc-selector {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  cursor: pointer;
}

.doc-selector-value {
  flex: 1;
  padding: 10px 14px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--bg-light);
  color: var(--text);
  font-size: 0.95em;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: all 0.2s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.doc-selector-value:hover {
  border-color: var(--primary);
  background: var(--bg);
}

.selector-arrow {
  color: var(--text-secondary);
  font-size: 0.9em;
  margin-left: 8px;
  transition: transform 0.2s ease;
}

.doc-selector.active .selector-arrow {
  transform: rotate(180deg);
}

/* 下拉选择框样式 */
.doc-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  width: 100%;
  max-width: 100%;
  background: var(--bg);
  border-radius: 8px;
  border: 1px solid var(--border);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  overflow: hidden;
}

.dropdown-header {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border);
  background: var(--bg-light);
}

.dropdown-title {
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--text);
  font-size: 1em;
}

.dropdown-search {
  position: relative;
}

.dropdown-search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--bg);
  color: var(--text);
  font-size: 0.9em;
}

.dropdown-search-input:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.2);
}

.dropdown-content {
  padding: 0;
  max-height: 250px;
  overflow-y: auto;
}

.dropdown-loading {
  text-align: center;
  padding: 24px;
  color: var(--text-secondary);
}

.dropdown-empty {
  text-align: center;
  padding: 24px;
  color: var(--text-secondary);
}

.dropdown-items {
  overflow-y: auto;
}

.dropdown-item {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-bottom: 1px solid var(--border);
  cursor: pointer;
  gap: 10px;
  transition: background 0.2s ease;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:hover {
  background: var(--bg-lighter);
}

.dropdown-item.selected {
  background: rgba(var(--primary-rgb), 0.1);
}

.dropdown-item input[type="checkbox"] {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: var(--primary);
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 0;
}

.item-name {
  font-weight: 500;
  color: var(--text);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 0.9em;
}

.item-type {
  padding: 2px 6px;
  border-radius: 4px;
  background: var(--primary);
  color: white;
  font-size: 0.8em;
  font-weight: 500;
}

.item-status {
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: 500;
}

.item-status.running {
  background: rgba(76, 175, 80, 0.2);
  color: #2e7d32;
}

.item-status.unstarted {
  background: rgba(255, 193, 7, 0.2);
  color: #f57c00;
}

.item-status.done {
  background: rgba(33, 150, 243, 0.2);
  color: #1976d2;
}

.item-status.failed {
  background: rgba(244, 67, 54, 0.2);
  color: #d32f2f;
}

.item-status.cancelled {
  background: rgba(158, 158, 158, 0.2);
  color: #616161;
}

.dropdown-footer {
  padding: 12px 16px;
  text-align: right;
  border-top: 1px solid var(--border);
  background: var(--bg-light);
}

.dropdown-btn {
  padding: 8px 16px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: transparent;
  color: var(--text);
  cursor: pointer;
  margin-left: 8px;
  font-size: 0.9em;
  transition: all 0.2s ease;
}

.dropdown-btn.apply {
  background: var(--primary);
  color: white;
  border-color: var(--primary);
}

.dropdown-btn.apply:hover {
  background: var(--primary-light);
  transform: translateY(-1px);
}

.dropdown-btn.cancel:hover {
  background: var(--bg-lighter);
  transform: translateY(-1px);
}

/* 已选文档标签样式 */
.selected-docs {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
}

.doc-tag {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 10px;
  border-radius: 4px;
  background: var(--primary);
  color: white;
  font-size: 0.9em;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.doc-tag:hover {
  border-color: var(--primary);
  background: var(--bg);
}

.doc-tag .tag-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.doc-tag.more {
  background: var(--bg-lighter);
  color: var(--text);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.remove-tag {
  color: var(--error);
  cursor: pointer;
  font-weight: bold;
  margin-left: 4px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.remove-tag:hover {
  background: rgba(244, 67, 54, 0.1);
}

.clear-tags {
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 0.9em;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-tags:hover {
  color: var(--error);
  background: rgba(244, 67, 54, 0.1);
}

/* 响应式设计 */
@media screen and (max-width: 1024px) {
  .search-options-row {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .similarity-slider {
    width: 100%;
  }
  
  .doc-search-row {
    flex-direction: column;
  }
  
  .search-btn {
    width: 100%;
    justify-content: center;
  }
}

@media screen and (max-width: 768px) {
  .search-controls {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(5, auto);
    gap: 12px;
  }
  
  .search-input-group {
    grid-column: 1 / -1;
    grid-row: 1;
  }
  
  .similarity-slider {
    grid-column: 1 / -1;
    grid-row: 2;
    width: 100%;
  }
  
  .search-option {
    grid-column: 1 / -1;
    grid-row: 3;
    justify-self: start;
  }
  
  .document-filter {
    grid-column: 1 / -1;
    grid-row: 4;
  }
  
  .results-per-page {
    grid-column: 1 / 2;
    grid-row: 5;
  }
  
  .search-btn {
    grid-column: 1 / -1;
    grid-row: 6;
    justify-self: stretch;
  }
  
  .search-input {
    min-width: 100%;
  }
}

@media screen and (max-width: 480px) {
  .search-view {
    padding: 8px;
  }
}

.error-state .suggestion-list {
  text-align: left;
  margin: 16px 0;
  padding: 0 20px;
}

.error-state .suggestion-list p {
  margin: 8px 0;
  color: var(--text-secondary);
}

.error-state.is-empty {
  background: var(--bg-lighter);
  border-color: var(--border);
  color: var(--text-secondary);
}

.error-state.is-empty .message {
  color: var(--text);
  font-weight: 500;
}

.error-state.is-error {
  background: var(--error-bg);
  border-color: var(--error);
  color: var(--error);
}

.error-state.is-error .message {
  color: var(--error);
  font-weight: 500;
}

.similarity-slider {
  grid-column: 1 / 2;
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 200px;
}

.similarity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  white-space: nowrap;
}

.similarity-label {
  color: var(--text-secondary);
  font-size: 0.9em;
}

.similarity-input-group {
  display: flex;
  align-items: center;
  gap: 4px;
}

.similarity-input {
  width: 50px;
  padding: 4px 8px;
  border: 1px solid var(--border);
  border-radius: 4px;
  background: var(--bg-light);
  color: var(--text);
  text-align: right;
}

.similarity-unit {
  color: var(--text-secondary);
  font-size: 0.9em;
}

/* 移除number input的上下箭头 */
.similarity-input::-webkit-inner-spin-button,
.similarity-input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

.similarity-input[type=number] {
  -moz-appearance: textfield;
  appearance: textfield;
}

.slider {
  flex: 1;
  min-width: 300px; /* 增加最小宽度 */
  height: 10px;
  background: var(--bg-lighter);
  border-radius: 5px;
  outline: none;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: 1px solid var(--border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: var(--primary);
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: var(--primary);
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.slider::-moz-range-track {
  height: 6px;
  background: var(--bg-lighter);
  border-radius: 3px;
  border: 1px solid var(--border);
}

.slider:focus {
  outline: none;
}

.slider::-ms-track {
  width: 100%;
  cursor: pointer;
  background: transparent;
  border-color: transparent;
  color: transparent;
  height: 6px;
}

.slider::-ms-fill-lower {
  background: var(--primary);
  border-radius: 3px;
}

.slider::-ms-fill-upper {
  background: var(--bg-lighter);
  border-radius: 3px;
}

.results-per-page {
  grid-column: 1 / 2;
  grid-row: 4;
  justify-self: start;
  padding: 8px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--bg-light);
  color: var(--text);
}

.search-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 10px 24px;
  border: none;
  border-radius: 6px;
  background: var(--primary);
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.search-icon {
  display: inline-block;
}

.search-icon.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 搜索结果样式 */
.search-results {
  margin-top: 12px;  /* 减小顶部间距 */
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0;
  margin-bottom: 12px;  /* 减小底部间距 */
}

/* AI回答区域样式 */
.ai-response {
  background: var(--bg-light);
  border-radius: 8px;
  border: 1px solid var(--border);
  margin-bottom: 10px;
  overflow: hidden;
  min-height: 80px;
  max-height: 35vh;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.ai-response.collapsed {
  max-height: 50px;
  min-height: 50px;
}

.ai-response-header {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border-bottom: 1px solid var(--border);
  background: var(--bg);
  min-height: 50px;
}

.ai-icon {
  font-size: 1.3em;
}

.ai-response-header h4 {
  margin: 0;
  color: var(--text);
  font-size: 1em;
}

.ai-controls {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 12px;
}

.ai-status-container {
  min-width: 100px;
  display: flex;
  justify-content: flex-end;
}

.ai-status {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 0.85em;
}

.ai-status-placeholder {
  height: 24px;
}

.markdown-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--text-secondary);
  font-size: 0.85em;
}

.markdown-toggle input[type="checkbox"] {
  cursor: pointer;
  accent-color: var(--primary);
}

.markdown-toggle label {
  cursor: pointer;
}

.loading-dots::after {
  content: '...';
  animation: dots 1.5s steps(4, end) infinite;
}

@keyframes dots {
  0%, 20% { content: ''; }
  40% { content: '.'; }
  60% { content: '..'; }
  80% { content: '...'; }
}

.ai-response-content {
  padding: 4px;
  line-height: 0.9;
  color: var(--text);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--primary) var(--bg);
  flex: 1;
  min-height: 50px;
  max-height: calc(60vh - 60px);
}

/* AI文本样式 */
.ai-text {
  white-space: pre-wrap;
  font-size: 0.85em;
  line-height: 0.9;
  word-break: break-word;
}

/* Markdown渲染样式 */
:deep(.ai-text) {
  /* 基本样式 */
  font-size: 0.85em;
  line-height: 0.9;
  word-break: break-word;
}

/* 标题样式 */
:deep(.ai-text h1), 
:deep(.ai-text h2), 
:deep(.ai-text h3), 
:deep(.ai-text h4), 
:deep(.ai-text h5), 
:deep(.ai-text h6) {
  margin: 0.2em 0 0.05em 0;
  color: var(--text);
  font-weight: 600;
  line-height: 0.9;
  display: block;
}

:deep(.ai-text h1) {
  font-size: 1.2em;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.1em;
}

:deep(.ai-text h2) {
  font-size: 1.1em;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.1em;
}

:deep(.ai-text h3) { font-size: 1.0em; }
:deep(.ai-text h4) { font-size: 0.95em; }

/* 段落和换行 */
:deep(.ai-text p) {
  margin: 0.2em 0;
  line-height: 1.0;
  display: inline-block;
  width: 100%;
}

/* 处理换行和换行标签 */
:deep(.ai-text br) {
  content: "";
  display: inline;
  margin: 0;
  line-height: 1;
}

/* 列表样式 */
:deep(.ai-text ul), 
:deep(.ai-text ol) {
  padding-left: 1em;
  margin: 0.1em 0;
  display: block;
}

:deep(.ai-text li) {
  margin: 0.02em 0;
  line-height: 0.9;
  display: block;
}

/* 代码块样式 */
:deep(.ai-text pre) {
  background: var(--bg-light);
  border-radius: 3px;
  padding: 0.2em;
  margin: 0.2em 0;
  overflow-x: auto;
  border: 1px solid var(--border);
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.8em;
  line-height: 0.9;
  display: block;
}

:deep(.ai-text code) {
  background: var(--bg-light);
  padding: 0.03em 0.1em;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 0.8em;
  color: var(--primary);
  border: 1px solid var(--border);
}

/* 引用块样式 */
:deep(.ai-text blockquote) {
  border-left: 2px solid var(--primary);
  padding: 0.05em 0.2em;
  margin: 0.1em 0;
  background: var(--bg-light);
  color: var(--text-secondary);
  line-height: 0.9;
  display: block;
}

/* 表格样式 */
:deep(.ai-text table) {
  border-collapse: collapse;
  margin: 0.1em 0;
  width: 100%;
  display: block;
  font-size: 0.85em;
}

:deep(.ai-text th),
:deep(.ai-text td) {
  border: 1px solid var(--border);
  padding: 0.05em 0.1em;
  line-height: 0.9;
}

/* 代码块标题 */
:deep(.ai-text pre[data-lang]::before) {
  content: attr(data-lang);
  display: block;
  background: var(--bg);
  padding: 1px 2px;
  border-bottom: 1px solid var(--border);
  font-size: 0.65em;
  color: var(--text-secondary);
  font-weight: 500;
  border-radius: 3px 3px 0 0;
  margin: -0.2em -0.2em 0.1em -0.2em;
}

/* 高亮样式优化 */
:deep(.content-text mark.keyword-highlight) {
  background: rgba(76, 175, 80, 0.15);
  color: #4caf50;
  padding: 2px 4px;
  border-radius: 2px;
  font-weight: 500;
  box-shadow: none;
  margin: 0 2px;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

:deep(.content-text mark.search-highlight) {
  background: rgba(33, 150, 243, 0.15);
  color: #42a5f5;
  padding: 2px 4px;
  border-radius: 2px;
  font-weight: 500;
  box-shadow: none;
  margin: 0 2px;
  border: 1px solid rgba(33, 150, 243, 0.3);
}

:deep(.content-text) {
  line-height: 1.8;
  color: rgba(0, 0, 0, 0.85);
}

/* 分页样式 */
.pagination {
  margin-top: auto;
  padding: 12px 0;  /* 减小内边距 */
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
  border-top: 1px solid var(--border);
  flex-shrink: 0;
  background: var(--bg-light);
  position: sticky;
  bottom: 0;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--bg-light);
  color: var(--text);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9em;
}

.page-btn:hover:not(:disabled) {
  background: var(--bg-lighter);
  border-color: var(--primary);
  color: var(--primary);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: var(--text-secondary);
  font-size: 0.9em;
}

.loading-icon {
  display: inline-block;
}

.loading-icon.spinning {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 添加每页条数选择器样式 */
.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-select {
  padding: 6px 10px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--bg-light);
  color: var(--text);
  cursor: pointer;
  min-width: 60px;
  text-align: center;
}

.page-size-select:hover {
  background: var(--bg-lighter);
}

.collapse-btn {
  padding: 4px 10px;
  border: 1px solid var(--border);
  border-radius: 4px;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.collapse-btn:hover {
  background: var(--bg-lighter);
  color: var(--text);
}

.collapse-icon {
  font-size: 0.8em;
}

.ai-response-loading {
  padding: 20px;
  text-align: center;
  background: var(--bg);
}

.ai-loading-animation {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.dot {
  width: 8px;
  height: 8px;
  background: var(--primary);
  border-radius: 50%;
  animation: bounce 1s infinite ease-in-out;
}

.dot:nth-child(2) { animation-delay: 0.2s; }
.dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

/* AI 错误状态 */
.ai-error {
  padding: 20px;
  color: var(--error);
  text-align: center;
  background: var(--bg);
}

.retry-btn {
  margin-top: 10px;
  padding: 6px 12px;
  border: 1px solid var(--primary);
  border-radius: 4px;
  background: transparent;
  color: var(--primary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-btn:hover {
  background: var(--primary);
  color: white;
}

.stop-btn {
  padding: 4px 8px;
  border: 1px solid var(--error);
  border-radius: 4px;
  background: transparent;
  color: var(--error);
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.2s ease;
}

.stop-btn:hover {
  background: var(--error);
  color: white;
}

/* 自定义滚动条样式 */
.ai-response-content::-webkit-scrollbar {
  width: 8px;
}

.ai-response-content::-webkit-scrollbar-track {
  background: var(--bg);
  border-radius: 4px;
}

.ai-response-content::-webkit-scrollbar-thumb {
  background-color: var(--primary);
  border-radius: 4px;
  border: 2px solid var(--bg);
}

/* 搜索结果区域样式 */
.results-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  overflow: hidden;
  margin-bottom: 10px;
}

.results-title {
  margin: 0 0 10px 0;
  padding: 10px 0;
  color: var(--primary-light);
  font-size: 1.2em;
  font-weight: 600;
  letter-spacing: 0.5px;
  border-bottom: 1px solid var(--border);
}

/* 结果列表样式 */
.results-list {
  flex: 1;
  overflow-y: auto;
  padding: 0 12px 12px 12px;  /* 减小内边距 */
  margin-right: -12px;
  min-height: 150px;  /* 减小最小高度 */
  max-height: calc(60vh - 100px);  /* 增加最大高度以显示更多结果 */
}

.result-item {
  position: relative;
  padding: 12px;  /* 减小内边距 */
  padding-left: 36px;  /* 减小左侧编号的空间 */
  background: var(--bg-light);
  border-radius: 8px;
  border: 1px solid var(--border);
  margin-bottom: 12px;  /* 减小底部间距 */
  margin-left: 16px;  /* 减小左侧间距 */
  transition: all 0.2s ease;
}

.result-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-number {
  position: absolute;
  left: -20px;  /* 调整位置 */
  top: 12px;
  min-width: 24px;  /* 减小大小 */
  height: 24px;
  border-radius: 12px;
  font-size: 0.85em;
  background: white;  /* 改为白色背景 */
  color: var(--primary);  /* 改为主题色(蓝色)文字 */
  display: flex;  /* 使用flex布局居中 */
  align-items: center;
  justify-content: center;
  font-weight: 500;  /* 加粗字体 */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);  /* 减轻阴影效果 */
  border: 1px solid var(--primary);  /* 添加蓝色边框 */
}

.result-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12px;
}

.result-meta {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-secondary);
  font-size: 0.9em;
}

.file-name {
  font-weight: 500;
  color: var(--text);
}

.file-date {
  color: var(--text-secondary);
}

.result-score {
  font-weight: bold;
  padding: 6px 12px;
  border-radius: 20px;
}

.result-score.score-high {
  background: rgba(76, 175, 80, 0.2);
  color: #2e7d32;
  border: 1px solid #2e7d32;
}

.result-score.score-medium {
  background: rgba(255, 193, 7, 0.2);
  color: #f57c00;
  border: 1px solid #f57c00;
}

.result-score.score-low {
  background: rgba(244, 67, 54, 0.2);
  color: #d32f2f;
  border: 1px solid #d32f2f;
}

.result-content {
  color: var(--text);
  padding: 16px;
  background: var(--bg);
  border-radius: 6px;
  margin-top: 12px;
}

.chunk-keywords {
  padding: 4px 8px;
  background: rgba(var(--primary-rgb), 0.1);
  border-radius: 4px;
  color: var(--primary);
  font-size: 0.9em;
  margin-right: 10px;
  display: inline-block;
}

.content-wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 8px;
}

.content-text {
  margin: 0;
  line-height: 1.6;
  font-size: 1rem;
  white-space: pre-wrap;
  word-break: break-word;
}

:deep(.ai-text th) {
  background: var(--bg-lighter);
  font-weight: 600;
}

.search-box {
  width: 100%;
}

.search-main {
  width: 100%;
}

.search-input-row {
  width: 100%;
  display: flex;
}

.search-input {
  width: 100%;
  flex: 1;
  padding: 12px 16px;
  border: 1px solid var(--border);
  border-radius: 8px;
  background: var(--bg-light);
  color: var(--text);
  font-size: 1em;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 2px rgba(var(--primary-rgb), 0.1);
}
</style> 