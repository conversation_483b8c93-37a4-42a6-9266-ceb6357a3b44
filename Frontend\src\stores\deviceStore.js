import { defineStore } from 'pinia'
import mqttClient from '../utils/mqtt'

export const useDeviceStore = defineStore('device', {
  state: () => ({
    selectedDevice: null,
    deviceData: null,
    connectionStatus: 'disconnected'
  }),

  actions: {
    // 获取默认设备数据
    getDefaultDeviceData(wellname = '--', displayName = '--') {
      return {
        WELL_COMMON_NAME: wellname,
        display_name: displayName,
        status: 'abnormal',
        Check_date: '--',
        oilPressure: '--',
        casingPressure: '--',
        wellheadTemp: '--',
        strokeLength: '--',
        strokeRate: '--',
        activePower: '--',
        reactivePower: '--',
        powerFactor: '--',
        totalPower: '--',
        phaseA: { voltage: '--', current: '--' },
        phaseB: { voltage: '--', current: '--' },
        phaseC: { voltage: '--', current: '--' },
        disp_load: '',
        disp_current: '',
        displacement: []
      }
    },

    selectDevice(device) {
      console.log(`[设备选择] 选择设备: ${device ? device.wellname || device.display_name : 'null'}`);
      this.selectedDevice = device
      
      // 断开之前的连接
      console.log(`[MQTT] 取消之前的所有订阅`);
      mqttClient.unsubscribeAll()
      
      if (device && device.topic) {
        // 先设置默认数据
        console.log(`[设备数据] 设置设备${device.wellname || device.display_name}的默认数据`);
        this.deviceData = this.getDefaultDeviceData(device.wellname, device.display_name)
        // 订阅新设备
        console.log(`[MQTT] 订阅设备主题: ${device.topic}`);
        mqttClient.subscribe(device.topic)
      } else {
        console.log(`[设备数据] 清空设备数据`);
        this.deviceData = null
      }
    },

    updateDeviceData(data) {
      // 如果没有数据，使用默认值
      if (!data) {
        console.log(`[数据更新] 无数据，使用默认值`);
        this.deviceData = this.getDefaultDeviceData(this.selectedDevice?.wellname, this.selectedDevice?.display_name)
        return
      }

      console.log(`[数据更新] 收到设备数据更新: ${data.Wellname || 'unknown'}`);
      
      // 数据清洗处理
      console.log(`[数据清洗] 开始清洗设备数据`);
      const cleanData = this.cleanData(data)
      
      // 从Values数组中提取关键指标
      const wellheadTemp = this.findValue(data.Values, 'TWT');
      const oilPressure = this.findValue(data.Values, 'WIP');
      console.log(`[关键指标] 井口温度: ${wellheadTemp}, 井口压力: ${oilPressure}`);
      
      // 保存当前的display_name值，确保它不会被覆盖
      const currentDisplayName = this.selectedDevice?.display_name || this.deviceData?.display_name || '--';
      console.log(`[设备显示名称] 使用名称: ${currentDisplayName}, 设备ID: ${data.Wellname || '--'}`);
      
      // 转换数据以匹配DevicePanel期望的格式
      console.log(`[数据转换] 转换数据格式`);
      this.deviceData = {
        WELL_COMMON_NAME: data.Wellname || '--',
        display_name: currentDisplayName, // 使用之前保存的display_name
        status: data.Status === 0 ? 'normal' : 'abnormal',
        Check_date: data.Check_date || '--',
        oilPressure: this.findValue(data.Values, 'WIP') || '--',      // 井口压力
        casingPressure: this.findValue(data.Values, 'CPV') || '--',   // 套压
        wellheadTemp: this.findValue(data.Values, 'TWT') || '--',     // 井口温度
        strokeLength: this.findValue(data.Values, 'SLV') || '--',     // 冲程
        strokeRate: this.findValue(data.Values, 'CHC') || '--',       // 冲次
        activePower: this.findValue(data.Values, 'ZYG') || '--',      // 总有功功率
        reactivePower: this.findValue(data.Values, 'ZWG') || '--',    // 总无功功率
        powerFactor: this.findValue(data.Values, 'GYS') || '--',      // 功率因数
        totalPower: this.findValue(data.Values, 'YGL') || '--',       // 总功耗
        
        // 电气参数
        phaseA: {
          voltage: this.findValue(data.Values, 'ADY') || '--',
          current: this.findValue(data.Values, 'ADL') || '--'
        },
        phaseB: {
          voltage: this.findValue(data.Values, 'BDY') || '--',
          current: this.findValue(data.Values, 'BDL') || '--'
        },
        phaseC: {
          voltage: this.findValue(data.Values, 'CDY') || '--',
          current: this.findValue(data.Values, 'CDL') || '--'
        },

        // 功图数据
        disp_load: this.formatGraphData(data.Disp_load, data.Displacement),
        disp_current: this.formatGraphData(data.Disp_current, data.Displacement),
        displacement: data.Displacement ? data.Displacement.split('|').map(Number) : []
      }
      
      console.log(`[数据更新完成] 设备 ${this.deviceData.display_name || this.deviceData.WELL_COMMON_NAME} 数据已更新`);
    },

    // 辅助方法：在Values数组中查找指定id的值
    findValue(values, id) {
      if (!Array.isArray(values)) return 0
      const item = values.find(v => v.id === id)
      return item ? item.Value : 0
    },

    // 数据清洗
    cleanData(data) {
      if (!data) return {}
      
      // 确保Values数组存在
      data.Values = data.Values || []
      
      // 确保基础字段存在
      const defaultData = {
        Station: 'Unknown',
        Wellname: 'Unknown',
        Status: 1,
        Check_date: new Date().toISOString().replace('T', ' ').split('.')[0],
        Dyna_points: 200,
        Disp_load: '',
        Disp_current: '',
        Displacement: '',
        Values: []
      }
      
      return {
        ...defaultData,
        ...data,
        // 确保Values是数组
        Values: Array.isArray(data.Values) ? data.Values : []
      }
    },

    // 格式化功图数据
    formatGraphData(yValues, xValues) {
      if (!yValues || !xValues) return ''
      
      const yArray = yValues.split('|').map(Number)
      const xArray = xValues.split('|').map(Number)
      
      if (yArray.length !== xArray.length) return ''
      
      // 组合x和y值成为功图数据点
      return yArray.map((y, index) => `${xArray[index]},${y}`).join('|')
    },

    setConnectionStatus(status) {
      const prevStatus = this.connectionStatus;
      this.connectionStatus = status
      console.log(`[连接状态] MQTT连接状态变更: ${prevStatus} -> ${status}`);
      
      // 当连接断开或出错时，使用默认数据
      if (status === 'disconnected' || status === 'error') {
        if (this.selectedDevice) {
          console.log(`[连接异常] 连接${status}，设置设备${this.selectedDevice.wellname}默认数据`);
          this.deviceData = this.getDefaultDeviceData(this.selectedDevice.wellname, this.selectedDevice.display_name)
        }
      }
    }
  }
})