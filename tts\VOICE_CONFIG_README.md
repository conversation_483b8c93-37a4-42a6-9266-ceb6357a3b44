# TTS固定音色配置说明

## 概述

本TTS服务已升级支持固定音色配置和文本预处理功能，确保每次生成的语音都使用一致的音色，并能正确处理Markdown格式的文本。

## 主要功能

### 1. 固定音色配置

- **默认音色**: `serious_male` - 严肃、智慧的男性声音
- **音色种子**: 42 (固定值，确保音色一致性)
- **音频种子**: 123 (固定值，确保生成一致性)
- **参数优化**: 降低随机性，提高朗读准确性

### 2. 文本预处理功能

自动处理以下Markdown格式：
- 标题标记 (`#`, `##`, `###` 等)
- 粗体和斜体 (`**粗体**`, `*斜体*`)
- 链接 (`[文本](URL)`)
- 代码块和行内代码
- 列表标记 (`-`, `*`, `1.`)
- 引用标记 (`>`)
- HTML标签

### 3. 朗读优化

- 英文单词前后添加适当停顿
- 数字正确发音处理
- 句子间添加停顿标记
- 标点符号停顿优化

## API接口

### 1. 基本TTS接口

```bash
POST /tts
```

请求参数：
```json
{
    "text": "要转换的文本",
    "voice_profile": "serious_male",  // 可选，音色配置名称
    "preprocess_text": true,         // 可选，是否预处理文本
    "voice_seed": 42,                // 可选，音色种子
    "audio_seed": 123,               // 可选，音频种子
    "temperature": 0.1,              // 可选，随机性控制
    "top_p": 0.5,                    // 可选，采样范围
    "top_k": 10                      // 可选，候选词数量
}
```

### 2. 流式TTS接口

```bash
POST /tts/stream
```

直接返回WAV音频文件流。

### 3. 文本预处理预览

```bash
POST /preprocess
```

请求参数：
```json
{
    "text": "要预处理的文本"
}
```

### 4. 音色配置列表

```bash
GET /voices
```

返回所有可用的音色配置。

### 5. 服务状态检查

```bash
GET /health
```

返回服务状态和配置信息。

## 可用音色配置

1. **serious_male** (默认)
   - 严肃、智慧的男性声音
   - 适合正式文档朗读

2. **gentle_female**
   - 温和的女性声音
   - 适合温馨内容朗读

3. **professional_male**
   - 专业播音员男性声音
   - 适合新闻、公告朗读

4. **calm_narrator**
   - 平静的叙述者声音
   - 适合故事、说明朗读

## 使用示例

### Python客户端示例

```python
import requests
import base64

# 基本TTS请求
response = requests.post("http://localhost:8003/tts", json={
    "text": "# 标题\n\n这是**重要**的内容，包含English words和数字123。",
    "voice_profile": "serious_male",
    "preprocess_text": True
})

if response.status_code == 200:
    result = response.json()
    if result["success"]:
        # 保存音频
        audio_data = base64.b64decode(result["audio_base64"])
        with open("output.wav", "wb") as f:
            f.write(audio_data)
        print(f"音频时长: {result['duration']:.2f}秒")
```

### 文本预处理示例

```python
# 预览文本处理结果
response = requests.post("http://localhost:8003/preprocess", json={
    "text": "# 标题\n\n这是**粗体**文本和[链接](url)。"
})

result = response.json()
print("原始文本:", result["original_text"])
print("处理后:", result["processed_text"])
```

## 启动服务

### 使用批处理文件（推荐）

```bash
start_tts_with_gpu.bat
```

### 手动启动

```bash
# 激活虚拟环境
.\venv\Scripts\activate

# 启动服务
python tts_service.py
```

## 测试功能

运行测试脚本验证所有功能：

```bash
python test_fixed_voice.py
```

测试内容包括：
- 服务状态检查
- 音色配置功能
- 文本预处理
- 固定音色TTS
- 不同音色对比
- 流式TTS

## 配置文件

音色配置在 `voice_config.py` 中定义，可以：
- 添加新的音色配置
- 修改现有配置参数
- 设置默认音色

## 注意事项

1. **GPU加速**: 确保安装了支持CUDA的PyTorch版本
2. **音色一致性**: 使用相同的voice_seed确保音色固定
3. **文本处理**: 启用preprocess_text获得最佳朗读效果
4. **参数调优**: 可根据需要调整temperature、top_p、top_k参数

## 故障排除

1. **CUDA不可用**: 检查PyTorch CUDA版本安装
2. **音色不一致**: 确保使用固定的voice_seed
3. **朗读效果差**: 启用文本预处理功能
4. **服务启动失败**: 检查虚拟环境和依赖安装
