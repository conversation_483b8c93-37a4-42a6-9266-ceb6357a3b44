#!/bin/bash

echo "正在启动Python RTSP代理服务..."

# 检查Python安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未安装Python3"
    echo "请安装Python 3.7或更高版本"
    exit 1
fi

# 检查依赖
echo "检查并安装依赖..."
echo "卸载可能存在冲突的依赖..."
python3 -m pip uninstall -y numpy opencv-python
echo "安装特定版本依赖..."
python3 -m pip install -r requirements.txt

# 启动代理服务
echo "启动RTSP代理服务器..."
python3 rtsp_proxy.py --host 0.0.0.0 --port 8001 