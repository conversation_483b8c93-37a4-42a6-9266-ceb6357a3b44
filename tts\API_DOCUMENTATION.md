# ChatTTS API 文档

## 概述

ChatTTS 文本转语音服务提供了基于 ChatTTS 模型的中文文本转语音功能。服务通过 RESTful API 接口提供服务，支持实时语音合成和音频流输出。

## 服务信息

- **服务地址**: `http://localhost:8001`
- **API版本**: v1.0.0
- **支持格式**: WAV (24kHz, 16-bit)
- **支持语言**: 中文

## API 接口

### 1. 服务状态检查

#### GET /
获取服务基本信息

**响应示例**:
```json
{
  "service": "ChatTTS 文本转语音服务",
  "version": "1.0.0",
  "status": "running",
  "device": "cpu",
  "model_info": {
    "initialized": true,
    "device": "cpu",
    "sample_rate": 24000,
    "model_loaded": true
  }
}
```

#### GET /health
健康检查接口

**响应示例**:
```json
{
  "status": "healthy",
  "model_loaded": true,
  "device": "cpu",
  "initialized": true
}
```

### 2. 文本转语音

#### POST /tts
将文本转换为语音，返回 base64 编码的音频数据

**请求参数**:
```json
{
  "text": "你好，这是一个测试语音。",
  "voice_seed": 42,
  "temperature": 0.3,
  "top_p": 0.7,
  "top_k": 20,
  "audio_seed": null,
  "text_seed": null,
  "refine_text": true
}
```

**参数说明**:
- `text` (string, 必需): 要转换的文本
- `voice_seed` (int, 可选): 声音种子，控制音色 (默认: null)
- `temperature` (float, 可选): 生成温度，控制随机性 (默认: 0.3)
- `top_p` (float, 可选): 核采样参数 (默认: 0.7)
- `top_k` (int, 可选): Top-K 采样参数 (默认: 20)
- `audio_seed` (int, 可选): 音频生成种子 (默认: null)
- `text_seed` (int, 可选): 文本处理种子 (默认: null)
- `refine_text` (bool, 可选): 是否优化文本 (默认: true)

**响应示例**:
```json
{
  "success": true,
  "message": "音频生成成功",
  "audio_base64": "UklGRiQAAABXQVZFZm10IBAAAAABAAEA...",
  "duration": 2.5,
  "sample_rate": 24000
}
```

**响应字段说明**:
- `success` (bool): 请求是否成功
- `message` (string): 响应消息
- `audio_base64` (string): base64 编码的音频数据
- `duration` (float): 音频时长（秒）
- `sample_rate` (int): 采样率

#### POST /tts/stream
将文本转换为语音，直接返回音频文件流

**请求参数**: 与 `/tts` 接口相同

**响应**: 直接返回 WAV 格式的音频文件流

**响应头**:
- `Content-Type`: `audio/wav`
- `Content-Disposition`: `attachment; filename=tts_output.wav`

## 使用示例

### Python 示例

```python
import requests
import base64

# 基本TTS请求
def text_to_speech(text, voice_seed=42):
    url = "http://localhost:8001/tts"
    data = {
        "text": text,
        "voice_seed": voice_seed,
        "temperature": 0.3,
        "top_p": 0.7,
        "top_k": 20,
        "refine_text": True
    }
    
    response = requests.post(url, json=data)
    
    if response.status_code == 200:
        result = response.json()
        if result["success"]:
            # 解码音频数据
            audio_bytes = base64.b64decode(result["audio_base64"])
            
            # 保存音频文件
            with open("output.wav", "wb") as f:
                f.write(audio_bytes)
            
            print(f"音频生成成功，时长: {result['duration']:.2f}秒")
        else:
            print(f"生成失败: {result['message']}")
    else:
        print(f"请求失败: {response.status_code}")

# 流式TTS请求
def text_to_speech_stream(text, output_file="output_stream.wav"):
    url = "http://localhost:8001/tts/stream"
    data = {
        "text": text,
        "voice_seed": 42,
        "temperature": 0.3,
        "refine_text": True
    }
    
    response = requests.post(url, json=data)
    
    if response.status_code == 200:
        with open(output_file, "wb") as f:
            f.write(response.content)
        print(f"流式音频保存到: {output_file}")
    else:
        print(f"请求失败: {response.status_code}")

# 使用示例
text_to_speech("你好，欢迎使用ChatTTS语音合成服务！")
text_to_speech_stream("这是一个流式音频测试。")
```

### JavaScript 示例

```javascript
// 基本TTS请求
async function textToSpeech(text, voiceSeed = 42) {
    const url = 'http://localhost:8001/tts';
    const data = {
        text: text,
        voice_seed: voiceSeed,
        temperature: 0.3,
        top_p: 0.7,
        top_k: 20,
        refine_text: true
    };
    
    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            const result = await response.json();
            if (result.success) {
                // 创建音频播放
                const audioData = 'data:audio/wav;base64,' + result.audio_base64;
                const audio = new Audio(audioData);
                audio.play();
                
                console.log(`音频生成成功，时长: ${result.duration.toFixed(2)}秒`);
            } else {
                console.error('生成失败:', result.message);
            }
        } else {
            console.error('请求失败:', response.status);
        }
    } catch (error) {
        console.error('请求异常:', error);
    }
}

// 使用示例
textToSpeech('你好，这是JavaScript调用的语音合成！');
```

### cURL 示例

```bash
# 基本TTS请求
curl -X POST "http://localhost:8001/tts" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "你好，这是cURL测试语音。",
       "voice_seed": 42,
       "temperature": 0.3,
       "refine_text": true
     }'

# 流式TTS请求
curl -X POST "http://localhost:8001/tts/stream" \
     -H "Content-Type: application/json" \
     -d '{
       "text": "这是流式音频测试。",
       "voice_seed": 42
     }' \
     --output "output.wav"

# 健康检查
curl -X GET "http://localhost:8001/health"
```

## 错误处理

### 常见错误码

- `400 Bad Request`: 请求参数错误
- `503 Service Unavailable`: TTS模型未初始化
- `500 Internal Server Error`: 服务器内部错误

### 错误响应示例

```json
{
  "detail": "TTS处理失败: 文本不能为空"
}
```

## 性能说明

- **初始化时间**: 首次启动需要加载模型，约需要 10-30 秒
- **处理速度**: 通常每秒可处理 10-20 个字符
- **内存使用**: 模型加载后约占用 2-4GB 内存
- **并发支持**: 支持多个并发请求，但受硬件性能限制

## 注意事项

1. 服务首次启动时需要下载模型文件，请确保网络连接正常
2. 建议在生产环境中使用 GPU 以获得更好的性能
3. 长文本建议分段处理以获得更好的效果
4. 相同的 `voice_seed` 会产生相同的音色特征
