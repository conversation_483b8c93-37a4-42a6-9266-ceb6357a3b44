from fastapi import APIRouter, WebSocket, WebSocketDisconnect, HTTPException, status, Query, Depends, Body
from fastapi.responses import JSONResponse
from fastapi.websockets import WebSocketState
from loguru import logger
from ..websockets.manager import manager
import traceback
import asyncio
from datetime import datetime
import json
import uuid
import time
import random

# 导入alert_generator
from ..utils.alert_generator import alert_generator

router = APIRouter()  # 移除前缀，将在main.py中添加

# 设置是否输出详细日志的标志
verbose_logging = True

def set_verbose_logging(verbose: bool):
    """设置WebSocket路由是否输出详细日志"""
    global verbose_logging
    verbose_logging = verbose
    # 同时设置管理器的日志级别
    manager.set_verbose_logging(verbose)

# 默认返回True的依赖函数，用于授权WebSocket连接
async def get_token(websocket: WebSocket):
    """始终授权WebSocket连接，无需验证"""
    return True

# 允许跨域连接的中间件函数
async def websocket_cors(websocket: WebSocket):
    """WebSocket的CORS中间件，允许跨域连接"""
    origin = websocket.headers.get("origin", "unknown")
    client_ip = websocket.client.host
    if verbose_logging:
        logger.info(f"收到WebSocket连接请求，来源: {origin}，IP: {client_ip}")
    return True  # 允许所有连接

# 使用更简单的路径，避免路径解析错误
@router.websocket("/alerts")
async def websocket_endpoint(websocket: WebSocket):
    """
    WebSocket端点，用于处理实时警报
    """
    connection_id = str(uuid.uuid4())
    logger.info(f"[WebSocket请求] 新的警报WebSocket连接请求，ID: {connection_id}")
    
    try:
        # 接受WebSocket连接
        await websocket.accept()
        logger.info(f"[连接接受] WebSocket连接已接受，ID: {connection_id}")
        
        # 将连接添加到管理器
        await manager.connect(websocket)
        logger.info(f"[连接管理] WebSocket连接已添加到管理器，ID: {connection_id}")
        
        # 发送连接成功消息
        await websocket.send_json({
            "type": "connection_status",
            "status": "connected",
            "message": "WebSocket连接已建立"
        })
        logger.info(f"[状态通知] 已发送连接成功消息，ID: {connection_id}")
        
        try:
            # 消息处理循环
            while True:
                try:
                    # 接收消息，设置超时时间为30秒
                    # logger.debug(f"[等待消息] 等待客户端 {connection_id} 发送消息")
                    data = await asyncio.wait_for(websocket.receive_json(), timeout=30.0)
                    logger.info(f"[收到消息] 客户端 {connection_id} 发送消息，类型: {data.get('type', 'unknown')}")
                    
                    # 处理不同类型的消息
                    if data.get("type") == "device_data":
                        device_id = data.get("device_id")
                        device_data = data.get("data")
                        
                        logger.info(f"[设备数据] 收到设备 {device_id} 的数据更新")
                        
                        if not device_id or not device_data:
                            logger.warning(f"[数据无效] 客户端 {connection_id} 发送的设备数据缺少必要字段")
                            await websocket.send_json({
                                "type": "error",
                                "message": "缺少必要的设备数据字段"
                            })
                            continue
                        
                        # 处理设备数据并生成警报
                        logger.info(f"[处理数据] 开始处理设备 {device_id} 的数据")
                        
                        # 使用alert_generator处理设备数据
                        try:
                            # 更新设备数据并检查是否生成警报
                            alerts = alert_generator.update_device_data(device_id, device_data)
                            if alerts is None:  # 确保返回值为列表
                                alerts = []
                                
                            logger.info(f"[生成警报] 处理设备 {device_id} 数据，生成 {len(alerts)} 个警报")
                            
                            # 广播警报
                            if alerts and len(alerts) > 0:
                                logger.info(f"[广播警报] 开始广播来自设备 {device_id} 的 {len(alerts)} 个警报")
                                await manager.broadcast_alerts(alerts)
                                logger.info(f"[广播完成] 已完成警报广播")
                            else:
                                logger.info(f"[无警报] 设备 {device_id} 数据正常，无需广播警报")
                        except Exception as e:
                            logger.error(f"[数据处理错误] 处理设备 {device_id} 数据时出错: {str(e)}")
                            await websocket.send_json({
                                "type": "error",
                                "message": f"处理设备数据时出错: {str(e)}"
                            })
                            continue
                        
                        # 发送确认消息
                        await websocket.send_json({
                            "type": "device_data_processed",
                            "device_id": device_id,
                            "alerts_count": len(alerts) if alerts else 0
                        })
                        logger.info(f"[处理确认] 已向客户端 {connection_id} 发送处理确认消息")
                    
                    elif data.get("type") == "test_message":
                        # 处理前端发送的测试消息
                        device_id = data.get("deviceId") or f"JH{random.randint(1, 8):03d}"
                        logger.info(f"[测试消息] 收到客户端 {connection_id} 的测试消息，设备: {device_id}")
                        
                        # 生成一个测试警报
                        # 生成这个设备的测试数据
                        test_alert = {
                            "type": "alert",
                            "title": f"测试警报 - {device_id}",
                            "content": f"这是一条来自后端的测试警报，设备ID: {device_id}",
                            "alertType": "info",
                            "timestamp": datetime.now().isoformat(),
                            "id": f"test-{int(time.time() * 1000)}",
                            "device": {
                                "id": device_id
                            }
                        }
                        
                        # 广播警报
                        logger.info(f"[测试警报] 广播测试警报消息")
                        await manager.broadcast(test_alert)
                        
                        # 发送确认消息
                        await websocket.send_json({
                            "type": "test_response",
                            "message": f"测试消息已处理，生成了测试警报",
                            "timestamp": datetime.now().isoformat()
                        })
                        logger.info(f"[测试确认] 已发送测试确认消息")
                    
                    elif data.get("type") == "heartbeat":
                        # 处理心跳消息
                        # logger.debug(f"[心跳] 收到客户端 {connection_id} 的心跳消息")
                        
                        # 回复心跳响应
                        await websocket.send_json({
                            "type": "pong",
                            "timestamp": datetime.now().isoformat()
                        })
                        # logger.debug(f"[心跳响应] 已向客户端 {connection_id} 发送心跳响应")
                    
                    else:
                        logger.warning(f"[未知消息] 客户端 {connection_id} 发送了未知类型的消息")
                        await websocket.send_json({
                            "type": "error",
                            "message": "未知的消息类型"
                        })
                
                except asyncio.TimeoutError:
                    # logger.debug(f"[超时检测] 客户端 {connection_id} 消息接收超时，发送心跳")
                    # 发送心跳消息
                    await websocket.send_json({
                        "type": "heartbeat",
                        "timestamp": time.time()
                    })
                    continue
                    
                except WebSocketDisconnect:
                    logger.warning(f"[连接断开] 客户端 {connection_id} WebSocket连接已断开")
                    break
                    
                except Exception as e:
                    logger.error(f"[处理错误] 处理客户端 {connection_id} 消息时出错: {str(e)}")
                    await websocket.send_json({
                        "type": "error",
                        "message": f"处理消息时出错: {str(e)}"
                    })
                    continue
                    
        except Exception as e:
            logger.error(f"[循环错误] 客户端 {connection_id} WebSocket消息循环出错: {str(e)}")
            
    except Exception as e:
        logger.error(f"[连接错误] 处理客户端 {connection_id} WebSocket连接时出错: {str(e)}")
        
    finally:
        # 确保连接被正确清理
        try:
            await manager.disconnect(websocket)
            logger.info(f"[连接清理] 客户端 {connection_id} WebSocket连接已清理")
        except Exception as e:
            logger.error(f"[清理错误] 清理客户端 {connection_id} WebSocket连接时出错: {str(e)}")

# 添加一个HTTP路由用于触发摄像头警报广播
@router.post("/broadcast/camera_alert")
async def send_camera_alert(
    position: dict = Body(..., description="摄像头位置信息"),
    title: str = Body("摄像头警报", description="警报标题"),
    message: str = Body(None, description="警报详细信息")
):
    """发送摄像头警报到所有连接的WebSocket客户端"""
    alert_id = f"camera-{int(time.time() * 1000)}"
    
    # 如果没有提供消息内容，则使用位置信息
    if not message:
        message = f"检测到可疑活动，摄像头已移动至坐标: x={position.get('x', 0):.2f}, y={position.get('y', 0):.2f}, z={position.get('z', 0):.2f}"
    
    # 创建警报消息
    alert_message = {
        "type": "camera",
        "id": alert_id,
        "title": title,
        "content": message,
        "position": position,
        "alertType": "danger",
        "timestamp": datetime.now().isoformat()
    }
    
    # 广播消息
    active_connections = len(manager.active_connections)
    await manager.broadcast(alert_message)
    
    logger.info(f"已广播摄像头警报 [ID: {alert_id}] 到 {active_connections} 个活跃连接")
    
    return {
        "success": True,
        "message": f"已发送摄像头警报到 {active_connections} 个连接",
        "alert_id": alert_id
    }

# 添加一个HTTP路由用于处理特定的摄像头移动请求
@router.post("/camera/move")
async def camera_move_request(
    data: dict = Body(..., description="摄像头移动数据")
):
    """处理摄像头移动请求并发送警报消息"""
    try:
        # 从请求中提取摄像头移动信息
        action = data.get("action")
        position = data.get("position", {})
        timestamp = data.get("timestamp")
        
        if not action or not position:
            return {
                "success": False,
                "message": "请求缺少必要的字段: action或position"
            }
        
        # 生成警报ID
        alert_id = f"camera-move-{int(time.time() * 1000)}"
        
        # 格式化位置信息
        pos_str = f"x={position.get('x', 0):.2f}, y={position.get('y', 0):.2f}, z={position.get('z', 0):.2f}"
        
        # 创建警报消息
        timestamp_str = timestamp or datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        alert_message = {
            "type": "camera",
            "id": alert_id,
            "title": "摄像头移动警报",
            "content": f"摄像头执行了 {action} 操作\n位置: {pos_str}\n时间: {timestamp_str}",
            "position": position,
            "action": action,
            "alertType": "info",
            "timestamp": datetime.now().isoformat(),
            "originalTimestamp": timestamp_str
        }
        
        # 广播消息
        active_connections = len(manager.active_connections)
        await manager.broadcast(alert_message)
        
        logger.info(f"已广播摄像头移动警报 [ID: {alert_id}, 动作: {action}] 到 {active_connections} 个活跃连接")
        
        return {
            "success": True,
            "message": f"已发送摄像头移动警报到 {active_connections} 个连接",
            "alert_id": alert_id
        }
        
    except Exception as e:
        logger.error(f"处理摄像头移动请求时发生错误: {str(e)}")
        return {
            "success": False,
            "message": f"处理请求时发生错误: {str(e)}"
        }

# 添加一个HTTP路由用于获取设备状态
@router.get("/api/devices/status")
async def get_devices_status():
    """获取所有设备状态"""
    try:
        return alert_generator.get_device_status()
    except Exception as e:
        logger.error(f"获取设备状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取设备状态失败: {str(e)}"
        )

# 添加以下测试接口
@router.post("/api/test/alert")
async def generate_test_alert(
    device_id: str = Body("JH001", description="设备ID"),
    alert_type: str = Body("warning", description="警报类型")
):
    """生成测试警报，直接触发警报广播"""
    try:
        logger.info(f"[测试警报] 生成测试警报: 设备={device_id}, 类型={alert_type}")
        
        # 生成测试警报数据
        test_alert = {
            "type": "alert",
            "title": f"测试警报 - {device_id}",
            "content": f"这是一条手动触发的测试警报，警报类型: {alert_type}",
            "alertType": alert_type,
            "timestamp": datetime.now().isoformat(),
            "id": f"test-alert-{int(time.time() * 1000)}",
            "deviceId": device_id
        }
        
        # 广播警报
        await manager.broadcast(test_alert)
        
        return {
            "success": True,
            "message": "测试警报已广播",
            "alert": test_alert
        }
    except Exception as e:
        logger.error(f"生成测试警报失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"生成测试警报失败: {str(e)}"
        )

@router.post("/api/test/device_data")
async def test_device_data(
    data: dict = Body(..., description="设备数据")
):
    """模拟接收设备数据并检测警报"""
    try:
        device_id = data.get("deviceId")
        if not device_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少设备ID"
            )
            
        logger.info(f"[测试数据] 接收设备 {device_id} 测试数据")
        
        # 将数据传递给警报生成器
        alerts = alert_generator.update_device_data(device_id, data)
        
        # 如果生成了警报，广播警报
        if alerts and len(alerts) > 0:
            logger.info(f"[测试警报] 设备数据生成了 {len(alerts)} 个警报")
            await manager.broadcast_alerts(alerts)
            
        return {
            "success": True,
            "message": f"设备数据已处理，生成 {len(alerts) if alerts else 0} 个警报",
            "alerts": alerts
        }
    except Exception as e:
        logger.error(f"处理测试设备数据失败: {str(e)}")
        traceback_str = traceback.format_exc()
        logger.error(f"详细错误: {traceback_str}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理测试设备数据失败: {str(e)}"
        ) 