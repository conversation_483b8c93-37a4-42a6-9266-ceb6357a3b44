#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChatTTS模型手动下载脚本
用于解决网络连接问题导致的模型下载失败
"""

import os
import requests
import hashlib
from pathlib import Path
from tqdm import tqdm

# 模型文件列表
MODEL_FILES = {
    "asset/Decoder.safetensors": "https://huggingface.co/2Noise/ChatTTS/resolve/main/asset/Decoder.safetensors",
    "asset/DVAE.safetensors": "https://huggingface.co/2Noise/ChatTTS/resolve/main/asset/DVAE.safetensors", 
    "asset/Embed.safetensors": "https://huggingface.co/2Noise/ChatTTS/resolve/main/asset/Embed.safetensors",
    "asset/Vocos.safetensors": "https://huggingface.co/2Noise/ChatTTS/resolve/main/asset/Vocos.safetensors",
    "asset/gpt/config.json": "https://huggingface.co/2Noise/ChatTTS/resolve/main/asset/gpt/config.json",
    "asset/gpt/model.safetensors": "https://huggingface.co/2Noise/ChatTTS/resolve/main/asset/gpt/model.safetensors",
    "asset/tokenizer/special_tokens_map.json": "https://huggingface.co/2Noise/ChatTTS/resolve/main/asset/tokenizer/special_tokens_map.json",
    "asset/tokenizer/tokenizer_config.json": "https://huggingface.co/2Noise/ChatTTS/resolve/main/asset/tokenizer/tokenizer_config.json",
    "asset/tokenizer/tokenizer.json": "https://huggingface.co/2Noise/ChatTTS/resolve/main/asset/tokenizer/tokenizer.json"
}

# 镜像源配置
MIRROR_SOURCES = {
    "huggingface": "https://huggingface.co/2Noise/ChatTTS/resolve/main/",
    "hf-mirror": "https://hf-mirror.com/2Noise/ChatTTS/resolve/main/",
    "modelscope": "https://www.modelscope.cn/models/pzc163/chatTTS/resolve/master/"
}

def download_file(url, local_path, chunk_size=8192):
    """
    下载文件并显示进度条
    """
    try:
        # 创建目录
        local_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 发送请求
        response = requests.get(url, stream=True, timeout=30)
        response.raise_for_status()
        
        # 获取文件大小
        total_size = int(response.headers.get('content-length', 0))
        
        # 下载文件
        with open(local_path, 'wb') as f:
            with tqdm(total=total_size, unit='B', unit_scale=True, desc=local_path.name) as pbar:
                for chunk in response.iter_content(chunk_size=chunk_size):
                    if chunk:
                        f.write(chunk)
                        pbar.update(len(chunk))
        
        print(f"✓ 下载完成: {local_path}")
        return True
        
    except Exception as e:
        print(f"✗ 下载失败: {local_path} - {str(e)}")
        return False

def check_file_exists(file_path):
    """检查文件是否存在"""
    return file_path.exists() and file_path.stat().st_size > 0

def download_models(mirror="huggingface", force=False):
    """
    下载ChatTTS模型文件
    
    Args:
        mirror: 镜像源 ("huggingface", "hf-mirror", "modelscope")
        force: 是否强制重新下载
    """
    print(f"使用镜像源: {mirror}")
    base_url = MIRROR_SOURCES.get(mirror, MIRROR_SOURCES["huggingface"])
    
    # 获取当前目录
    current_dir = Path.cwd()
    
    success_count = 0
    total_count = len(MODEL_FILES)
    
    for relative_path, original_url in MODEL_FILES.items():
        local_path = current_dir / relative_path
        
        # 检查文件是否已存在
        if not force and check_file_exists(local_path):
            print(f"⚡ 文件已存在，跳过: {local_path}")
            success_count += 1
            continue
        
        # 构建下载URL
        if mirror == "huggingface":
            download_url = original_url
        else:
            download_url = base_url + relative_path
        
        print(f"📥 下载: {relative_path}")
        print(f"   URL: {download_url}")
        
        # 下载文件
        if download_file(download_url, local_path):
            success_count += 1
        else:
            print(f"❌ 下载失败，尝试其他镜像源")
    
    print(f"\n下载完成: {success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 所有模型文件下载成功！")
        return True
    else:
        print("⚠️  部分文件下载失败，请检查网络连接或尝试其他镜像源")
        return False

def main():
    """主函数"""
    print("ChatTTS 模型下载工具")
    print("=" * 50)
    
    # 检查当前目录
    current_dir = Path.cwd()
    print(f"当前目录: {current_dir}")
    
    # 选择镜像源
    print("\n可用的镜像源:")
    for i, (key, url) in enumerate(MIRROR_SOURCES.items(), 1):
        print(f"{i}. {key}: {url}")
    
    try:
        choice = input("\n请选择镜像源 (1-3, 默认1): ").strip()
        if not choice:
            choice = "1"
        
        mirror_keys = list(MIRROR_SOURCES.keys())
        selected_mirror = mirror_keys[int(choice) - 1]
        
    except (ValueError, IndexError):
        print("无效选择，使用默认镜像源")
        selected_mirror = "huggingface"
    
    # 询问是否强制重新下载
    force_download = input("\n是否强制重新下载已存在的文件? (y/N): ").strip().lower() == 'y'
    
    print(f"\n开始下载模型文件...")
    print(f"镜像源: {selected_mirror}")
    print(f"强制下载: {force_download}")
    print("-" * 50)
    
    # 开始下载
    success = download_models(selected_mirror, force_download)
    
    if success:
        print("\n✅ 模型下载完成！现在可以启动TTS服务了。")
        print("运行命令: python tts_service.py")
    else:
        print("\n❌ 模型下载未完全成功。")
        print("建议:")
        print("1. 检查网络连接")
        print("2. 尝试使用VPN")
        print("3. 尝试其他镜像源")
        print("4. 手动下载文件")

if __name__ == "__main__":
    main()
