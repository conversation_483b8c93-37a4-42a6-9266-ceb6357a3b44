#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChatTTS 文本转语音服务
提供RESTful API接口，支持中文文本转语音功能
"""

import os
import io
import base64
import asyncio
import tempfile
import re
from typing import Optional, Dict, Any
from pathlib import Path

import torch
import torchaudio
import numpy as np
from fastapi import FastAPI, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from pydantic import BaseModel
import uvicorn
from loguru import logger

# 导入我们的TTS核心模块
from tts_core import tts_core
from voice_config import get_voice_config, list_available_voices, DEFAULT_VOICE

# 配置日志
logger.add("logs/tts_service.log", rotation="1 day", retention="7 days", level="INFO")

# 创建FastAPI应用
app = FastAPI(
    title="ChatTTS 文本转语音服务",
    description="基于ChatTTS的中文文本转语音API服务",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 获取默认音色配置
default_config = get_voice_config(DEFAULT_VOICE)
FIXED_VOICE_SEED = default_config["voice_seed"]  # 固定的音色种子，产生严肃、智慧的男性声音
FIXED_AUDIO_SEED = default_config["audio_seed"]  # 固定的音频种子，确保一致性
DEFAULT_TEMPERATURE = default_config["temperature"]
DEFAULT_TOP_P = default_config["top_p"]
DEFAULT_TOP_K = default_config["top_k"]

def preprocess_text(text: str) -> str:
    """
    预处理文本，去除Markdown格式并优化朗读效果

    Args:
        text: 原始文本

    Returns:
        str: 处理后的文本
    """
    if not text:
        return ""

    # 保存原始文本用于调试
    original_text = text

    # 1. 去除Markdown标题标记
    text = re.sub(r'^#{1,6}\s+', '', text, flags=re.MULTILINE)

    # 2. 去除粗体和斜体标记
    text = re.sub(r'\*\*\*(.+?)\*\*\*', r'\1', text)  # 粗斜体
    text = re.sub(r'\*\*(.+?)\*\*', r'\1', text)      # 粗体
    text = re.sub(r'\*(.+?)\*', r'\1', text)          # 斜体
    text = re.sub(r'__(.+?)__', r'\1', text)          # 粗体（下划线）
    text = re.sub(r'_(.+?)_', r'\1', text)            # 斜体（下划线）

    # 3. 处理链接 [文本](URL) -> 文本
    text = re.sub(r'\[([^\]]+)\]\([^)]+\)', r'\1', text)

    # 4. 去除图片标记 ![alt](url)
    text = re.sub(r'!\[[^\]]*\]\([^)]+\)', '', text)

    # 5. 去除代码块标记
    text = re.sub(r'```[^`]*```', '', text, flags=re.DOTALL)
    text = re.sub(r'`([^`]+)`', r'\1', text)

    # 6. 去除引用标记
    text = re.sub(r'^>\s+', '', text, flags=re.MULTILINE)

    # 7. 去除列表标记
    text = re.sub(r'^\s*[-*+]\s+', '', text, flags=re.MULTILINE)
    text = re.sub(r'^\s*\d+\.\s+', '', text, flags=re.MULTILINE)

    # 8. 去除表格分隔符
    text = re.sub(r'\|', ' ', text)
    text = re.sub(r'^[-\s:]+$', '', text, flags=re.MULTILINE)

    # 9. 处理HTML标签
    text = re.sub(r'<[^>]+>', '', text)

    # 10. 关键步骤：将字母数字组合转换为中文表达
    # A2 -> A二, B1 -> B一, C3 -> C三 等
    number_map = {
        '0': '零', '1': '一', '2': '二', '3': '三', '4': '四',
        '5': '五', '6': '六', '7': '七', '8': '八', '9': '九'
    }

    def replace_letter_number(match):
        letter = match.group(1)
        number = match.group(2)
        chinese_number = ''.join(number_map.get(digit, digit) for digit in number)
        return f'{letter}{chinese_number}'

    text = re.sub(r'([A-Z])(\d+)', replace_letter_number, text)
    logger.info(f"步骤10-字母数字转中文后: '{text}'")

    # 11. 处理英文单词
    text = re.sub(r'([A-Za-z]{2,})', r' \1 ', text)
    logger.info(f"步骤11-英文单词处理后: '{text}'")

    # 12. 处理数字
    text = re.sub(r'(\d+)', r' \1 ', text)
    logger.info(f"步骤12-数字处理后: '{text}'")

    # 13. 处理单个字母（包括中文数字组合）
    text = re.sub(r'([A-Z][一二三四五六七八九零]+)', r' \1 ', text)
    logger.info(f"步骤13-字母中文数字处理后: '{text}'")

    # 14. 规范化空白字符
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()
    logger.info(f"步骤14-最终结果: '{text}'")

    # 14. 添加适当的停顿标记
    text = re.sub(r'([.!?])\s*', r'\1 [pause] ', text)
    text = re.sub(r'([,;])\s*', r'\1 [short_pause] ', text)

    # 最终清理
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()

    logger.info(f"文本预处理完成: '{original_text}' -> '{text}'")

    return text

class TTSRequest(BaseModel):
    """TTS请求模型"""
    text: str
    voice_seed: Optional[int] = FIXED_VOICE_SEED  # 固定音色种子
    temperature: Optional[float] = DEFAULT_TEMPERATURE  # 使用配置文件中的默认值
    top_p: Optional[float] = DEFAULT_TOP_P  # 使用配置文件中的默认值
    top_k: Optional[int] = DEFAULT_TOP_K  # 使用配置文件中的默认值
    audio_seed: Optional[int] = FIXED_AUDIO_SEED  # 固定音频种子
    text_seed: Optional[int] = None
    refine_text: Optional[bool] = True
    preprocess_text: Optional[bool] = True  # 是否进行文本预处理
    voice_profile: Optional[str] = DEFAULT_VOICE  # 音色配置名称

class TTSResponse(BaseModel):
    """TTS响应模型"""
    success: bool
    message: str
    audio_base64: Optional[str] = None
    duration: Optional[float] = None
    sample_rate: Optional[int] = None

async def initialize_chattts():
    """初始化ChatTTS模型"""
    try:
        logger.info("正在初始化ChatTTS模型...")

        # 使用TTS核心模块初始化
        success = tts_core.initialize(device="auto", compile_model=False)

        if success:
            logger.info("ChatTTS模型初始化成功")
        else:
            logger.error("ChatTTS模型初始化失败")
            raise Exception("模型加载失败")

    except Exception as e:
        logger.error(f"初始化ChatTTS时出错: {str(e)}")
        raise e

@app.on_event("startup")
async def startup_event():
    """应用启动时的事件处理"""
    await initialize_chattts()

@app.get("/")
async def root():
    """根路径，返回服务信息"""
    model_info = tts_core.get_model_info()
    return {
        "service": "ChatTTS 文本转语音服务",
        "version": "1.0.0",
        "status": "running",
        "device": model_info.get("device", "unknown"),
        "model_info": model_info
    }

@app.get("/health")
async def health_check():
    """健康检查接口"""
    model_info = tts_core.get_model_info()
    return {
        "status": "healthy" if tts_core.is_ready() else "not_ready",
        "model_loaded": model_info.get("model_loaded", False),
        "device": model_info.get("device", "unknown"),
        "initialized": model_info.get("initialized", False),
        "default_voice": DEFAULT_VOICE,
        "fixed_voice_seed": FIXED_VOICE_SEED,
        "fixed_audio_seed": FIXED_AUDIO_SEED
    }

@app.get("/voices")
async def list_voices():
    """列出所有可用的音色配置"""
    try:
        voices = list_available_voices()
        return {
            "success": True,
            "voices": voices,
            "default_voice": DEFAULT_VOICE,
            "current_config": get_voice_config(DEFAULT_VOICE)
        }
    except Exception as e:
        logger.error(f"获取音色列表出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取音色列表失败: {str(e)}")

@app.post("/tts", response_model=TTSResponse)
async def text_to_speech(request: TTSRequest):
    """
    文本转语音接口

    Args:
        request: TTS请求参数

    Returns:
        TTSResponse: 包含音频数据的响应
    """
    if not tts_core.is_ready():
        raise HTTPException(status_code=503, detail="TTS模型未初始化")

    try:
        # 获取音色配置
        voice_config = get_voice_config(request.voice_profile)

        # 文本预处理
        processed_text = request.text
        if request.preprocess_text:
            processed_text = preprocess_text(request.text)
            logger.info(f"文本预处理: '{request.text[:30]}...' -> '{processed_text[:30]}...'")

        # 强制使用固定的音色配置，完全忽略请求中的参数以确保绝对一致性
        voice_seed = 42  # 强制固定为42，绝对不变
        audio_seed = 42  # 强制固定为42，与voice_seed相同确保一致
        text_seed = 42   # 文本种子也固定
        temperature = 0.05  # 极低随机性
        top_p = 0.3      # 极低采样范围
        top_k = 5        # 最少候选词
        refine_text = False  # 禁用文本优化避免随机性

        logger.info(f"处理TTS请求，使用音色配置: {request.voice_profile}, 音色种子: {voice_seed}")

        # 使用TTS核心模块生成语音 - 强制使用固定参数
        audio_data, sample_rate = tts_core.generate_speech(
            text=processed_text,
            voice_seed=voice_seed,
            audio_seed=audio_seed,
            refine_text=refine_text,  # 使用固定的refine_text
            temperature=temperature,
            top_p=top_p,
            top_k=top_k
        )

        # 将音频转换为字节流
        audio_bytes = tts_core.audio_to_bytes(audio_data, sample_rate)

        # 转换为base64编码
        audio_base64 = base64.b64encode(audio_bytes).decode('utf-8')

        # 计算音频时长
        duration = len(audio_data) / sample_rate

        logger.info(f"TTS处理完成，音频时长: {duration:.2f}秒，使用音色种子: {voice_seed}")

        return TTSResponse(
            success=True,
            message=f"音频生成成功，音色配置: {request.voice_profile}, 音色种子: {voice_seed}",
            audio_base64=audio_base64,
            duration=duration,
            sample_rate=sample_rate
        )

    except Exception as e:
        logger.error(f"TTS处理出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"TTS处理失败: {str(e)}")

@app.post("/tts/stream")
async def text_to_speech_stream(request: TTSRequest):
    """
    流式文本转语音接口
    直接返回音频文件流
    """
    if not tts_core.is_ready():
        raise HTTPException(status_code=503, detail="TTS模型未初始化")

    try:
        # 获取音色配置
        voice_config = get_voice_config(request.voice_profile)

        # 文本预处理
        processed_text = request.text
        if request.preprocess_text:
            processed_text = preprocess_text(request.text)
            logger.info(f"流式TTS文本预处理: '{request.text[:30]}...' -> '{processed_text[:30]}...'")

        # 强制使用固定的音色配置，完全忽略请求中的参数以确保绝对一致性
        voice_seed = 42  # 强制固定为42，绝对不变
        audio_seed = 42  # 强制固定为42，与voice_seed相同确保一致
        text_seed = 42   # 文本种子也固定
        temperature = 0.05  # 极低随机性
        top_p = 0.3      # 极低采样范围
        top_k = 5        # 最少候选词
        refine_text = False  # 禁用文本优化避免随机性

        logger.info(f"处理流式TTS请求，使用音色配置: {request.voice_profile}, 音色种子: {voice_seed}")

        # 使用TTS核心模块生成语音 - 强制使用固定参数
        audio_data, sample_rate = tts_core.generate_speech(
            text=processed_text,
            voice_seed=voice_seed,
            audio_seed=audio_seed,
            refine_text=refine_text,  # 使用固定的refine_text
            temperature=temperature,
            top_p=top_p,
            top_k=top_k
        )

        # 将音频转换为字节流
        audio_bytes = tts_core.audio_to_bytes(audio_data, sample_rate)

        return StreamingResponse(
            io.BytesIO(audio_bytes),
            media_type="audio/wav",
            headers={
                "Content-Disposition": "attachment; filename=tts_output.wav",
                "Content-Length": str(len(audio_bytes)),
                "X-Voice-Seed": str(voice_seed),
                "X-Voice-Profile": request.voice_profile,
                "X-Audio-Seed": str(audio_seed)
            }
        )

    except Exception as e:
        logger.error(f"流式TTS处理出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"流式TTS处理失败: {str(e)}")

@app.post("/preprocess")
async def preview_text_preprocessing(request: dict):
    """
    预览文本预处理结果

    Args:
        request: 包含text字段的字典

    Returns:
        dict: 包含原始文本和处理后文本的字典
    """
    try:
        original_text = request.get("text", "")
        if not original_text:
            raise HTTPException(status_code=400, detail="文本不能为空")

        processed_text = preprocess_text(original_text)

        return {
            "success": True,
            "original_text": original_text,
            "processed_text": processed_text,
            "voice_seed": FIXED_VOICE_SEED,
            "audio_seed": FIXED_AUDIO_SEED,
            "message": "文本预处理完成"
        }

    except Exception as e:
        logger.error(f"文本预处理出错: {str(e)}")
        raise HTTPException(status_code=500, detail=f"文本预处理失败: {str(e)}")

if __name__ == "__main__":
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)
    
    # 启动服务
    uvicorn.run(
        "tts_service:app",
        host="0.0.0.0",
        port=8003,
        reload=False,  # 生产环境建议设为False
        log_level="info"
    )
