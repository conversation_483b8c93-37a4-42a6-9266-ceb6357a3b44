# ChatTTS 核心依赖
torch>=2.0.0
torchaudio>=2.0.0
transformers>=4.30.0
numpy>=1.21.0
scipy>=1.7.0
librosa>=0.9.0
soundfile>=0.12.0

# Web服务框架
fastapi>=0.100.0
uvicorn>=0.23.0
python-multipart>=0.0.6

# 音频处理
pydub>=0.25.0
wave

# HTTP客户端和工具
requests>=2.28.0
aiofiles>=23.0.0

# 日志和配置
python-dotenv>=1.0.0
loguru>=0.7.0

# CORS支持
fastapi-cors>=0.0.6

# 可选：如果需要GPU加速
# torch-audio  # 根据CUDA版本选择

# ChatTTS (从GitHub安装)
# git+https://github.com/2noise/ChatTTS.git
