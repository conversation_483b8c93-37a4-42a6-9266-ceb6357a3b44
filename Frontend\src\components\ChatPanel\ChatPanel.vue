<template>
  <div class="chat-container" :class="{ 'chat-expanded': isExpanded }">
    <!-- 聊天图标按钮 -->
    <div class="chat-icon" @click="toggleChat" v-if="!isExpanded">
      <i class="fas fa-comments"></i>
    </div>

    <!-- 聊天面板 -->
    <div class="chat-panel" v-if="isExpanded">
      <!-- 面板头部 -->
      <div class="chat-header">
        <h3>智能助手</h3>
        <div class="header-actions">
          <button class="tts-toggle-btn" @click="toggleTTS" :title="isTTSEnabled ? 'TTS已启用' : 'TTS已禁用'">
            <i class="fas fa-volume-up" :class="{ 'tts-enabled': isTTSEnabled, 'tts-disabled': !isTTSEnabled }"></i>
          </button>
          <button class="auto-play-btn" @click="toggleAutoPlay" :title="isAutoPlayEnabled ? 'TTS自动播放已启用' : 'TTS自动播放已禁用'">
            <i class="fas fa-play-circle" :class="{ 'auto-play-enabled': isAutoPlayEnabled, 'auto-play-disabled': !isAutoPlayEnabled }"></i>
            <span v-if="autoPlayIndicator" class="auto-play-indicator">●</span>
          </button>
          <button class="backend-switch" @click="toggleBackend" :title="useMcpBackend ? '当前:智能后台' : '当前:知识库后台'">
            <i class="fas fa-server"></i>
            <span class="backend-indicator" :class="{ 'active': useMcpBackend }"></span>
          </button>
          <button class="reset-btn" @click="resetChat" title="重置对话">
            <i class="fas fa-redo"></i>
          </button>
          <button class="doc-manager-btn" @click="toggleDocManager">
            <i class="fas fa-folder"></i>
          </button>
          <button class="close-btn" @click="toggleChat" title="关闭">
            <i class="fa-solid fa-xmark"></i>
          </button>
        </div>
      </div>

      <!-- 消息列表区域 -->
      <div class="chat-messages" ref="messagesContainer">
        <div
          v-for="msg in messages"
          :key="msg.id"
          :class="['message', msg.role === 'user' ? 'user-message' : 'ai-message']"
        >
          <div class="message-content">
            <div v-if="msg.role === 'assistant'" v-html="renderMarkdown(msg.content || '')" ref="messageContent"></div>
            <div v-else>{{ msg.content }}</div>
          </div>

          <!-- TTS播放按钮 -->
          <div v-if="msg.role === 'assistant' && isTTSReady && msg.content && msg.content !== '思考中...'" class="message-actions">
            <button
              class="tts-play-btn"
              @click="playMessageAudio(msg)"
              :disabled="isPlayingAudio"
              :title="isPlayingAudio ? '正在播放...' : '播放语音'"
            >
              <i class="fas" :class="isPlayingAudio && currentPlayingMessage === msg.id ? 'fa-stop' : 'fa-play'"></i>
            </button>
          </div>

          <!-- 在第一条助手消息下添加常用问题按钮 -->
          <div v-if="msg.role === 'assistant' && msg.id === messages[0].id" class="common-questions">
            <div class="common-questions-title">常用问题：</div>
            <div class="question-buttons">
              <button 
                v-for="(question, index) in commonQuestions" 
                :key="index" 
                @click="sendCommonQuestion(question)"
                :disabled="isStreaming"
              >
                {{ question }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <textarea 
          v-model="userInput"
          @keydown.enter.prevent="sendMessage"
          placeholder="请输入您的问题..."
          rows="2"
        ></textarea>
        <div class="input-buttons">
          <button @click="stopMessageGeneration" v-if="isStreaming" class="stop-btn" title="停止生成">
            <i class="fas fa-stop"></i>
          </button>
          <button @click="sendMessage" :disabled="!userInput.trim() || isStreaming">
            <i class="fas fa-paper-plane"></i>
          </button>
        </div>
      </div>
    </div>

    <!-- 文档系统弹窗 -->
    <DocSystemModal v-if="showDocManager" @close="toggleDocManager" />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, inject, getCurrentInstance, onUpdated } from 'vue'
import { marked } from 'marked'
import DOMPurify from 'dompurify'
import DocSystemModal from '../RagManager/DocSystemModal.vue'
import hljs from 'highlight.js'
import 'highlight.js/styles/github-dark.css'
import http from '@/utils/axios'
import { emitter } from '@/utils/emitter'

// 配置DOMPurify，允许onclick事件
DOMPurify.addHook('afterSanitizeAttributes', function(node) {
  // 允许所有元素的onclick属性
  if (node.hasAttribute('onclick')) {
    // 保留onclick属性
    node.setAttribute('data-onclick', node.getAttribute('onclick'));
    // 为了兼容性，我们添加一个data-action属性来标记可点击元素
    node.setAttribute('data-action', 'true');
    // 为可点击元素添加样式标识
    node.classList.add('clickable-element');
  }
  
  // 为图片和链接添加指针样式
  if (node.tagName === 'IMG' || node.tagName === 'A') {
    node.style.cursor = 'pointer';
  }
  
  // 保留类名
  if (node.hasAttribute('class')) {
    node.setAttribute('class', node.getAttribute('class'));
  }
});

// 设置DOMPurify配置
const domPurifyConfig = {
  ADD_ATTR: ['class', 'style', 'target', 'data-onclick', 'data-action'],
  ADD_TAGS: ['div', 'span'],
  ALLOW_DATA_ATTR: true,
  FORCE_BODY: true,
  ALLOWED_TAGS: ['div', 'span', 'p', 'br', 'a', 'img', 'b', 'i', 'strong', 'em', 'code', 'pre', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
  ALLOWED_ATTR: ['class', 'style', 'href', 'src', 'alt', 'target', 'data-onclick', 'data-action']
};

// 自定义指令，处理处理经过DOMPurify清理后的HTML内容中的点击事件
const handleContentClicks = (el) => {
  el.addEventListener('click', (e) => {
    // 查找最近的可点击元素
    let target = e.target;
    let dataOnclick = null;
    
    // 向上遍历DOM树查找带有data-onclick属性的元素
    while (target && target !== el) {
      if (target.hasAttribute('data-onclick')) {
        dataOnclick = target.getAttribute('data-onclick');
        break;
      }
      target = target.parentElement;
    }
    
    // 如果找到了data-onclick属性
    if (dataOnclick) {
      console.log('执行点击操作:', dataOnclick);
      try {
        // 判断是否是打开图片还是PDF
        if (dataOnclick.includes('open-image-view')) {
          // 提取图片路径和标题
          const match = dataOnclick.match(/imagePath: ['"]([^'"]+)['"], title: ['"]([^'"]+)['"]/);
          if (match) {
            const imagePath = match[1];
            const title = match[2];
            console.log('打开图片:', imagePath, title);
            
            // 触发自定义事件打开图片
            window.dispatchEvent(new CustomEvent('open-image-view', {
              detail: { imagePath, title }
            }));
          }
        } 
        else if (dataOnclick.includes('open-pdf-file')) {
          // 提取PDF路径和标题
          const match = dataOnclick.match(/filePath: ['"]([^'"]+)['"], title: ['"]([^'"]+)['"]/);
          if (match) {
            const filePath = match[1];
            const title = match[2];
            console.log('打开PDF:', filePath, title);
            
            // 触发自定义事件打开PDF
            window.dispatchEvent(new CustomEvent('open-pdf-file', {
              detail: { filePath, title }
            }));
          }
        }
      } catch (err) {
        console.error('执行点击操作失败:', err);
      }
    }
  });
};

// 配置marked以使用highlight.js
marked.setOptions({
  highlight: function (code, language) {
    if (language && hljs.getLanguage(language)) {
      try {
        return hljs.highlight(code, { language }).value
      } catch (e) {
        console.error(e)
      }
    }
    return code
  }
})

const isExpanded = ref(false)
const messages = ref([])
const userInput = ref('')
const messagesContainer = ref(null)
const showDocManager = ref(false)
const isStreaming = ref(false)
const currentStreamingMessage = ref('')
const typingSpeed = ref(1) // 降低打字速度以更好地跟随流式响应
const abortController = ref(null) // 用于中止请求的控制器
const devicePanelOpened = ref(false)

// TTS相关变量
const isTTSEnabled = ref(true) // TTS功能是否启用
const isTTSReady = ref(false) // TTS服务是否就绪
const isPlayingAudio = ref(false) // 是否正在播放音频
const currentPlayingMessage = ref(null) // 当前播放的消息ID
const currentAudio = ref(null) // 当前音频对象
const ttsServiceUrl = ref('http://localhost:8003') // TTS服务地址

// 自动播放相关变量
const isAutoPlayEnabled = ref(false) // 自动播放功能是否启用
const audioQueue = ref([]) // 音频播放队列
const isProcessingQueue = ref(false) // 是否正在处理播放队列
const autoPlayIndicator = ref(false) // 自动播放状态指示器

// TTS固定音色配置 - 强制使用完全相同的参数，确保音色绝对一致
const TTS_CONFIG = {
  voice_profile: "serious_male",    // 固定使用严肃智慧男性音色
  voice_seed: 42,                   // 强制固定音色种子，绝对不变
  audio_seed: 42,                   // 使用相同的种子确保完全一致
  text_seed: 42,                    // 文本种子也固定
  temperature: 0.05,                // 极低随机性，确保稳定
  top_p: 0.3,                       // 进一步降低采样范围
  top_k: 5,                         // 最少候选词，最大一致性
  refine_text: false,               // 禁用文本优化，避免随机性
  preprocess_text: true             // 启用Markdown文本预处理，确保A2等被正确朗读
}

// 场景引用
const sceneRef = inject('sceneRef')

// API配置
const apiKey = 'ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG'
const chatId = 'c1a83f6f475c11f0bbc3345a603cb29c'

// 添加新的本地MCP后台API配置
const useMcpBackend = ref(true) // 是否使用本地MCP后台
const mcpChatId = 'local-mcp'  // MCP聊天ID

// 添加强制使用智能后台的问题列表
const mcpBackendQuestions = [
  '获取海南19-13C的实时数据'
]

// 添加常用问题列表
const commonQuestions = [
  '获取海南19-13C的实时数据',
  '知识库中站场巡检工作有哪些',
  '知识库中采油作业三区的组织机构情况',
  '知识库中井口刺漏发生井喷如何处理',
  '打开二号站17-5井组的摄像头',
  '调出作业区目前生产油井的A2日报',
  '调出作业区10口井的功图',
  '展示中控班管理手册'
]

// 切换聊天面板
const toggleChat = () => {
  isExpanded.value = !isExpanded.value
  if (isExpanded.value) {
    // 打开聊天时禁用键盘控制
    sceneRef.value?.setKeyboardControlsEnabled(false)
    nextTick(() => {
      scrollToBottom()
    })
  } else {
    // 关闭聊天时启用键盘控制
    sceneRef.value?.setKeyboardControlsEnabled(true)
  }
}

// 切换文档管理器显示状态
const toggleDocManager = () => {
  showDocManager.value = !showDocManager.value
}

// 生成唯一消息ID
const generateMessageId = () => {
  return `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
}

// 创建处理流式响应的函数
const updateMessageContent = (text, targetMessage) => {
  if (!targetMessage) return
  
  // 检查当前消息是否为"思考中..."并且收到了实际内容(包含工具结果)
  if (targetMessage.content === '思考中...' && 
      (text.includes('<tool-usage-mark') || text.includes('[工具'))) {
    console.log('检测到从思考中状态更新为实际内容，完全替换内容');
    // 完全替换消息内容，而不是追加
    targetMessage.content = text;
  } else {
    // 正常更新消息内容
    targetMessage.content = text;
  }
  
  // 滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

// 增强型打字机效果函数（带回调功能）
const typewriterEffect = (fullText, targetMessage, delay = 30, callback = null, triggerPoint = 0.4, immediate = false) => {
  if (!targetMessage) return
  
  // 如果已经有动画在执行，可以先清除它
  if (targetMessage.typingTimer) {
    clearTimeout(targetMessage.typingTimer)
  }
  
  // 检查是否包含HTML标记，并获取实际文本内容的起始位置
  let startIndex = 0;
  let hiddenPrefix = '';
  
  // 如果包含工具标记，我们需要跳过这个标记
  if (fullText.includes('<tool-usage-mark></tool-usage-mark>')) {
    startIndex = '<tool-usage-mark></tool-usage-mark>'.length;
    hiddenPrefix = '<tool-usage-mark></tool-usage-mark>';
  }
  
  // 如果要立即显示全部内容，跳过打字动画
  if (immediate) {
    targetMessage.content = fullText;
    
    // 如果有回调，立即执行
    if (callback && !targetMessage.callbackTriggered) {
      targetMessage.callbackTriggered = true;
      callback();
    }
    
    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
    
    return;
  }
  
  let i = startIndex;
  let currentText = hiddenPrefix; // 以隐藏前缀开始
  const textLength = fullText.length;
  
  // 初始设置带有隐藏前缀的内容，但用户看不到前缀
  targetMessage.content = hiddenPrefix;
  
  const typeNextChar = () => {
    if (i < textLength) {
      currentText += fullText.charAt(i);
      targetMessage.content = currentText;
      i++;
      
      // 计算显示进度百分比（不包括隐藏前缀）
      const displayedLength = i - startIndex;
      const totalVisibleLength = textLength - startIndex;
      const progressPercentage = displayedLength / totalVisibleLength;
      
      // 检查是否达到触发点
      if (callback && progressPercentage >= triggerPoint && !targetMessage.callbackTriggered) {
        targetMessage.callbackTriggered = true;
        callback();
      }
      
      // 记录计时器ID以便可以清除
      targetMessage.typingTimer = setTimeout(typeNextChar, delay);
      
      // 在每个字符后滚动
      scrollToBottom();
    } else {
      // 打字完成后清除计时器引用
      targetMessage.typingTimer = null;
      
      // 如果回调尚未触发，则触发
      if (callback && !targetMessage.callbackTriggered) {
        callback();
      }
    }
  };
  
  // 开始打字
  typeNextChar();
};

// 发送消息
const sendMessage = async () => {
  if (!userInput.value.trim() || isStreaming.value) return
  
  // 每次开始新的消息发送时重置设备面板状态
  devicePanelOpened.value = false
  
  // 获取用户问题内容
  const inputQuestion = userInput.value.trim()

  // 检查是否是需要自动切换到知识库后台的特定问题
  const knowledgeBaseQuestions = [
    '知识库中站场巡检工作有哪些',
    '知识库中采油作业三区的组织机构情况',
    '知识库中井口刺漏发生井喷如何处理'
  ]
  
  // 保存当前后台设置，以便在发送特定问题后恢复
  const originalBackendSetting = useMcpBackend.value
  
  // 如果是知识库问题，临时切换到知识库后台（useMcpBackend = false）
  if (knowledgeBaseQuestions.includes(inputQuestion)) {
    console.log(`检测到知识库专属问题: ${inputQuestion}，临时切换到知识库后台`)
    useMcpBackend.value = false
  }
  // 如果是强制使用智能后台的问题，临时切换到智能后台
  else if (mcpBackendQuestions.includes(inputQuestion)) {
    console.log(`检测到智能后台专属问题: ${inputQuestion}，确保使用智能后台`)
    useMcpBackend.value = true
  }

  // 检查是否是打开摄像头的请求
  if (inputQuestion.includes('打开') && inputQuestion.includes('摄像头')) {
    // 从问题中提取设备ID
    const deviceIdMatch = inputQuestion.match(/(JH\d+|H\d+)/i);
    const deviceId = deviceIdMatch ? deviceIdMatch[0] : 'JH005'; // 默认JH005
    
    // 添加用户消息
    messages.value.push({
      id: generateMessageId(),
      role: 'user',
      content: userInput.value
    });

    // 添加AI响应消息
    const assistantMessageId = generateMessageId();
    messages.value.push({
      id: assistantMessageId,
      role: 'assistant',
      content: ''  // 初始为空
    });
    
    // 准备好打开视频面板的回调函数
    const openVideoCallback = () => {
      // 触发打开视频面板的事件
      console.log(`打开设备${deviceId}的视频面板`);
      emitter.emit('open-video-panel', {
        deviceId: deviceId,
        position: { x: window.innerWidth - 380, y: 20 } // 默认位置在右上角
      });
      
      // 标记设备面板已打开
      devicePanelOpened.value = true;
    };
    
    // 使用打字机效果显示消息，并在打字进行到一定程度后执行回调
    const responseMessage = messages.value.find(msg => msg.id === assistantMessageId);
    typewriterEffect(`<tool-usage-mark></tool-usage-mark>已为您打开二号站17-5井组的摄像头进行实时监控，您可以在视频面板中查看监控画面。`, responseMessage, 40, openVideoCallback, 0.6);
    
    // 清空输入框
    userInput.value = '';
    
    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
    
    // 恢复原始后台设置
    useMcpBackend.value = originalBackendSetting;
    
    return; // 提前返回，不再调用sendMessage
  }

  // 检查是否是查询实时数据的请求
  else if ((inputQuestion.includes('实时数据') || inputQuestion.includes('H005') || inputQuestion.includes('JH005') || inputQuestion.includes('海南19-13C')) && !devicePanelOpened.value) {
    // 从问题中提取设备ID，优先匹配"海南xx-xx"格式
    const hainanMatch = inputQuestion.match(/(海南\d+-\d+)/i);
    const deviceIdMatch = hainanMatch || inputQuestion.match(/(JH\d+|H\d+)/i);
    const deviceId = deviceIdMatch ? deviceIdMatch[0] : 'JH005'; // 默认JH005
    
    console.log(`提取的设备ID: ${deviceId}`);
    
    // 仅触发打开设备面板事件
    console.log(`仅打开设备${deviceId}的设备面板`);
    emitter.emit('open-device-panel', {
      deviceId: deviceId
    });
    
    // 标记设备面板已经打开
    devicePanelOpened.value = true;
  }

  // 添加用户消息
  messages.value.push({
    id: generateMessageId(),
    role: 'user',
    content: userInput.value
  })

  // 准备发送到服务器
  const userQuestion = userInput.value
  userInput.value = ''

  // 添加AI思考状态消息
  const assistantMessageId = generateMessageId()
  messages.value.push({
    id: assistantMessageId,
    role: 'assistant',
    content: '思考中...'
  })

  // 重置流式消息状态
  isStreaming.value = true
  currentStreamingMessage.value = ''
  
  // 创建新的AbortController用于取消请求
  abortController.value = new AbortController()

  try {
    // 构建要发送的消息历史
    const messageHistory = messages.value
      .filter(msg => msg.id !== assistantMessageId) // 排除当前的"思考中"消息
      .map(msg => ({
        role: msg.role,
        content: msg.content
      }))

    // 准备请求参数
    const requestData = {
      model: useMcpBackend.value ? 'qwen2.5-7b-instruct-1m' : 'qwen2.5:32b', // 根据后台选择模型
      messages: messageHistory,
      stream: true
    }
    
    // 添加详细调试信息
    console.log('请求数据对象:', requestData)
    
    // 设置请求头 - 根据使用的后台服务选择不同的认证方式
    const headers = {
      'Content-Type': 'application/json'
    }
    
    // 只有使用RAGFlow服务时才添加认证令牌
    if (!useMcpBackend.value) {
      headers['Authorization'] = `Bearer ${apiKey}`
    }

    // 确定请求URL
    const requestUrl = useMcpBackend.value 
      ? `/api/v1/chats_openai/${mcpChatId}/chat/completions`  // 本地MCP后台
      : `/ragflow/api/v1/chats_openai/${chatId}/chat/completions`  // 改为新的RAGFlow代理路径
    
    console.log(`发送聊天请求到: ${requestUrl}`)
    console.log('请求头:', headers)
    const requestPayload = JSON.stringify(requestData)
    console.log('请求体:', requestPayload)

    // 创建EventSource或使用fetch进行流式请求
    const response = await fetch(requestUrl, {
      method: 'POST',
      headers: headers,
      body: requestPayload,
      signal: abortController.value.signal // 添加信号用于中断请求
    })
    
    console.log('收到响应状态:', response.status)
    
    if (!response.ok) {
      // 尝试读取响应内容
      const errorText = await response.text()
      console.error('响应错误内容:', errorText)
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`)
    }

    // 处理流式响应
    const reader = response.body.getReader()
    const decoder = new TextDecoder('utf-8')
    let buffer = ''
    let streamContent = ''
    
    // 先清空助手消息内容
    const lastMessage = messages.value.find(msg => msg.id === assistantMessageId)
    if (lastMessage) {
      lastMessage.content = ''
    }

    // 添加调试日志
    console.log('开始接收流式响应')

    while (true) {
      const { done, value } = await reader.read()
      if (done) {
        console.log('流式响应读取完成')
        break
      }
      
      // 解码数据
      const chunk = decoder.decode(value, { stream: true })
      buffer += chunk
      
      // 添加调试信息
      console.log('收到原始数据块:', chunk)
      
      // 处理可能的多个数据块
      let processBuffer = async () => {
        // 找到完整的数据块
        const dataPos = buffer.indexOf('data:')
        if (dataPos === -1) return false
        
        // 找到数据块结束位置（寻找下一个data:或者结束）
        let endPos = buffer.indexOf('data:', dataPos + 5)
        if (endPos === -1) {
          endPos = buffer.length
        }
        
        // 提取数据行并清理
        const dataLine = buffer.substring(dataPos, endPos).trim()
        // 更新缓冲区，移除已处理的数据
        buffer = buffer.substring(endPos)
        
        // 处理数据行
        if (dataLine.startsWith('data:')) {
          const data = dataLine.substring(5).trim() // 去掉 "data:" 前缀
          console.log('处理数据行:', data) // 调试日志
          
          if (data === '[DONE]') {
            console.log('流式响应结束')

            // 在流结束时，确保如果消息仍显示"思考中..."，则将其替换为空白或更友好的消息
            if (lastMessage && (lastMessage.content === '思考中...' || lastMessage.content === 'thinking...' || lastMessage.content === '处理中...')) {
              lastMessage.content = '已完成，但未返回结果';
              console.log('流结束时替换了思考中状态');
            }

            // 流式响应完成后，触发自动播放
            if (lastMessage && isAutoPlayEnabled.value) {
              console.log('流式响应完成，准备自动播放');
              console.log('流式响应完成时的消息内容:', lastMessage.content);
              console.log('流式响应完成时的消息ID:', lastMessage.id);
              console.log('当前自动播放状态:', isAutoPlayEnabled.value);
              console.log('当前TTS就绪状态:', isTTSReady.value);

              // 延迟一小段时间确保消息内容完全更新
              setTimeout(() => {
                console.log('延迟后开始添加到自动播放队列');
                addToAutoPlayQueue(lastMessage);
              }, 500);
            } else {
              console.log('流式响应完成，但不满足自动播放条件');
              console.log('lastMessage存在:', !!lastMessage);
              console.log('自动播放启用:', isAutoPlayEnabled.value);
            }

            return true
          }
          
          try {
            const json = JSON.parse(data)
            console.log('解析后的JSON对象:', json) // 调试日志
            
            // 处理内容更新
            if (json.choices && json.choices[0]?.delta?.content !== undefined) {
              const content = json.choices[0].delta.content || ''
              console.log('提取的content内容:', content) // 调试日志
              
              // 特殊检查工具调用标记
              if (content.includes('<tool-usage-mark')) {
                console.log('检测到工具调用标记，工具调用内容:', content)
                
                // 检查是否包含数学运算标记
                if (content.includes('data-math-operation="true"')) {
                  console.log('检测到数学运算标记')
                }
                
                // 在检测到工具标记时，确保清除任何思考中状态
                if (lastMessage && (lastMessage.content === '思考中...' || lastMessage.content === 'thinking...' || lastMessage.content === '处理中...')) {
                  streamContent = content // 完全替换内容，而不是追加
                  console.log('检测到工具标记，重置消息内容');
                } else {
                  // 添加普通内容
                  streamContent += content
                }
              } else {
                // 添加普通内容
                streamContent += content
              }
              
              if (lastMessage && lastMessage.id === assistantMessageId) {
                // 更新消息内容，并确保不保留"思考中..."状态
                console.log('更新消息内容，当前累积内容:', streamContent);
                
                // 特殊处理：如果原始内容是"思考中..."但收到了带工具标记的内容
                // 则完全替换而不是追加
                if (lastMessage.content === '思考中...' && streamContent.includes('<tool-usage-mark')) {
                  console.log('检测到从思考中状态收到工具结果，替换整个内容');
                  lastMessage.content = streamContent;
                } else {
                  // 正常更新内容
                  updateMessageContent(streamContent, lastMessage);
                }
                
                // 完成更新后检查内容是否仍然包含"思考中..."
                if (lastMessage.content.includes('思考中...') && 
                    (streamContent.includes('<tool-usage-mark') || 
                     streamContent.includes('[工具'))) {
                  // 如果既包含思考中又包含工具标记，删除思考中部分
                  lastMessage.content = lastMessage.content.replace('思考中...', '');
                  console.log('删除了消息中的思考中部分');
                }
              }
            }
          } catch (e) {
            console.error('解析SSE消息错误:', e, dataLine)
          }
        }
        
        return buffer.includes('data:') // 如果还有更多数据块，继续处理
      }
      
      // 处理缓冲区中所有完整数据块
      while (await processBuffer()) {}
    }

  } catch (error) {
    console.error('请求出错:', error)
    const lastMessage = messages.value.find(msg => msg.id === assistantMessageId)
    if (lastMessage) {
      // 如果是用户主动中断请求，不显示错误消息
      if (error.name === 'AbortError') {
        console.log('用户停止了消息生成')
        if (lastMessage.content.length <= 0) {
          lastMessage.content = '已停止生成'
        }
        return
      }
      
      // 详细显示错误信息
      let errorMsg = '';
      if (error.response?.data?.error?.message) {
        errorMsg = `错误：${error.response.data.error.message}`;
      } else if (error.message) {
        errorMsg = `错误：${error.message}`;
        
        // 对于HTTP错误，添加更多信息
        if (error.message.includes('HTTP error!')) {
          errorMsg += `\n\n请求URL: ${requestUrl}\n`;
          errorMsg += `使用的后台: ${useMcpBackend.value ? '智能后台' : '知识库后台'}\n`;
          errorMsg += '可能原因：\n1. 后台服务未启动\n2. API路径错误\n3. 后台服务内部错误';
          
          if (useMcpBackend.value) {
            errorMsg += '\n\n如果使用智能后台，请确保：\n- 已启动backend/app/minimal-mcp/custom_interactive_client_sse.py\n- LM Studio已运行并提供API服务';
          }
        }
      } else {
        errorMsg = '抱歉，发生了未知错误，请稍后重试。';
      }
      
      lastMessage.content = errorMsg;
    }
  } finally {
    isStreaming.value = false
    currentStreamingMessage.value = ''
    abortController.value = null // 清理请求控制器
    
    // 恢复原始后台设置
    useMcpBackend.value = originalBackendSetting
    
    await nextTick()
    scrollToBottom()
  }
}

// 停止消息生成
const stopMessageGeneration = () => {
  if (abortController.value && isStreaming.value) {
    abortController.value.abort()
    console.log('消息生成已停止')
  }
}

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
  }
}

// Markdown 渲染
const renderMarkdown = (content) => {
  // 如果内容为空，直接返回空字符串，避免显示标记
  if (!content || content.trim() === '') {
    return '';
  }

  // 添加调试日志
  console.log('渲染Markdown内容:', content);
  
  // 检查是否是思考中状态 - 关键修改：确保思考中状态不会与工具标记共存
  if ((content === '思考中...' || content === 'thinking...' || content === '处理中...') && 
      !content.includes('<tool-usage-mark') && !content.includes('[工具')) {
    console.log('检测到思考中状态，显示处理中占位符');
    return `<div class="tool-usage neutral">
      <span class="tool-icon neutral-icon">⏳</span>
      <span class="tool-name">智能工具</span>
      <span class="tool-status">处理中...</span>
    </div>
    <div>智能工具正在处理您的请求...</div>`;
  }
  
  // 如果包含自定义预览内容，特殊处理
  if (content.includes('<div class="file-preview">')) {
    // 移除工具标记，但保留其他HTML内容
    const cleanContent = content.replace(/<tool-usage-mark[^>]*><\/tool-usage-mark>/g, '');
    
    // 直接使用DOMPurify清理，但不经过Markdown转换
    return DOMPurify.sanitize(cleanContent, domPurifyConfig);
  }
  
  // 检查是否是摄像头请求
  const isCameraRequest = content.includes('打开摄像头') || content.includes('监控画面');
  if (isCameraRequest && content.includes('<tool-usage-mark')) {
    // 移除工具标记，添加摄像头工具状态
    const cleanContent = content.replace(/<tool-usage-mark[^>]*><\/tool-usage-mark>/g, '');
    return DOMPurify.sanitize(`<div class="tool-usage success">
      <span class="tool-icon success-icon">✓</span>
      <span class="tool-name">摄像头工具</span>
      <span class="tool-status">已打开摄像头</span>
    </div>` + cleanContent, domPurifyConfig);
  }

  // 检查是否包含AI最终消息的特殊标记
  let finalMessage = '';
  const finalMessageMatch = content.match(/<ai-final-message>\n?([\s\S]*?)\n?<\/ai-final-message>/);
  if (finalMessageMatch) {
    finalMessage = finalMessageMatch[1].trim();
    console.log('从特殊标记中提取的最终消息:', finalMessage);
    
    // 从原始内容中移除最终消息标记
    content = content.replace(/<ai-final-message>\n?([\s\S]*?)\n?<\/ai-final-message>/g, '').trim();
  }

  // 判断是否有工具调用结果标记
  const hasToolMark = content.includes('<tool-usage-mark') || content.includes('[工具');
  
  // 如果有工具标记，处理工具结果
  if (hasToolMark) {
    console.log('检测到工具标记，处理工具调用结果');
    
    // 检查是否有数学运算标记
    const isMathOperation = content.includes('data-math-operation="true"');
    console.log('是否是数学运算:', isMathOperation);
    
    // 提取所有工具调用结果
    console.log('开始提取工具调用结果');
    console.log('完整内容:', content);
    
    // 使用更简单直接的方式提取工具调用结果
    let allToolResults = [];
    
    // 1. 首先移除工具标记，保留实际内容
    let processedContent = content.replace(/<tool-usage-mark[^>]*><\/tool-usage-mark>/g, '').trim();
    
    // 2. 检查是否包含[工具标记
    if (processedContent.includes('[工具')) {
      console.log('检测到[工具]标记，开始提取');
      
      // 使用分割字符串的方式提取
      const parts = processedContent.split(/\[工具\s+/);
      console.log(`分割后得到 ${parts.length} 个部分`);
      
      // 提取可能存在的AI最终消息（在工具结果之外的文本）
      if (!finalMessage && parts[0].trim()) {
        finalMessage = parts[0].trim();
      }
      
      // 第一部分通常是前导内容，不包含工具调用
      if (parts.length > 1) {
        // 处理每个包含工具调用的部分
        for (let i = 1; i < parts.length; i++) {
          const part = parts[i];
          console.log(`处理分割部分 #${i}:`, part);
          
          // 提取工具名称和结果
          const colonIndex = part.indexOf(':');
          const colonZhIndex = part.indexOf('：');
          const effectiveColonIndex = colonIndex >= 0 ? colonIndex : colonZhIndex;
          
          if (effectiveColonIndex >= 0) {
            // 提取工具名称 - 可能包含]符号
            let toolName = part.substring(0, effectiveColonIndex).trim();
            // 移除可能的]符号
            toolName = toolName.replace(']', '').trim();
            
            // 提取工具结果 - 一直到下一个[工具或结束
            let toolResult = part.substring(effectiveColonIndex + 1).trim();
            // 如果结果包含下一个[工具，需要截断
            const nextToolIndex = toolResult.indexOf('[工具');
            if (nextToolIndex >= 0) {
              toolResult = toolResult.substring(0, nextToolIndex).trim();
            }
            
            console.log(`分割方法提取 - 工具名: "${toolName}", 结果: "${toolResult}"`);
            
            // 添加到结果集
            allToolResults.push({
              name: toolName,
              result: toolResult
            });
          }
        }
      }
      
      console.log(`总共提取了 ${allToolResults.length} 个工具结果`);
      
      if (allToolResults.length > 0) {
        console.log('检测到工具使用:', allToolResults);
        
        // 移除原始内容中的所有工具调用文本
        let remainingContent = processedContent.replace(/\[工具\s+([^:\]]+)(?:\]:?)?\s*(?::|：)\s*([\s\S]*?)(?=\n\n\[工具|\n\[工具|$)/g, '');
        
        // 如果内容以\n\n开头（由于我们去掉了工具调用），则去掉这些前导换行
        remainingContent = remainingContent.replace(/^\n\n+/, '');
        
        // 如果没有从特殊标记中提取到最终消息，尝试从文本中提取
        if (!finalMessage) {
          // 寻找工具之后的最终消息（可能在文本的末尾）
          // 查找最后一个工具标记之后的内容
          const lastToolIndex = processedContent.lastIndexOf('[工具');
          if (lastToolIndex !== -1) {
            // 找到最后一个工具标记之后的下一个双换行
            const nextDoubleNewlineIndex = processedContent.indexOf('\n\n', lastToolIndex);
            if (nextDoubleNewlineIndex !== -1) {
              finalMessage = processedContent.substring(nextDoubleNewlineIndex + 2).trim();
              console.log('从文本中提取到的最终消息:', finalMessage);
            }
          }
        }
        
        // 从所有工具结果中移除与最终消息相同的内容
        if (finalMessage) {
          console.log('尝试从工具结果中移除最终消息内容');
          allToolResults.forEach(toolResult => {
            if (toolResult.result.includes(finalMessage)) {
              console.log(`从工具 ${toolResult.name} 的结果中移除最终消息`);
              toolResult.result = toolResult.result.replace(finalMessage, '').trim();
            }
          });
        }
        
        // 根据工具类型对结果进行分组
        const toolsByType = {};
        
        allToolResults.forEach(tool => {
          if (!toolsByType[tool.name]) {
            toolsByType[tool.name] = [];
          }
          toolsByType[tool.name].push(tool.result);
        });
        
        console.log('分组后的工具结果:', toolsByType);
        
        // 为每个工具类型创建单独的UI组件
        let toolResultsHtml = '';
        
        // 处理任何类型的工具调用 - 简化逻辑，保持通用性
        for (const toolName in toolsByType) {
          const toolDisplayName = getToolDisplayName(toolName);
          const toolTypeClass = getToolTypeClass(toolName);
          const toolIcon = getToolIcon(toolName);
          
          // 创建通用工具框架
          toolResultsHtml += `
            <div class="tool-usage ${toolTypeClass}">
              <div class="tool-header">
                <span class="tool-icon">${toolIcon}</span>
                <span class="tool-name">${toolDisplayName}</span>
              </div>
              <div class="tool-content">
          `;
          
          // 判断是否是数学类工具，使用特殊处理
          if (['add', 'multiply', 'subtract', 'divide'].includes(toolName.toLowerCase())) {
            toolResultsHtml += formatMathResult(toolName, toolsByType[toolName]);
          } else {
            // 为其他工具类型的每个结果添加结果显示
            toolsByType[toolName].forEach(result => {
              // 确保结果不为空
              if (result && result.trim()) {
                toolResultsHtml += `<div class="tool-result">${result}</div>`;
              }
            });
          }
          
          toolResultsHtml += `
              </div>
            </div>
          `;
        }
        
        // 处理AI的最终消息，如果有的话
        let finalMessageHtml = '';
        if (finalMessage) {
          finalMessageHtml = `<div class="ai-final-message">${marked(finalMessage)}</div>`;
        }
        
        // 将工具结果HTML和最终消息HTML合并
        const finalContent = toolResultsHtml + finalMessageHtml;
        
        // 直接返回处理后的HTML
        return DOMPurify.sanitize(finalContent, domPurifyConfig);
      }
    }
  } else if (finalMessage) {
    // 如果只有最终消息，没有工具标记，直接渲染最终消息
    return DOMPurify.sanitize(`<div class="ai-final-message">${marked(finalMessage)}</div>`, domPurifyConfig);
  }
  
  // 清理特殊标记
  let cleanContent = content.replace(/##[a-z0-9]+\$\$/g, '');
  
  // 移除工具标记
  cleanContent = cleanContent.replace(/<tool-usage-mark[^>]*><\/tool-usage-mark>/g, '').trim();
  
  return DOMPurify.sanitize(marked(cleanContent), domPurifyConfig);
}

// 重置聊天
const resetChat = async () => {
  try {
    // 停止所有正在进行的打字机效果
    messages.value.forEach(msg => {
      if (msg.typingTimer) {
        clearTimeout(msg.typingTimer);
        msg.typingTimer = null;
      }
    });
    
    // 重置前端状态
    const welcomeMessage = {
      id: generateMessageId(),
      role: 'assistant',
      content: ''
    };
    messages.value = [welcomeMessage];
    userInput.value = '';
    
    // 获取当前使用的后台名称
    const backendName = useMcpBackend.value ? '智能后台' : '知识库后台';
    
    // 使用打字机效果显示欢迎消息，但设置立即显示为true，跳过动画
    typewriterEffect(`您好，我是金小海——油气生产智能助手。当前使用${backendName}，请告诉我您的需求或问题，我会竭尽所能为您提供高效、专业的支持与帮助！`, welcomeMessage, 30, null, 0.4, true);
    
    // 重置后端状态 - 发送一个空的系统消息来重置对话
    const headers = {
      'Content-Type': 'application/json'
    }
    
    // 只有使用RAGFlow服务时才添加认证令牌
    if (!useMcpBackend.value) {
      headers['Authorization'] = `Bearer ${apiKey}`
    }
    
    // 确定请求URL
    const requestUrl = useMcpBackend.value 
      ? `/api/v1/chats_openai/${mcpChatId}/chat/completions`  // 本地MCP后台
      : `/ragflow/api/v1/chats_openai/${chatId}/chat/completions`  // 改为新的RAGFlow代理路径
    
    await fetch(requestUrl, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({
        model: useMcpBackend.value ? 'qwen2.5-7b-instruct-1m' : 'qwen2.5:32b',
        messages: [{
          role: 'system',
          content: '对话已重置。'
        }],
        stream: false
      })
    })
  } catch (error) {
    console.error('重置对话时出错:', error)
  }
}

// 切换后端服务
const toggleBackend = () => {
  useMcpBackend.value = !useMcpBackend.value;
  console.log(`切换到${useMcpBackend.value ? '智能后台' : '知识库后台'}`);
  
  // 重置聊天，以便显示当前后台信息
  resetChat();
}

// 监听显示文件的自定义事件
const handleOpenPdfFile = (event) => {
  if (event && event.detail) {
    // 使用现有的FileViewer组件打开PDF
    emitter.emit('open-file-view', {
      filePath: event.detail.filePath,
      title: event.detail.title || '文件查看',
      position: { x: 20, y: 10 } // 默认位置
    });
  }
}

// 添加点击查看大图的处理函数
const handleOpenImageView = (event) => {
  if (event && event.detail) {
    // 判断是否为功图预览
    const isGongtubao = event.detail.imagePath && 
                        (event.detail.imagePath.includes('/功图/') || 
                         event.detail.title && event.detail.title.includes('功图'));
    
    // 使用现有的FileViewer组件打开图片
    emitter.emit('open-file-view', {
      filePath: event.detail.imagePath,
      imagePath: event.detail.imagePath, // 传递图片路径供图片浏览模式使用
      title: event.detail.title || '图片查看',
      position: { x: 20, y: 10 } // 默认位置
    });
  }
}

onMounted(() => {
  // 可以在这里添加初始化逻辑
  // 添加一条欢迎消息
  const welcomeMessage = {
    id: generateMessageId(),
    role: 'assistant',
    content: ''
  };
  messages.value.push(welcomeMessage);
  
  // 重置聊天以显示当前后台信息
  resetChat();
  
  // 处理可能的页面快速加载和交互情况，立即显示完整欢迎消息
  // typewriterEffect('您好，我是金小海——油气生产智能助手。请告诉我您的需求或问题，我会竭尽所能为您提供高效、专业的支持与帮助！', welcomeMessage, 30, null, 0.4, true);
  
  // 添加PDF文件打开事件监听
  window.addEventListener('open-pdf-file', handleOpenPdfFile);
  
  // 添加图片查看事件监听
  window.addEventListener('open-image-view', handleOpenImageView);
  
  // 设置全局点击事件监听
  setupGlobalClickListeners();

  // 初始化TTS服务
  initializeTTS();

  // 初始化处理消息内容点击事件
  nextTick(() => {
    // 找到所有消息内容元素
    const messageContents = document.querySelectorAll('.message-content > div');
    messageContents.forEach(el => {
      handleContentClicks(el);
    });
  });
})

// 监听DOM更新，处理新添加的消息内容的点击事件
onUpdated(() => {
  nextTick(() => {
    // 找到所有消息内容元素
    const messageContents = document.querySelectorAll('.message-content > div');
    messageContents.forEach(el => {
      // 如果元素上还没有click事件监听器，添加一个
      if (!el._hasClickListener) {
        handleContentClicks(el);
        el._hasClickListener = true;
      }
    });
  });
})

// 组件卸载时清理
onUnmounted(() => {
  // 确保重新启用键盘控制
  if (isExpanded.value) {
    sceneRef.value?.setKeyboardControlsEnabled(true)
  }

  // 停止当前播放的音频
  stopCurrentAudio();

  // 移除PDF文件打开事件监听
  window.removeEventListener('open-pdf-file', handleOpenPdfFile);

  // 移除图片查看事件监听
  window.removeEventListener('open-image-view', handleOpenImageView);
})

// TTS相关方法
async function initializeTTS() {
  try {
    console.log('正在初始化TTS服务...');
    const response = await fetch(`${ttsServiceUrl.value}/health`);
    if (response.ok) {
      const data = await response.json();
      isTTSReady.value = data.status === 'healthy' && data.model_loaded;
      console.log('TTS服务状态:', data);
    } else {
      console.warn('TTS服务连接失败:', response.status);
      isTTSReady.value = false;
    }
  } catch (error) {
    console.warn('TTS服务不可用:', error);
    isTTSReady.value = false;
  }
}

function toggleTTS() {
  isTTSEnabled.value = !isTTSEnabled.value;
  if (!isTTSEnabled.value) {
    stopCurrentAudio();
    // 如果禁用TTS，也禁用自动播放
    if (isAutoPlayEnabled.value) {
      isAutoPlayEnabled.value = false;
      clearAudioQueue();
    }
  }
  console.log(`TTS功能${isTTSEnabled.value ? '已启用' : '已禁用'}`);
}

function toggleAutoPlay() {
  if (!isTTSEnabled.value || !isTTSReady.value) {
    console.warn('TTS服务未就绪，无法启用自动播放');
    return;
  }

  isAutoPlayEnabled.value = !isAutoPlayEnabled.value;

  if (!isAutoPlayEnabled.value) {
    // 禁用自动播放时，清空队列并停止当前播放
    clearAudioQueue();
    if (isPlayingAudio.value) {
      stopCurrentAudio();
    }
  }

  console.log(`TTS自动播放${isAutoPlayEnabled.value ? '已启用' : '已禁用'}`);
}

function stopCurrentAudio() {
  if (currentAudio.value) {
    currentAudio.value.pause();
    currentAudio.value = null;
  }
  isPlayingAudio.value = false;
  currentPlayingMessage.value = null;
  autoPlayIndicator.value = false;
}

// 清空音频播放队列
function clearAudioQueue() {
  audioQueue.value = [];
  isProcessingQueue.value = false;
  autoPlayIndicator.value = false;
  console.log('音频播放队列已清空');
}

// 添加消息到自动播放队列
function addToAutoPlayQueue(message) {
  console.log('addToAutoPlayQueue: 开始处理自动播放请求');
  console.log('addToAutoPlayQueue: isAutoPlayEnabled =', isAutoPlayEnabled.value);
  console.log('addToAutoPlayQueue: isTTSReady =', isTTSReady.value);

  if (!isAutoPlayEnabled.value || !isTTSReady.value) {
    console.log('addToAutoPlayQueue: 自动播放未启用或TTS未就绪，跳过');
    return;
  }

  // 检查消息是否适合自动播放
  console.log('addToAutoPlayQueue: 检查消息是否适合自动播放');
  if (!shouldAutoPlay(message)) {
    console.log('addToAutoPlayQueue: 消息不适合自动播放，跳过');
    return;
  }

  console.log(`addToAutoPlayQueue: 添加消息到自动播放队列: ${message.id}`);
  console.log('addToAutoPlayQueue: 当前队列长度:', audioQueue.value.length);
  audioQueue.value.push(message);
  console.log('addToAutoPlayQueue: 添加后队列长度:', audioQueue.value.length);

  // 如果当前没有在处理队列，开始处理
  if (!isProcessingQueue.value) {
    console.log('addToAutoPlayQueue: 开始处理音频队列');
    processAudioQueue();
  } else {
    console.log('addToAutoPlayQueue: 队列正在处理中，等待');
  }
}

// 判断消息是否应该自动播放
function shouldAutoPlay(message) {
  if (!message || message.role !== 'assistant') {
    console.log('shouldAutoPlay: 消息角色不是assistant，跳过自动播放');
    return false;
  }

  const content = message.content;
  if (!content || content.trim() === '') {
    console.log('shouldAutoPlay: 消息内容为空，跳过自动播放');
    return false;
  }

  // 跳过思考中状态
  if (content === '思考中...' || content === 'thinking...' || content === '处理中...') {
    console.log('shouldAutoPlay: 检测到思考中状态，跳过自动播放');
    return false;
  }

  // 提取纯文本内容进行判断
  const textContent = extractTextFromHTML(content);
  console.log('shouldAutoPlay: 提取的文本内容:', textContent);
  console.log('shouldAutoPlay: 文本长度:', textContent ? textContent.length : 0);

  // 跳过只包含工具调用结果的消息（没有实际文本内容）
  if (!textContent || textContent.length < 5) {
    console.log('shouldAutoPlay: 文本内容过短或为空，跳过自动播放');
    return false;
  }

  console.log('shouldAutoPlay: 消息符合自动播放条件');
  return true;
}

// 处理音频播放队列
async function processAudioQueue() {
  console.log('processAudioQueue: 开始处理音频队列');
  console.log('processAudioQueue: isProcessingQueue =', isProcessingQueue.value);
  console.log('processAudioQueue: 队列长度 =', audioQueue.value.length);

  if (isProcessingQueue.value || audioQueue.value.length === 0) {
    console.log('processAudioQueue: 队列正在处理中或队列为空，退出');
    return;
  }

  isProcessingQueue.value = true;
  autoPlayIndicator.value = true;

  console.log(`processAudioQueue: 开始处理音频队列，队列长度: ${audioQueue.value.length}`);

  while (audioQueue.value.length > 0 && isAutoPlayEnabled.value) {
    const message = audioQueue.value.shift();
    console.log(`processAudioQueue: 处理队列中的消息: ${message.id}`);

    try {
      console.log(`processAudioQueue: 开始自动播放消息: ${message.id}`);
      await playMessageAudioInternal(message, true); // true表示自动播放

      console.log(`processAudioQueue: 等待音频播放完成: ${message.id}`);
      // 等待当前音频播放完成
      await waitForAudioComplete();
      console.log(`processAudioQueue: 音频播放完成: ${message.id}`);

    } catch (error) {
      console.error('processAudioQueue: 自动播放出错:', error);
      // 继续处理下一个消息
    }
  }

  isProcessingQueue.value = false;
  autoPlayIndicator.value = false;
  console.log('processAudioQueue: 音频队列处理完成');
}

// 等待音频播放完成
function waitForAudioComplete() {
  return new Promise((resolve) => {
    if (!currentAudio.value || currentAudio.value.ended) {
      resolve();
      return;
    }

    const checkComplete = () => {
      if (!currentAudio.value || currentAudio.value.ended || !isPlayingAudio.value) {
        resolve();
      } else {
        setTimeout(checkComplete, 100);
      }
    };

    checkComplete();
  });
}

function extractTextFromHTML(html) {
  if (!html) return '';

  // 创建临时DOM元素来提取纯文本
  const temp = document.createElement('div');
  temp.innerHTML = html;

  // 移除工具使用标记和其他不需要的元素
  const toolElements = temp.querySelectorAll('.tool-usage, .tool-result, .tool-header');
  toolElements.forEach(el => el.remove());

  // 获取纯文本内容
  let textContent = temp.textContent || temp.innerText || '';

  // 清理文本：移除多余的空白字符和特殊标记
  textContent = textContent
    .replace(/\s+/g, ' ') // 将多个空白字符替换为单个空格
    .replace(/^\s+|\s+$/g, '') // 移除首尾空白
    .replace(/思考中\.\.\./g, '') // 移除思考中标记
    .replace(/处理中\.\.\./g, '') // 移除处理中标记
    .trim();

  // 记录提取的文本内容，用于调试TTS处理
  console.log('提取的TTS文本内容:', textContent);

  // 检查是否包含A2等字母数字组合
  const letterNumberMatches = textContent.match(/[A-Z]\d+/g);
  if (letterNumberMatches) {
    console.log('检测到字母数字组合:', letterNumberMatches);
    console.log('这些组合将通过TTS预处理转换为中文朗读');
  }

  return textContent;
}

// 手动播放消息音频（用户点击播放按钮）
async function playMessageAudio(message) {
  if (!isTTSEnabled.value || !isTTSReady.value) {
    return;
  }

  // 如果正在播放同一条消息，则停止播放
  if (currentPlayingMessage.value === message.id && isPlayingAudio.value) {
    stopCurrentAudio();
    return;
  }

  // 手动播放时，清空自动播放队列并停止自动播放
  if (isAutoPlayEnabled.value) {
    clearAudioQueue();
  }

  await playMessageAudioInternal(message, false);
}

// 内部播放函数，支持自动播放和手动播放
async function playMessageAudioInternal(message, isAutoPlay = false) {
  const playType = isAutoPlay ? '自动播放' : '手动播放';
  console.log(`playMessageAudioInternal: 开始${playType}处理`);
  console.log('playMessageAudioInternal: isTTSEnabled =', isTTSEnabled.value);
  console.log('playMessageAudioInternal: isTTSReady =', isTTSReady.value);

  if (!isTTSEnabled.value || !isTTSReady.value) {
    console.log(`playMessageAudioInternal: TTS未启用或未就绪，跳过${playType}`);
    return;
  }

  // 如果当前正在播放其他音频，停止它
  if (isPlayingAudio.value && currentPlayingMessage.value !== message.id) {
    console.log('playMessageAudioInternal: 停止当前播放的音频');
    stopCurrentAudio();
  }

  try {
    // 提取纯文本内容
    console.log('playMessageAudioInternal: 提取纯文本内容');
    const textContent = extractTextFromHTML(message.content);
    console.log('playMessageAudioInternal: 提取的文本内容:', textContent);

    if (!textContent || textContent.length < 2) {
      console.log('playMessageAudioInternal: 消息内容为空或过短，跳过TTS播放');
      return;
    }

    console.log(`playMessageAudioInternal: 开始${playType}TTS:`, textContent.substring(0, 50) + '...');
    console.log('playMessageAudioInternal: 使用TTS配置:', TTS_CONFIG);

    console.log('playMessageAudioInternal: 设置播放状态');
    isPlayingAudio.value = true;
    currentPlayingMessage.value = message.id;

    if (isAutoPlay) {
      autoPlayIndicator.value = true;
      console.log('playMessageAudioInternal: 设置自动播放指示器');
    }

    // 调用TTS API - 使用固定音色配置确保一致性
    console.log('playMessageAudioInternal: 准备调用TTS API');
    console.log('playMessageAudioInternal: TTS服务URL:', ttsServiceUrl.value);

    const requestBody = {
      text: textContent,
      ...TTS_CONFIG  // 使用固定的TTS配置，确保音色和参数完全一致
    };
    console.log('playMessageAudioInternal: TTS请求体:', requestBody);

    const response = await fetch(`${ttsServiceUrl.value}/tts`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    console.log('playMessageAudioInternal: TTS响应状态:', response.status);

    if (!response.ok) {
      throw new Error(`TTS请求失败: ${response.status}`);
    }

    const result = await response.json();
    console.log('playMessageAudioInternal: TTS响应结果:', result);

    if (!result.success) {
      throw new Error(result.message);
    }

    // 创建音频对象并播放
    const audioUrl = `data:audio/wav;base64,${result.audio_base64}`;
    currentAudio.value = new Audio(audioUrl);

    // 设置音频事件监听器
    currentAudio.value.addEventListener('ended', () => {
      isPlayingAudio.value = false;
      currentPlayingMessage.value = null;
      currentAudio.value = null;
      if (isAutoPlay) {
        autoPlayIndicator.value = false;
      }
    });

    currentAudio.value.addEventListener('error', (e) => {
      console.error('音频播放错误:', e);
      isPlayingAudio.value = false;
      currentPlayingMessage.value = null;
      currentAudio.value = null;
      if (isAutoPlay) {
        autoPlayIndicator.value = false;
      }
    });

    // 开始播放
    await currentAudio.value.play();
    console.log(`${playType}TTS播放开始，预计时长: ${result.duration?.toFixed(2)}秒`);
    console.log(`使用固定音色配置 - 音色: ${TTS_CONFIG.voice_profile}, 种子: ${TTS_CONFIG.voice_seed}`);

  } catch (error) {
    console.error(`${isAutoPlay ? '自动' : '手动'}TTS播放失败:`, error);
    isPlayingAudio.value = false;
    currentPlayingMessage.value = null;
    currentAudio.value = null;
    if (isAutoPlay) {
      autoPlayIndicator.value = false;
    }
  }
}

// 发送设备定位事件
function sendDeviceLocateEvent(deviceName) {
  // 确保设备ID格式化
  let formattedDeviceId = deviceName;
  
  // 如果设备ID以H开头但不是HN开头，转换为JH格式
  if (deviceName && deviceName.match(/^H\d+$/) && !deviceName.startsWith('HN')) {
    formattedDeviceId = 'JH' + deviceName.substring(1);
    console.log(`设备ID格式转换: ${deviceName} -> ${formattedDeviceId}`);
  }
  
  console.log(`准备发送设备定位事件: ${formattedDeviceId}`);
  
  // 创建设备定位事件
  const locateEvent = new CustomEvent('device-locate', {
    detail: {
      deviceId: formattedDeviceId,
      message: `发现在查询${formattedDeviceId}的信息，正在移动到指定位置打开摄像头`,
      timestamp: new Date().toISOString()
    }
  });
  
  // 触发事件
  console.log('发送设备定位事件:', locateEvent.detail);
  window.dispatchEvent(locateEvent);
}

// 发送常用问题
const sendCommonQuestion = (question) => {
  if (isStreaming.value) return // 如果正在生成回答，则不处理点击

  // 每次开始新的问题时重置设备面板状态
  devicePanelOpened.value = false
  
  // 设置用户输入
  userInput.value = question
  
  // 检查是否是需要自动切换到知识库后台的特定问题
  const knowledgeBaseQuestions = [
    '知识库中站场巡检工作有哪些',
    '知识库中采油作业三区的组织机构情况',
    '知识库中井口刺漏发生井喷如何处理'
  ]
  
  // 保存当前后台设置，以便在发送特定问题后恢复
  const originalBackendSetting = useMcpBackend.value
  
  // 如果是知识库问题，临时切换到知识库后台（useMcpBackend = false）
  if (knowledgeBaseQuestions.includes(question)) {
    console.log(`检测到知识库专属问题: ${question}，临时切换到知识库后台`)
    useMcpBackend.value = false
  }
  // 如果是强制使用智能后台的问题，临时切换到智能后台
  else if (mcpBackendQuestions.includes(question)) {
    console.log(`检测到智能后台专属问题: ${question}，确保使用智能后台`)
    useMcpBackend.value = true
  }
  
  // 检查是否是打开摄像头的请求
  if (question.includes('打开') && question.includes('摄像头')) {
    // 从问题中提取设备ID
    const deviceIdMatch = question.match(/(JH\d+|H\d+)/i);
    const deviceId = deviceIdMatch ? deviceIdMatch[0] : 'JH005'; // 默认JH005
    
    // 触发设备定位事件，但不打开设备面板
    console.log(`检测到摄像头请求，将触发设备${deviceId}的定位并高亮`);
    
    // 添加用户消息
    messages.value.push({
      id: generateMessageId(),
      role: 'user',
      content: question
    });

    // 添加AI响应消息
    const assistantMessageId = generateMessageId();
    messages.value.push({
      id: assistantMessageId,
      role: 'assistant',
      content: ''  // 初始为空
    });
    
    // 准备好打开视频面板的回调函数
    const openVideoCallback = () => {
      // 触发打开视频面板的事件
      console.log(`打开设备${deviceId}的视频面板`);
      emitter.emit('open-video-panel', {
        deviceId: deviceId,
        position: { x: window.innerWidth - 380, y: 20 } // 默认位置在右上角
      });
      
      // 标记设备面板已打开
      devicePanelOpened.value = true;
    };
    
    // 使用打字机效果
    const responseMessage = messages.value.find(msg => msg.id === assistantMessageId);
    typewriterEffect(`<tool-usage-mark></tool-usage-mark>已为您打开二号站17-5井组的摄像头进行实时监控，您可以在视频面板中查看监控画面。`, responseMessage, 40, openVideoCallback, 0.6);
    
    // 清空输入框
    userInput.value = '';
    
    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
    
    // 恢复原始后台设置
    useMcpBackend.value = originalBackendSetting;
    
    return; // 提前返回，不再调用sendMessage
  }
  // 检查是否是查询实时数据的请求
  else if ((question.includes('实时数据') || question.includes('H005') || question.includes('JH005') || question.includes('海南19-13C')) && !devicePanelOpened.value) {
    // 从问题中提取设备ID，优先匹配"海南xx-xx"格式
    const hainanMatch = question.match(/(海南\d+-\d+)/i);
    const deviceIdMatch = hainanMatch || question.match(/(JH\d+|H\d+)/i);
    const deviceId = deviceIdMatch ? deviceIdMatch[0] : 'JH005'; // 默认JH005
    
    console.log(`提取的设备ID: ${deviceId}`);
    
    // 仅触发打开设备面板事件
    console.log(`仅打开设备${deviceId}的设备面板`);
    emitter.emit('open-device-panel', {
      deviceId: deviceId
    });
    
    // 标记设备面板已经打开
    devicePanelOpened.value = true;
  }
  else if (question.includes('A2日报')) {
    // 添加用户消息
    messages.value.push({
      id: generateMessageId(),
      role: 'user',
      content: question
    });

    // 添加AI响应消息
    const assistantMessageId = generateMessageId();
    messages.value.push({
      id: assistantMessageId,
      role: 'assistant',
      content: ''  // 初始为空
    });
    
    const thumbnailPath = '/Files/file/1.金小海，调出作业区目前生产油井的A2日报/缩略图.png';
    const filePath = '/Files/file/1.金小海，调出作业区目前生产油井的A2日报/采油井生产日报-1.pdf';
    
    // 准备好打开文件的回调函数
    const openFileCallback = () => {
      // 不再直接打开PDF，而是在消息中显示缩略图和链接
      const responseMessage = messages.value.find(msg => msg.id === assistantMessageId);
      if (responseMessage) {
        responseMessage.content = `<tool-usage-mark></tool-usage-mark>
这是作业区目前生产油井的A2日报信息：

<div class="file-preview">
  <img src="${thumbnailPath}" alt="A2日报缩略图" class="file-thumbnail" style="cursor: pointer;" />
  <a href="javascript:void(0);" class="file-link">
    点击查看完整的作业区生产油井A2日报
  </a>
</div>`;
      }
    };
    
    // 使用打字机效果创建初始状态
    const responseMessage = messages.value.find(msg => msg.id === assistantMessageId);
    typewriterEffect(`<tool-usage-mark></tool-usage-mark>正在为您获取作业区目前生产油井的A2日报信息...`, responseMessage, 20, openFileCallback, 1);
    
    // 清空输入框
    userInput.value = '';
    
    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
    
    // 恢复原始后台设置
    useMcpBackend.value = originalBackendSetting;
    
    return; // 提前返回，不再调用sendMessage
  }
  else if (question.includes('功图')) {
    // 添加用户消息
    messages.value.push({
      id: generateMessageId(),
      role: 'user',
      content: question
    });

    // 添加AI响应消息
    const assistantMessageId = generateMessageId();
    messages.value.push({
      id: assistantMessageId,
      role: 'assistant',
      content: ''  // 初始为空
    });
    
    const filePath = '/Files/file/2.金小海，调出作业区目海南19-13井的最近的10幅功图/2.金小海，调出作业区目海南19-13井的最近的10幅功图.pdf';
    const imageBasePath = '/Files/file/2.金小海，调出作业区目海南19-13井的最近的10幅功图/';
    
    // 准备好打开文件的回调函数
    const openFileCallback = () => {
      // 显示10张图片和链接
      let imagesHtml = '';
      for (let i = 1; i <= 10; i++) {
        imagesHtml += `<img src="${imageBasePath}${i}.jpg" alt="功图${i}" class="gongtubao-image" style="cursor: pointer;" />`;
      }
      
      const responseMessage = messages.value.find(msg => msg.id === assistantMessageId);
      if (responseMessage) {
        responseMessage.content = `<tool-usage-mark></tool-usage-mark>
这是作业区10口井的功图：

<div class="file-preview">
  <div class="gongtubao-images">
    ${imagesHtml}
  </div>
  <a href="javascript:void(0);" class="file-link">
    点击查看完整的功图报告
  </a>
</div>`;
      }
    };
    
    // 使用打字机效果
    const responseMessage = messages.value.find(msg => msg.id === assistantMessageId);
    typewriterEffect(`<tool-usage-mark></tool-usage-mark>正在为您获取作业区的10口井的功图...`, responseMessage, 20, openFileCallback, 1);
    
    // 清空输入框
    userInput.value = '';
    
    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
    
    // 恢复原始后台设置
    useMcpBackend.value = originalBackendSetting;
    
    return; // 提前返回，不再调用sendMessage
  }
  else if (question.includes('中控班管理手册')) {
    // 添加用户消息
    messages.value.push({
      id: generateMessageId(),
      role: 'user',
      content: question
    });

    // 添加AI响应消息
    const assistantMessageId = generateMessageId();
    messages.value.push({
      id: assistantMessageId,
      role: 'assistant',
      content: ''  // 初始为空
    });
    
    const thumbnailPath = '/Files/file/3.金小海，展示中控班管理手册/缩略图.png';
    const filePath = '/Files/file/3.金小海，展示中控班管理手册/金海采油厂采油作业三区 中控班管理手册【全版】11111.pdf';
    
    // 准备好打开文件的回调函数
    const openFileCallback = () => {
      // 不再直接打开PDF，而是在消息中显示缩略图和链接
      const responseMessage = messages.value.find(msg => msg.id === assistantMessageId);
      if (responseMessage) {
        responseMessage.content = `<tool-usage-mark></tool-usage-mark>
这是中控班管理手册：

<div class="file-preview">
  <img src="${thumbnailPath}" alt="中控班管理手册缩略图" class="file-thumbnail" style="cursor: pointer;" />
  <a href="javascript:void(0);" class="file-link">
    点击查看完整的中控班管理手册
  </a>
</div>`;
      }
    };
    
    // 使用打字机效果
    const responseMessage = messages.value.find(msg => msg.id === assistantMessageId);
    typewriterEffect(`<tool-usage-mark></tool-usage-mark>正在为您获取中控班管理手册...`, responseMessage, 20, openFileCallback, 1);
    
    // 清空输入框
    userInput.value = '';
    
    // 滚动到底部
    nextTick(() => {
      scrollToBottom();
    });
    
    // 恢复原始后台设置
    useMcpBackend.value = originalBackendSetting;
    
    return; // 提前返回，不再调用sendMessage
  }
  
  // 调用发送消息函数
  sendMessage()
  
  // 消息发送后恢复原始后台设置
  setTimeout(() => {
    useMcpBackend.value = originalBackendSetting
  }, 100)
}

//为PDF图片/链接设置全局事件监听
function setupGlobalClickListeners() {
  // 为A2日报图片和链接添加点击事件监听
  document.addEventListener('click', function(e) {
    console.log('点击事件触发:', e.target.tagName, e.target.className);
    
    // 使用事件委托处理文件/图片的点击
    let target = e.target;
    
    // 处理点击图片元素
    if (target.classList.contains('file-thumbnail') || 
        target.closest('.file-thumbnail') || 
        target.classList.contains('gongtubao-image')) {
      
      // 获取图片路径
      const imagePath = target.src;
      const title = target.alt || '图片查看';
      
      if (imagePath) {
        console.log('点击图片，打开大图:', imagePath);
        window.dispatchEvent(new CustomEvent('open-image-view', {
          detail: { imagePath, title }
        }));
        e.preventDefault();
        e.stopPropagation();
      }
    }
    
    // 处理点击链接元素
    if (target.classList.contains('file-link') || target.closest('.file-link')) {
      // 如果点击的是file-link内部的元素，获取父元素
      const linkElement = target.classList.contains('file-link') ? target : target.closest('.file-link');
      console.log('点击链接元素:', linkElement.textContent.trim());
      
      // 从链接文本推断PDF文件路径
      let filePath = '';
      let title = '';
      
      const linkText = linkElement.textContent.trim();
      
      if (linkText.includes('A2日报')) {
        filePath = '/Files/file/1.金小海，调出作业区目前生产油井的A2日报/采油井生产日报-1.pdf';
        title = '作业区生产油井A2日报';
      } else if (linkText.includes('功图')) {
        filePath = '/Files/file/2.金小海，调出作业区目海南19-13井的最近的10幅功图/2.金小海，调出作业区目海南19-13井的最近的10幅功图.pdf';
        title = '海南19-13井最近10幅功图';
      } else if (linkText.includes('管理手册')) {
        filePath = '/Files/file/3.金小海，展示中控班管理手册/金海采油厂采油作业三区 中控班管理手册【全版】11111.pdf';
        title = '中控班管理手册';
      }
      
      if (filePath) {
        console.log('点击链接，打开PDF:', filePath);
        window.dispatchEvent(new CustomEvent('open-pdf-file', {
          detail: { filePath, title }
        }));
        e.preventDefault();
        e.stopPropagation();
      }
    }
  });
}

// 辅助函数：获取工具类型的CSS类
function getToolTypeClass(toolName) {
  // 统一转为小写进行匹配
  const lowerToolName = toolName.toLowerCase();
  
  if (lowerToolName === 'get_weather' || lowerToolName.includes('weather')) {
    return 'weather';
  } else if (['add', 'multiply', 'subtract', 'divide'].includes(lowerToolName)) {
    return 'math';
  } else if (lowerToolName.includes('redmine')) {
    return 'redmine';
  } else if (lowerToolName.includes('search')) {
    return 'search';
  } else if (lowerToolName.includes('file') || lowerToolName.includes('read')) {
    return 'file';
  } else if (lowerToolName.includes('memory') || lowerToolName.includes('store')) {
    return 'memory';
  } else {
    return '';
  }
}

// 辅助函数：获取工具的友好显示名称
function getToolDisplayName(toolName) {
  // 统一转为小写进行匹配
  const lowerToolName = toolName.toLowerCase();
  
  const toolMap = {
    'get_weather': '天气查询工具',
    'add': '加法计算工具',
    'multiply': '乘法计算工具',
    'subtract': '减法计算工具',
    'divide': '除法计算工具',
    'search_redmine': 'Redmine搜索工具',
    'get_redmine_issues': 'Redmine问题查询工具',
    'get_redmine_projects': 'Redmine项目查询工具',
    'get_redmine_users': 'Redmine用户查询工具'
  };
  
  // 先尝试精确匹配
  if (toolMap[toolName]) {
    return toolMap[toolName];
  } 
  
  // 然后尝试通过包含关系进行模糊匹配
  if (lowerToolName.includes('weather')) {
    return '天气查询工具';
  } else if (lowerToolName.includes('redmine')) {
    return 'Redmine工具';
  } else if (lowerToolName.includes('search')) {
    return '搜索工具';
  } else if (lowerToolName.includes('file') || lowerToolName.includes('read')) {
    return '文件工具';
  } else if (lowerToolName.includes('math')) {
    return '数学计算工具';
  } else {
    // 将工具名称格式化为更友好的显示名称，将下划线替换为空格，首字母大写
    return toolName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()) + '工具';
  }
}

// 辅助函数：获取工具图标
function getToolIcon(toolName) {
  // 统一转为小写进行匹配
  const lowerToolName = toolName.toLowerCase();
  
  const iconMap = {
    'get_weather': '🌤️',
    'add': '➕',
    'multiply': '✖️',
    'subtract': '➖',
    'divide': '➗',
    'search_redmine': '🔍',
    'get_redmine_issues': '📋',
    'get_redmine_projects': '📂',
    'get_redmine_users': '👥'
  };
  
  // 先尝试精确匹配
  if (iconMap[toolName]) {
    return iconMap[toolName];
  }
  
  // 然后尝试通过包含关系进行模糊匹配
  if (lowerToolName.includes('weather')) {
    return '🌤️';
  } else if (lowerToolName.includes('redmine')) {
    return '📋';
  } else if (lowerToolName.includes('search')) {
    return '🔍';
  } else if (lowerToolName.includes('file') || lowerToolName.includes('read')) {
    return '📄';
  } else if (lowerToolName.includes('math')) {
    return '🔢';
  } else {
    return '🔧';
  }
}

// 辅助函数：处理数学工具的特殊显示
function formatMathResult(toolName, results) {
  // 如果没有结果，返回空字符串
  if (!results || results.length === 0) {
    return '';
  }
  
  // 判断是计算结果，还是组合计算
  // 如果只有一个计算结果，简单显示
  if (results.length === 1) {
    return `<div class="tool-result">${results[0]}</div>`;
  }
  
  // 工具名称到运算符的映射
  const operatorMap = {
    'add': '+',
    'multiply': '×',
    'subtract': '-',
    'divide': '÷'
  };
  
  // 获取当前运算符
  const operator = operatorMap[toolName] || '?';
  
  let resultHtml = '';
  
  // 提取操作数和结果，假设都是数字
  const numberRegex = /^\d+(\.\d+)?$/;
  let operands = [];
  let finalResult = '';
  
  // 假设最后一个结果是最终结果
  if (results.length >= 2) {
    // 提取数字操作数
    for (let i = 0; i < results.length; i++) {
      const result = results[i];
      if (numberRegex.test(result)) {
        if (i === results.length - 1) {
          finalResult = result;
        } else {
          operands.push(result);
        }
      }
    }
    
    // 如果没有找到操作数或结果，使用原始结果
    if (!finalResult && results.length > 0) {
      finalResult = results[results.length - 1];
    }
    if (operands.length === 0 && results.length > 1) {
      operands = [results[0], results[1]];
    }
    
    // 如果只有一个操作数，假设另一个是1或结果值
    if (operands.length === 1) {
      if (toolName === 'multiply' || toolName === 'divide') {
        operands.push('2'); // 假设默认乘除数是2
      } else {
        operands.push('1'); // 假设默认加减数是1
      }
    }
    
    // 创建计算表达式: 操作数1 [操作符] 操作数2 = 结果
    if (operands.length >= 1 && finalResult) {
      resultHtml = `
        <div class="tool-result">${operands[0]}</div>
        <div class="tool-operator">${operator}</div>
        <div class="tool-result">${operands.length > 1 ? operands[1] : '?'}</div>
        <div class="tool-operator">=</div>
        <div class="tool-result">${finalResult}</div>
      `;
    } else {
      // 简单显示所有结果
      results.forEach(result => {
        resultHtml += `<div class="tool-result">${result}</div>`;
      });
    }
  } else {
    // 简单显示所有结果
    results.forEach(result => {
      resultHtml += `<div class="tool-result">${result}</div>`;
    });
  }
  
  return resultHtml;
}
</script>

<style scoped>
.chat-container {
  position: fixed;
  left: 20px;
  bottom: 20px;
  z-index: 1000;
}

.chat-icon {
  width: 50px;
  height: 50px;
  background: linear-gradient(90deg, #e6f7ff, #91d5ff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #0050b3;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.chat-panel {
  width: 450px;
  height: calc(100vh - 40px);
  background: rgba(250, 250, 250, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  color: #333;
}

.chat-header {
  background: linear-gradient(90deg, #f0f2f5, #f5f7fa);
  color: #333;
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  backdrop-filter: blur(5px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.header-actions {
  display: flex;
  gap: 8px;
}

.header-actions button {
  background: none;
  border: none;
  color: #333;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.header-actions button:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: scale(1.1);
}

.header-actions button i {
  font-size: 16px;
}

.header-actions .close-btn:hover {
  background: rgba(255, 59, 48, 0.2);
  color: #ff453a;
}

.header-actions .reset-btn {
  background: none;
  border: none;
  color: #333;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.header-actions .reset-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #1890ff;
  transform: scale(1.1);
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  background: rgba(250, 250, 250, 0.95);
}

.message {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 12px;
  word-break: break-word;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.user-message {
  align-self: flex-end;
  background: #1a4b91;
  color: #ffffff;
}

.ai-message {
  align-self: flex-start;
  background: #e6f7ff;
  color: #333;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.chat-input {
  padding: 16px;
  display: flex;
  gap: 8px;
  background: rgba(245, 245, 245, 0.95);
  backdrop-filter: blur(5px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.chat-input textarea {
  flex: 1;
  resize: none;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 12px;
  font-family: inherit;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  transition: all 0.3s;
}

.chat-input textarea:focus {
  outline: none;
  border-color: #1890ff;
  background: rgba(255, 255, 255, 0.95);
}

.chat-input textarea::placeholder {
  color: #999;
}

.input-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.chat-input button {
  padding: 8px 16px;
  background: #1a4b91;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.chat-input .stop-btn {
  background: #ff453a;
}

.chat-input .stop-btn:hover {
  background: #d73a31;
}

.chat-input button:hover:not(:disabled) {
  background: #1d5bb1;
  transform: translateY(-1px);
}

.chat-input button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Markdown 样式 */
:deep(.message-content pre) {
  background: #1f2937;
  color: #e1e4e8;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  font-family: 'Fira Code', monospace;
  margin: 8px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

:deep(.message-content code) {
  font-family: 'Fira Code', monospace;
  padding: 2px 6px;
  background: #1f2937;
  color: #e1e4e8;
  border-radius: 4px;
}

:deep(.message-content a) {
  color: #58a6ff;
  text-decoration: none;
}

:deep(.message-content a:hover) {
  text-decoration: underline;
}

/* 调整文档管理按钮样式 */
.doc-manager-btn {
  background: none;
  border: none;
  color: #e1e4e8;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.doc-manager-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: scale(1.1);
}

/* 工具使用提示样式 */
:deep(.tool-usage) {
  display: inline-flex;
  align-items: center;
  margin-bottom: 8px;
  padding: 4px 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  font-weight: normal;
  gap: 4px;
}

:deep(.tool-usage.success) {
  background: rgba(82, 196, 26, 0.1);
}

:deep(.tool-usage.warning) {
  background: rgba(250, 173, 20, 0.1);
}

:deep(.tool-usage.neutral) {
  background: rgba(24, 144, 255, 0.1);
}

:deep(.tool-icon) {
  margin-right: 2px;
  font-size: 12px;
}

:deep(.tool-icon.success-icon) {
  color: #52c41a;
}

:deep(.tool-icon.warning-icon) {
  color: #faad14;
}

:deep(.tool-icon.neutral-icon) {
  color: #1890ff;
}

:deep(.tool-name) {
  font-weight: 400;
  font-size: 12px;
  color: #666;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
  padding-right: 4px;
}

:deep(.tool-status) {
  font-size: 11px;
  color: #888;
  padding-left: 2px;
}

:deep(.tool-result) {
  margin: 0;
  padding: 8px 14px;
  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #e6edf8;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  text-align: center;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  /* 使用等宽字体更清晰展示数值 */
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
}

/* 改进不同工具类型的样式 */
:deep(.tool-usage.math) {
  background-color: #f5f0ff;
  border-color: #dfc3ff;
}

:deep(.tool-usage.math .tool-result) {
  color: #6200ea;
  border-color: #dfc3ff;
}

:deep(.tool-usage.weather) {
  background-color: #f0faff;
  border-color: #c0e0ff;
}

:deep(.tool-usage.redmine) {
  background-color: #fff8f0;
  border-color: #ffe0c0;
}

/* 常用问题样式 */
.common-questions {
  margin-top: 10px;
  width: 100%;
}

.common-questions-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 6px;
}

.question-buttons {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.question-buttons button {
  text-align: left;
  background: rgba(26, 75, 145, 0.1);
  color: #1a4b91;
  border: 1px solid rgba(26, 75, 145, 0.2);
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s;
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.question-buttons button:hover:not(:disabled) {
  background: rgba(26, 75, 145, 0.2);
  transform: translateY(-1px);
}

.question-buttons button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 文件预览样式 */
:deep(.file-preview) {
  margin-top: 12px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #eaeaea;
  padding-bottom: 8px;
}

:deep(.file-thumbnail) {
  max-width: 100%;
  height: auto;
  display: block;
  border-bottom: 1px solid #eaeaea;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer !important;
}

:deep(.file-thumbnail:hover) {
  transform: scale(1.01);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  outline: 2px solid #4a90e2; 
}

:deep(.file-link) {
  display: block;
  padding: 12px 16px;
  text-align: center;
  color: #1a4b91;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s;
  margin-top: 8px;
  border-radius: 4px;
  background: rgba(26, 75, 145, 0.05);
  cursor: pointer !important;
}

:deep(.file-link:hover) {
  color: #2d6ecd;
  background: rgba(26, 75, 145, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  outline: 2px solid #4a90e2;
}

:deep(.gongtubao-images) {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  padding: 8px;
  max-height: 420px;
  overflow-y: auto;
}

:deep(.gongtubao-image) {
  width: 100%;
  border-radius: 4px;
  border: 1px solid #eaeaea;
  transition: transform 0.2s, box-shadow 0.2s;
  cursor: pointer !important;
}

:deep(.gongtubao-image:hover) {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  outline: 2px solid #4a90e2;
}

.backend-switch {
  position: relative;
  background: none;
  border: none;
  color: #333;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.backend-switch:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: scale(1.1);
}

.backend-indicator {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #cccccc;
  transition: all 0.3s;
}

.backend-indicator.active {
  background: #52c41a;
}

/* 工具使用样式 */
:deep(.tool-usage) {
  margin: 10px 0;
  padding: 8px 12px;
  border-radius: 8px;
  background-color: #f0f7ff;
  border: 1px solid #d0e3ff;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

:deep(.tool-header) {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  padding-bottom: 4px;
  border-bottom: 1px solid #e0e9f7;
}

:deep(.tool-icon) {
  margin-right: 6px;
  font-size: 16px;
}

:deep(.tool-name) {
  font-weight: 500;
  font-size: 14px;
  color: #1e63c4;
}

:deep(.tool-content) {
  padding-top: 4px;
}

:deep(.tool-result) {
  margin: 0;
  padding: 6px;
  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #e6edf8;
  font-family: monospace;
  font-size: 13px;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  margin-bottom: 6px;
}

:deep(.tool-result:last-child) {
  margin-bottom: 0;
}

/* 数学运算工具的特殊样式 */
:deep(.tool-usage.math .tool-content) {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

:deep(.tool-usage.math .tool-result) {
  margin-bottom: 0;
  padding: 6px 12px;
  min-width: 50px;
  text-align: center;
  font-size: 16px;
  font-weight: 600;
  color: #6200ea;
  background-color: #f5f0ff;
  border-color: #dfc3ff;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(98, 0, 234, 0.1);
}

:deep(.tool-usage.math .tool-operator) {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 500;
  color: #6200ea;
  padding: 0 4px;
}

/* 不同工具类型可以使用不同颜色 */
:deep(.tool-usage.weather) {
  background-color: #f0faff;
  border-color: #c0e0ff;
}

:deep(.tool-usage.math) {
  background-color: #f6f4ff;
  border-color: #e0d4ff;
}

:deep(.tool-usage.redmine) {
  background-color: #fff8f0;
  border-color: #ffe0c0;
}

/* 步骤式数学表达式样式 */
:deep(.math-expression) {
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 8px;
  width: 100%;
}

:deep(.step-container) {
  display: flex;
  flex-direction: column;
  border: 1px solid #e0d4ff;
  border-radius: 8px;
  padding: 8px;
  background-color: #f9f7ff;
}

:deep(.step-label) {
  font-size: 12px;
  color: #6200ea;
  font-weight: 500;
  margin-bottom: 6px;
  border-bottom: 1px dashed #e0d4ff;
  padding-bottom: 4px;
}

:deep(.step-content) {
  display: flex;
  align-items: center;
  flex-direction: row;
  justify-content: center;
  gap: 8px;
  padding: 4px;
}

:deep(.final-result) {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 10px;
  background-color: #f5f0ff;
  border: 1px solid #dfc3ff;
  border-radius: 8px;
  margin-top: 5px;
}

:deep(.result-label) {
  font-size: 14px;
  color: #6200ea;
  margin-bottom: 6px;
  font-weight: 500;
}

:deep(.result-value) {
  font-size: 22px;
  color: #6200ea;
  font-weight: 700;
}

:deep(.ai-final-message) {
  margin-top: 15px;
  padding: 10px 15px;
  border-radius: 8px;
  background-color: #f8f9fa;
  border-left: 3px solid #1e63c4;
  line-height: 1.5;
}

:deep(.ai-final-message p) {
  margin: 0;
  font-size: 15px;
  color: #333;
}

/* TTS相关样式 */
.tts-toggle-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tts-toggle-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

.tts-toggle-btn .tts-enabled {
  color: #4CAF50;
}

.tts-toggle-btn .tts-disabled {
  color: #999;
}

.message-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.message:hover .message-actions {
  opacity: 1;
}

.tts-play-btn {
  background: none;
  border: 1px solid #ddd;
  color: #666;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tts-play-btn:hover:not(:disabled) {
  background: #f5f5f5;
  border-color: #ccc;
  color: #333;
}

.tts-play-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.tts-play-btn i {
  font-size: 10px;
}

/* 自动播放按钮样式 */
.auto-play-btn {
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auto-play-btn:hover {
  background: rgba(0, 123, 255, 0.1);
  color: #007bff;
}

.auto-play-btn .fa-play-circle.auto-play-enabled {
  color: #28a745;
}

.auto-play-btn .fa-play-circle.auto-play-disabled {
  color: #6c757d;
}

/* 自动播放状态指示器 */
.auto-play-indicator {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 8px;
  height: 8px;
  background: #ff4444;
  border-radius: 50%;
  font-size: 8px;
  color: #ff4444;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* TTS启用/禁用状态样式 */
.tts-enabled {
  color: #28a745 !important;
}

.tts-disabled {
  color: #6c757d !important;
}
</style>