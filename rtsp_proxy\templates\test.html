
<!DOCTYPE html>
<html>
<head>
    <title>RTSP流测试页面</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
        }
        .test-form {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-row {
            margin-bottom: 10px;
        }
        label {
            display: block;
            margin-bottom: 5px;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .preview {
            margin-top: 20px;
            width: 100%;
            height: 400px;
            background-color: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
        }
        .preview img {
            max-width: 100%;
            max-height: 100%;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RTSP流测试页面</h1>
        
        <div class="test-form">
            <div class="form-row">
                <label for="streamName">流名称:</label>
                <input type="text" id="streamName" placeholder="输入已创建的流名称">
            </div>
            <button id="loadStream">加载流</button>
        </div>
        
        <div id="preview" class="preview">
            <div>在此处显示视频流</div>
        </div>
    </div>
    
    <script>
        document.getElementById('loadStream').addEventListener('click', function() {
            const streamName = document.getElementById('streamName').value.trim();
            if (!streamName) {
                alert('请输入流名称');
                return;
            }
            
            const preview = document.getElementById('preview');
            preview.innerHTML = '';
            
            const img = document.createElement('img');
            // 添加时间戳防止缓存
            img.src = `/api/stream/${streamName}/mjpeg?t=${Date.now()}`;
            img.onerror = function() {
                preview.innerHTML = '<div style="color: red">加载流失败，请检查流名称是否正确</div>';
            };
            
            preview.appendChild(img);
        });
    </script>
</body>
</html>
        