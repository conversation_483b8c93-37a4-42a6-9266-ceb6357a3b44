<template>
  <div ref="sceneContainer" class="scene-container"></div>
  <div v-if="loadingProgress.visible" class="loading-overlay">
    <div class="loading-content">
      <div class="loading-text">{{ loadingProgress.text }}</div>
      <div class="progress-bar">
        <div class="progress-fill" :style="{ width: loadingProgress.percentage + '%' }"></div>
      </div>
      <div class="progress-text">{{ loadingProgress.percentage }}%</div>
    </div>
  </div>
  <DevicePanel
    v-if="devicePanelPosition.visible"
    :deviceInfo="deviceStore.deviceData || getDefaultDeviceInfo()"
    :position="{
      x: devicePanelPosition.x,
      y: devicePanelPosition.y
    }"
    @heightChange="height => currentPanelHeight = height"
    @close="closeDevicePanel"
  />
  <VideoPanel
    :visible="videoPanelVisible"
    :position="videoPanelPosition"
    :initialSize="videoPanelSize"
    @close="closeVideoPanel"
  />
  <ChatPanel />
  <SceneDock 
    :currentScene="currentScene" 
    @sceneChange="handleSceneChange" 
  />
  <AlertDialog />
</template>

<script setup>
import * as THREE from 'three'
import { onMounted, onUnmounted, ref, computed, inject } from 'vue'
import { useDeviceStore } from '../../stores/deviceStore'
import DevicePanel from '../DevicePanel/DevicePanel.vue'
import VideoPanel from '../VideoPanel/VideoPanel.vue'
import ChatPanel from '../ChatPanel/ChatPanel.vue'
import SceneDock from './SceneDock.vue'
import AlertDialog from '../AlertDialog/AlertDialog.vue'
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader.js'
import { EffectComposer } from 'three/examples/jsm/postprocessing/EffectComposer.js'
import { RenderPass } from 'three/examples/jsm/postprocessing/RenderPass.js'
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass.js'
import * as TWEEN from '@tweenjs/tween.js'
import { emitter } from '../../utils/emitter'
import deviceConfigManager from '../../utils/deviceConfig'

// 获取设备Store
const deviceStore = useDeviceStore()

// 场景元素
const sceneContainer = ref(null)
const loader = new GLTFLoader()

// 场景状态
const currentScene = ref('oilPump')
const scenes = {
  oilPump: {
    objects: [],
    setup: setupOilPumpScene
  },
  gasPipeline: {
    modelPath: 'Files/gltf/3dzutai.gltf',
    objects: [],
    setup: setupGasPipelineScene
  },
  waterInjection: {
    modelPath: 'Files/gltf/JH_MODELS.gltf',
    objects: [],
    setup: setupWaterInjectionScene
  }
}

// 组件状态
const devicePanelPosition = ref({ x: 0, y: 0, visible: false })
const currentPanelHeight = ref(0)

// 添加加载进度状态
const loadingProgress = ref({
  visible: false,
  percentage: 0,
  text: '正在加载模型...'
})

// 视频面板状态
const videoPanelVisible = ref(false) // 默认不显示视频面板
const videoPanelPosition = ref({ x: window.innerWidth - 840, y: 20 }) // 右上角位置
const videoPanelSize = ref({ width: 800, height: 550 }) // 设置视频面板初始大小

// 默认设备信息
function getDefaultDeviceInfo() {
  return {
    WELL_COMMON_NAME: '--',
    status: 'abnormal',
    oilPressure: '--',
    casingPressure: '--',
    wellheadTemp: '--',
    strokeLength: '--',
    strokeRate: '--',
    activePower: '--',
    reactivePower: '--',
    powerFactor: '--',
    totalPower: '--',
    phaseA: { voltage: '--', current: '--' },
    phaseB: { voltage: '--', current: '--' },
    phaseC: { voltage: '--', current: '--' },
    disp_load: '',
    disp_current: '',
    displacement: []
  }
}

// 初始化 Three.js 场景
let scene, camera, renderer, controls, composer, selectedObject = null, outlinePass, clock

// 相机移动速度
const moveSpeed = 1

// 键盘状态
const keys = {
  w: false,
  a: false,
  s: false,
  d: false,
  ' ': false, // 空格键
  f: false, // f键
  shift: false // 添加shift键状态
}

// 取消选中当前对象
function deselectObject() {
  if (selectedObject) {
    // 如果是抽油机组，需要遍历所有子对象恢复原始颜色
    if (selectedObject.userData.isPumpJack || selectedObject.name?.match(/组\d{3}/)) {
      selectedObject.traverse((child) => {
        if (child.isMesh && child.userData.originalColor) {
          child.material.color.copy(child.userData.originalColor)
        }
      })
    } else if (selectedObject.material && selectedObject.userData.originalColor) {
      selectedObject.material.color.copy(selectedObject.userData.originalColor)
    }

    // 恢复原始比例
    selectedObject.scale.set(1, 1, 1)
    
    // 清理特效
    if (selectedObject.userData.glowMesh) {
      scene.remove(selectedObject.userData.glowMesh)
    }
    if (selectedObject.userData.borderLine) {
      scene.remove(selectedObject.userData.borderLine)
    }
    
    // 移除所有相关的补间动画
    TWEEN.removeAll()
    
    // 清除选中状态
    selectedObject = null
    outlinePass.selectedObjects = []
    devicePanelPosition.value.visible = false
    deviceStore.selectDevice(null)
    deviceStore.deviceData = null
  }
}

// 初始化基础场景
function initBaseScene() {
  scene = new THREE.Scene()
  clock = new THREE.Clock() // 添加时钟对象
  
  camera = new THREE.PerspectiveCamera(
    75,
    sceneContainer.value.clientWidth / sceneContainer.value.clientHeight,
    0.1,
    10000
  )
  
  camera.position.set(300, 160, -50)
  camera.lookAt(0, 0, 0)
  
  renderer = new THREE.WebGLRenderer({
    antialias: true,
    alpha: false
  })
  renderer.setSize(sceneContainer.value.clientWidth, sceneContainer.value.clientHeight)
  renderer.setClearColor(0x0a1a2f)
  
  // 优化设备性能，根据设备像素比设置渲染器像素比
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5))
  
  // 调整色调映射，使场景更加逼真
  renderer.physicallyCorrectLights = true
  renderer.toneMapping = THREE.ReinhardToneMapping
  renderer.toneMappingExposure = 2.8
  renderer.outputEncoding = THREE.sRGBEncoding
  
  sceneContainer.value.appendChild(renderer.domElement)
  
  // 调整环境光照强度，避免过亮
  const ambientLight = new THREE.AmbientLight(0xffffff, 2.5)
  scene.add(ambientLight)
  
  // 添加半球光，从天空到地面的渐变光照，更自然
  const hemisphereLight = new THREE.HemisphereLight(0xddeeff, 0x202020, 1.5)
  scene.add(hemisphereLight)
  
  // 调整主直射光，优化位置和强度
  const directionalLight = new THREE.DirectionalLight(0xffffff, 1.0)
  directionalLight.position.set(150, 200, 100)
  directionalLight.castShadow = true
  // 优化阴影贴图尺寸，平衡质量和性能
  directionalLight.shadow.mapSize.width = 1024
  directionalLight.shadow.mapSize.height = 1024
  directionalLight.shadow.camera.near = 0.5
  directionalLight.shadow.camera.far = 1000
  directionalLight.shadow.bias = -0.001 // 减少阴影瑕疵
  scene.add(directionalLight)
  
  // 调整填充光，使其更柔和
  const fillLight = new THREE.DirectionalLight(0xffffff, 0.7)
  fillLight.position.set(-100, 50, -50)
  scene.add(fillLight)
  
  // 增加背光，提高模型轮廓感
  const backLight = new THREE.DirectionalLight(0xffffff, 0.5)
  backLight.position.set(0, 50, -100)
  scene.add(backLight)
  
  // 调整点光源，减小强度和范围，只照亮关键区域
  const pointLight1 = new THREE.PointLight(0xffffff, 0.8, 500)
  pointLight1.position.set(0, 150, 0)
  pointLight1.castShadow = true
  pointLight1.shadow.bias = -0.001
  scene.add(pointLight1)
  
  renderer.shadowMap.enabled = true
  renderer.shadowMap.type = THREE.PCFSoftShadowMap
  
  // 加载天空盒
  const cubeTextureLoader = new THREE.CubeTextureLoader()
  const skyTexture = cubeTextureLoader.load([
    'Files/Images/cubemaps/sky/posx.jpg',
    'Files/Images/cubemaps/sky/negx.jpg',
    'Files/Images/cubemaps/sky/posy.jpg',
    'Files/Images/cubemaps/sky/negy.jpg',
    'Files/Images/cubemaps/sky/posz.jpg',
    'Files/Images/cubemaps/sky/negz.jpg'
  ])
  
  // 调整天空盒颜色，使其更加明亮
  const skyColor = new THREE.Color(0.6, 0.8, 1.0); // 亮蓝色
  scene.background = skyTexture
  scene.fog = new THREE.FogExp2(skyColor, 0.0008); // 添加轻微雾效，增强景深感
  
  composer = new EffectComposer(renderer)
  const renderPass = new RenderPass(scene, camera)
  composer.addPass(renderPass)
  
  outlinePass = new OutlinePass(
    new THREE.Vector2(sceneContainer.value.clientWidth, sceneContainer.value.clientHeight),
    scene,
    camera
  )
  outlinePass.visibleEdgeColor.set(0xffff00)
  outlinePass.hiddenEdgeColor.set(0x000000)
  outlinePass.edgeStrength = 2.5
  outlinePass.edgeThickness = 1.5
  outlinePass.pulsePeriod = 2 // 添加脉冲效果
  composer.addPass(outlinePass)
  
  // 优化composer设置
  composer.setSize(sceneContainer.value.clientWidth, sceneContainer.value.clientHeight)
  composer.setPixelRatio(Math.min(window.devicePixelRatio, 1.5))
  
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true
  controls.dampingFactor = 0.05
  controls.screenSpacePanning = true
  controls.enablePan = true
  controls.enableRotate = true  // 确保启用旋转
  controls.rotateSpeed = 0.3    // 添加旋转速度控制，值越大旋转越快
  controls.minPolarAngle = 0  // 允许向上旋转到顶部
  controls.maxPolarAngle = Math.PI  // 允许向下旋转到底部
  controls.minDistance = 1
  controls.maxDistance = 50000
  controls.panSpeed = 0.5
}

// 修改加载GLTF模型的函数，优化材质处理
async function loadGLTFModel(path) {
  return new Promise((resolve, reject) => {
    loadingProgress.value.visible = true
    loadingProgress.value.percentage = 0
    loadingProgress.value.text = '正在加载模型...'
    
    const manager = new THREE.LoadingManager()
    manager.onProgress = (url, loaded, total) => {
      const percentage = Math.round((loaded / total) * 100)
      loadingProgress.value.percentage = percentage
    }
    
    manager.onLoad = () => {
      loadingProgress.value.visible = false
    }
    
    manager.onError = (url) => {
      console.error('加载出错:', url)
      loadingProgress.value.text = '加载失败，请刷新页面重试'
      reject(new Error('模型加载失败'))
    }
    
    const loader = new GLTFLoader(manager)
    loader.load(path,
      (gltf) => {
        // 处理模型材质，根据材质名称智能调整参数
        gltf.scene.traverse((node) => {
          if (node.isMesh) {
            // 保存原始材质类型以便后续处理
            const originalMaterial = node.material;
            
            // 根据材质名称或节点名称判断材质类型
            const isMetal = node.name.toLowerCase().includes('metal') || 
                           (originalMaterial.name && originalMaterial.name.toLowerCase().includes('metal'));
            const isPlastic = node.name.toLowerCase().includes('plastic') || 
                             (originalMaterial.name && originalMaterial.name.toLowerCase().includes('plastic'));
            
            // 检查是否为抽油机部件
            const isPumpPart = node.name?.toLowerCase().includes('抽油') || 
                              node.parent?.name?.match(/组\d{3}/) ||
                              node.name?.toLowerCase().includes('pump');
                              
            // 获取父节点组编号，用于给不同组分配不同颜色
            let groupNumber = 0;
            if (node.parent?.name?.match(/组(\d{3})/)) {
              groupNumber = parseInt(node.parent.name.match(/组(\d{3})/)[1]) || 0;
            }
                             
            // 如果是MeshStandardMaterial或MeshPhysicalMaterial，直接调整参数
            if (originalMaterial.isMeshStandardMaterial || originalMaterial.isMeshPhysicalMaterial) {
              // 特殊处理抽油机组件的颜色
              if (isPumpPart && originalMaterial.color) {
                // 检测是否为深红色
                if (originalMaterial.color.r > 0.7 && originalMaterial.color.g < 0.3 && originalMaterial.color.b < 0.3) {
                  // 根据组号分配不同颜色
                  switch(groupNumber % 5) {
                    case 0:
                      // 深蓝色
                      originalMaterial.color.setRGB(0.1, 0.3, 0.7);
                      break;
                    case 1:
                      // 绿色
                      originalMaterial.color.setRGB(0.1, 0.6, 0.3);
                      break;
                    case 2:
                      // 橙色
                      originalMaterial.color.setRGB(0.9, 0.6, 0.1);
                      break;
                    case 3:
                      // 青色
                      originalMaterial.color.setRGB(0.1, 0.6, 0.7);
                      break;
                    case 4:
                      // 紫色
                      originalMaterial.color.setRGB(0.5, 0.2, 0.7);
                      break;
                    default:
                      // 默认蓝色
                      originalMaterial.color.setRGB(0.2, 0.4, 0.7);
                  }
                }
                
                // 增强材质质感
                originalMaterial.metalness = Math.min(originalMaterial.metalness + 0.1, 0.9);
                originalMaterial.roughness = Math.max(originalMaterial.roughness - 0.1, 0.2);
                
                // 增加环境贴图强度，提高反光效果
                originalMaterial.envMapIntensity = 1.5;
                
                // 给抽油机增加轻微发光效果，增强视觉效果
                const colorObj = originalMaterial.color;
                // 创建一个更柔和的发光颜色
                const emissiveColor = new THREE.Color(
                  colorObj.r * 0.25,
                  colorObj.g * 0.25,
                  colorObj.b * 0.25
                );
                originalMaterial.emissive = emissiveColor;
              }
              
              if (isMetal) {
                originalMaterial.roughness = 0.2;
                originalMaterial.metalness = 0.9;
                originalMaterial.envMapIntensity = 1.2;
                // 增加材质亮度
                originalMaterial.emissive = new THREE.Color(0x222222);
              } else if (isPlastic) {
                originalMaterial.roughness = 0.7;
                originalMaterial.metalness = 0.1;
                originalMaterial.envMapIntensity = 0.8;
                // 增加材质亮度
                originalMaterial.emissive = new THREE.Color(0x111111);
              } else {
                originalMaterial.roughness = Math.max(0.5, originalMaterial.roughness);
                originalMaterial.metalness = Math.min(0.4, originalMaterial.metalness);
                originalMaterial.envMapIntensity = 0.9;
                // 增加材质亮度
                originalMaterial.emissive = new THREE.Color(0x111111);
              }
              
              // 如果有镜面贴图，降低其影响
              if (originalMaterial.specularMap) {
                originalMaterial.specularIntensity = 0.6;
              }
            } 
            // 如果是MeshPhongMaterial或其他材质，转换为MeshStandardMaterial
            else if (originalMaterial.isMeshPhongMaterial || originalMaterial.isMeshLambertMaterial) {
              const newMaterial = new THREE.MeshStandardMaterial({
                color: originalMaterial.color ? originalMaterial.color.clone() : 0xffffff,
                emissive: new THREE.Color(0x111111),
                map: originalMaterial.map,
                normalMap: originalMaterial.normalMap,
                roughness: isMetal ? 0.2 : (isPlastic ? 0.7 : 0.5),
                metalness: isMetal ? 0.9 : (isPlastic ? 0.1 : 0.4),
                envMapIntensity: isMetal ? 1.2 : (isPlastic ? 0.8 : 0.9),
                transparent: originalMaterial.transparent,
                opacity: originalMaterial.opacity
              });
              node.material = newMaterial;
            }
            
            // 根据模型大小决定是否投射阴影，优化性能
            const box = new THREE.Box3().setFromObject(node);
            const size = box.getSize(new THREE.Vector3());
            
            if (size.x > 10 || size.y > 10 || size.z > 10) {
              node.castShadow = true;
            } else {
              node.castShadow = false; // 小物体不投射阴影，提高性能
            }
            node.receiveShadow = true;
          }
        });
        
        // 根据不同模型路径调整缩放
        if (path.includes('gasPipeline')) {
          gltf.scene.scale.set(4, 4, 4);
        } else if (path.includes('JH_MODELS')) {
          gltf.scene.scale.set(6, 6, 6);
          gltf.scene.position.y = -5; // 调整水注场景模型位置
        } else {
          gltf.scene.scale.set(5, 5, 5);
        }
        
        scene.add(gltf.scene)
        resolve(gltf)
      },
      (progress) => {
        const percentage = Math.round((progress.loaded / progress.total) * 100)
        loadingProgress.value.percentage = percentage
      },
      (error) => {
        console.error('加载模型失败:', error)
        loadingProgress.value.text = '加载失败，请刷新页面重试'
        reject(error)
      }
    )
  })
}

// 修改处理场景切换的函数
async function handleSceneChange(newScene) {
  deselectObject()
  clearCurrentScene()
  currentScene.value = newScene
  await scenes[newScene].setup()
}

// 动态计算相机移动速度的函数
function calculateMoveSpeed() {
  // 获取相机与原点的距离
  //const distance = camera.position.distanceTo(new THREE.Vector3(0, 0, 0))
  // 根据距离动态调整速度，距离越远移动越快
  //return Math.max(1, distance / 300)
  return 0.6
}

// 修改相机位置更新函数
function updateCameraPosition() {
  // 获取相机的前方向和右方向
  const forward = new THREE.Vector3(0, 0, -1)
  const right = new THREE.Vector3(1, 0, 0)
  
  // 将方向向量应用相机的旋转
  forward.applyQuaternion(camera.quaternion)
  right.applyQuaternion(camera.quaternion)
  
  // 移除这两行代码，允许相机上下旋转
  // forward.y = 0
  // right.y = 0
  
  // 标准化向量
  forward.normalize()
  right.normalize()
  
  // 计算当前动态速度
  let currentSpeed = calculateMoveSpeed()
  
  // 如果按下shift键，增加移动速度（加速效果）
  if (keys.shift) {
    currentSpeed *= 3.0  // 速度增加3倍
  }
  
  // 垂直移动速度设置为水平移动速度的0.5倍
  const verticalSpeed = currentSpeed * 0.5
  
  // 根据按键状态更新位置
  if (keys.w) camera.position.addScaledVector(forward, currentSpeed)
  if (keys.s) camera.position.addScaledVector(forward, -currentSpeed)
  if (keys.a) camera.position.addScaledVector(right, -currentSpeed)
  if (keys.d) camera.position.addScaledVector(right, currentSpeed)
  if (keys[' ']) camera.position.y += verticalSpeed // 空格键向上
  if (keys.f) camera.position.y -= verticalSpeed // f键向下
  
  // 更新控制器目标点，保持当前视角方向
  const lookAtDirection = forward.clone()
  controls.target.copy(camera.position).add(lookAtDirection)
}

// 调整setupOilPumpScene函数的材质
function setupOilPumpScene() {
  const deviceColors = [0xff0000, 0x0000ff, 0x00ff00, 0xffa500, 0x800080]
  
  // 获取所有设备配置
  const devices = deviceConfigManager.getAllDevices();
  
  // 只处理非"组xxx"格式的设备（这些通常是单独的设备，不是抽油机组）
  const standaloneDevices = devices.filter(dev => !dev.model_name.startsWith('组'));
  
  // 创建每个独立设备的3D对象
  standaloneDevices.forEach((device, index) => {
    const colorIndex = index % deviceColors.length;
    
    // 使用MeshStandardMaterial更好地控制金属感和粗糙度
    const material = new THREE.MeshStandardMaterial({ 
      color: deviceColors[colorIndex],
      roughness: 0.6,
      metalness: 0.3,
      envMapIntensity: 0.4
    });
    
    const deviceGeometry = new THREE.BoxGeometry(30, 30, 30);
    const deviceObj = new THREE.Mesh(deviceGeometry, material);
    
    // 设置位置（这里使用简单的网格布局）
    const row = Math.floor(index / 3);
    const col = index % 3;
    deviceObj.position.set(-100 + col * 100, 13, -100 + row * 100);
    
    deviceObj.castShadow = true;
    
    // 设置用户数据，包含配置信息
    deviceObj.userData = {
      model_name: device.model_name,
      display_name: device.display_name,
      topic: device.topic
    };
    
    scene.add(deviceObj);
    scenes.oilPump.objects.push(deviceObj);
  });
}

// 设置气体管道场景
async function setupGasPipelineScene() {
  try {
    const gltf = await loadGLTFModel(scenes.gasPipeline.modelPath)
    gltf.scene.scale.set(5, 5, 5)
    scenes.gasPipeline.objects.push(gltf.scene)
  } catch (error) {
    console.error('加载气体管道模型失败:', error)
  }
}

// 设置水注管理场景
async function setupWaterInjectionScene() {
  try {
    const gltf = await loadGLTFModel(scenes.waterInjection.modelPath)
    gltf.scene.scale.set(5, 5, 5)
    
    console.log('==== 模型加载信息 ====')
    console.log('动画列表：', gltf.animations.map(anim => anim.name))
    
    // 创建动画混合器
    const mixer = new THREE.AnimationMixer(gltf.scene)
    scenes.waterInjection.mixer = mixer
    
    // 遍历场景，标记抽油机组
    gltf.scene.traverse((node) => {
      // 检查是否是抽油机组（直接检查名称是否匹配"组xxx"格式）
      if (node.name?.match(/组\d{3}/)) {
        // 使用配置管理器查找设备信息
        const deviceConfig = deviceConfigManager.findByModelName(node.name);
        
        if (deviceConfig) {
          console.log(`找到抽油机组配置: ${node.name} -> ${deviceConfig.topic}`);
          
          // 使用配置文件中的信息设置userData
          node.userData = {
            isPumpJack: true,
            id: node.name,
            topic: deviceConfig.topic,
            display_name: deviceConfig.display_name,
            model_name: deviceConfig.model_name
          };
          
          // 为组内所有子对象添加相同的userData
          node.traverse((child) => {
            if (child !== node) {
              child.userData = {
                ...node.userData,
                isPartOfPumpJack: true,
                parentGroup: node
              };
            }
          });
        } else {
          console.warn(`未找到抽油机组配置: ${node.name}`);
        }
      }
    })
    
    // 播放所有动画
    gltf.animations.forEach((clip) => {
      console.log('播放动画:', clip.name)
      const action = mixer.clipAction(clip)
      action.setLoop(THREE.LoopRepeat)
      action.timeScale = 1.0
      action.play()
    })
    
    scenes.waterInjection.objects.push(gltf.scene)
  } catch (error) {
    console.error('加载水注管理模型失败:', error)
  }
}

// 清理当前场景
function clearCurrentScene() {
  const currentObjects = scenes[currentScene.value].objects
  currentObjects.forEach(obj => {
    scene.remove(obj)
  })
  
  // 停止当前场景的动画混合器
  const currentMixer = scenes[currentScene.value].mixer
  if (currentMixer) {
    currentMixer.stopAllAction()
  }
  
  scenes[currentScene.value].objects = []
}

// 处理点击事件
function onClick(event) {
  const raycaster = new THREE.Raycaster()
  const mouse = new THREE.Vector2()
  
  const rect = renderer.domElement.getBoundingClientRect()
  mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1
  mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1
  
  raycaster.setFromCamera(mouse, camera)
  const intersects = raycaster.intersectObjects(scene.children, true)
  
  if (intersects.length > 0) {
    const object = intersects[0].object
    
    // 输出点击对象的详细信息
    console.log('==== 点击对象信息 ====')
    console.log('对象名称:', object.name || 'Unnamed')
    console.log('对象类型:', object.type)
    if (object.parent) {
      console.log('父对象名称:', object.parent.name || 'Unnamed')
    }
    
    // 获取目标对象
    let targetObject = null
    
    // 检查是否点击了抽油机组或其部件
    if (object.name?.match(/组\d{3}/)) {
      // 直接点击了抽油机组
      targetObject = object
    } else {
      // 向上遍历所有父级对象，查找抽油机组
      let current = object
      while (current) {
        if (current.name?.match(/组\d{3}/)) {
          targetObject = current
          break
        }
        current = current.parent
      }
      
      // 如果没找到抽油机组，检查是否是普通设备
      if (!targetObject && object.geometry instanceof THREE.BoxGeometry) {
        targetObject = object
      }
    }
    
    if (targetObject) {
      if (selectedObject !== targetObject) {
        deselectObject()
        selectedObject = targetObject
        
        // 如果是抽油机组
        if (targetObject.name?.match(/组\d{3}/)) {
          // 保存所有子对象的原始颜色
          targetObject.traverse((child) => {
            if (child.isMesh) {
              child.userData.originalColor = child.material.color.clone()
            }
          })
          
          // 将所有网格添加到轮廓选择中
          const outlineObjects = []
          targetObject.traverse((child) => {
            if (child.isMesh) {
              outlineObjects.push(child)
            }
          })
          outlinePass.selectedObjects = outlineObjects
          
          // 显示设备面板
          const vector = targetObject.position.clone().project(camera)
          const clickX = (vector.x * 0.5 + 0.5) * sceneContainer.value.clientWidth
          const clickY = (vector.y * -0.5 + 0.5) * sceneContainer.value.clientHeight
          
          const panelWidth = 400
          const estimatedHeight = currentPanelHeight.value || 600
          
          // 计算设备在屏幕上的相对位置
          const screenRatio = clickX / sceneContainer.value.clientWidth
          
          // 根据设备在屏幕上的相对位置动态调整面板位置
          let x, y
          if (currentScene.value === 'waterInjection') {
            // 注水管理场景：根据设备位置动态调整偏移
            if (screenRatio < 0.2) {
              // 设备在屏幕最左侧，面板显示在右侧，使用较大偏移
              x = clickX + 100
            } else if (screenRatio > 0.8) {
              // 设备在屏幕最右侧，面板显示在左侧，使用较大偏移
              x = clickX - panelWidth - 100
            } else if (screenRatio < 0.4) {
              // 设备在屏幕左侧区域，面板显示在右侧
              x = clickX + 60
            } else if (screenRatio > 0.6) {
              // 设备在屏幕右侧区域，面板显示在左侧
              x = clickX - panelWidth - 60
            } else {
              // 设备在屏幕中间区域，根据点击位置决定显示在左侧还是右侧
              x = clickX + 40
              if (x + panelWidth > window.innerWidth) {
                x = clickX - panelWidth - 40
              }
            }
            
            // 垂直位置根据设备高度调整
            y = clickY - estimatedHeight / 2
          } else {
            // 采油管理场景：保持原有的位置计算逻辑
            x = clickX + 20
            if (x + panelWidth > window.innerWidth) {
              x = clickX - panelWidth - 20
            }
            y = clickY - estimatedHeight / 3
          }
          
          // 优化边界检查逻辑
          // 垂直方向：确保面板完全在屏幕内
          if (y < 0) y = 10
          if (y + estimatedHeight > window.innerHeight) {
            y = window.innerHeight - estimatedHeight - 10
          }
          
          // 水平方向：允许面板部分超出屏幕
          if (screenRatio < 0.2) {
            // 最左侧区域：允许面板超出左边界
            if (x < -panelWidth) x = -panelWidth
          } else if (screenRatio > 0.8) {
            // 最右侧区域：允许面板超出右边界
            if (x > window.innerWidth) x = window.innerWidth - panelWidth
          } else {
            // 中间区域：保持面板在屏幕内
            if (x < 0) x = 10
            if (x + panelWidth > window.innerWidth) {
              x = window.innerWidth - panelWidth - 10
            }
          }
          
          devicePanelPosition.value = { x, y, visible: true }
          
          // 选中设备并开始MQTT订阅
          const deviceConfig = deviceConfigManager.findByModelName(targetObject.name);
          
          if (deviceConfig) {
            deviceStore.selectDevice({
              model_name: deviceConfig.model_name,
              display_name: deviceConfig.display_name,
              topic: deviceConfig.topic
            });
          } else {
            // 如果在配置中找不到，使用对象的userData（兼容旧逻辑）
            deviceStore.selectDevice({
              model_name: targetObject.name,
              topic: targetObject.userData.topic,
              display_name: targetObject.name
            });
          }
        } else {
          // 原有的设备选中效果
          targetObject.userData.originalColor = targetObject.material.color.clone()
          outlinePass.selectedObjects = [targetObject]
          
          new TWEEN.Tween(targetObject.scale)
            .to({ x: 1.5, y: 1.5, z: 1.5 }, 200)
            .easing(TWEEN.Easing.Quadratic.Out)
            .start()
          
          new TWEEN.Tween(targetObject.material.color)
            .to({ r: 1, g: 0.5, b: 0 }, 200)
            .easing(TWEEN.Easing.Quadratic.Out)
            .start()
          
          new TWEEN.Tween(targetObject.rotation)
            .to({ y: Math.PI * 2 }, 1000)
            .repeat(Infinity)
            .start()
          
          const glowMaterial = new THREE.MeshBasicMaterial({
            color: 0xffa500,
            transparent: true,
            opacity: 0.9,
            blending: THREE.AdditiveBlending
          })
          const glowGeometry = new THREE.BoxGeometry(40, 40, 40)
          const glowMesh = new THREE.Mesh(glowGeometry, glowMaterial)
          glowMesh.position.copy(targetObject.position)
          scene.add(glowMesh)
          targetObject.userData.glowMesh = glowMesh
          
          new TWEEN.Tween(glowMesh.scale)
            .to({ x: 1.5, y: 1.5, z: 1.5 }, 300)
            .easing(TWEEN.Easing.Quadratic.InOut)
            .yoyo(true)
            .repeat(Infinity)
            .start()
          
          // 显示设备面板
          const vector = targetObject.position.clone().project(camera)
          const clickX = (vector.x * 0.5 + 0.5) * sceneContainer.value.clientWidth
          const clickY = (vector.y * -0.5 + 0.5) * sceneContainer.value.clientHeight
          
          const panelWidth = 400
          const estimatedHeight = currentPanelHeight.value || 600
          
          // 计算设备在屏幕上的相对位置
          const screenRatio = clickX / sceneContainer.value.clientWidth
          
          // 根据设备在屏幕上的相对位置动态调整面板位置
          let x, y
          if (currentScene.value === 'waterInjection') {
            // 注水管理场景：根据设备位置动态调整偏移
            if (screenRatio < 0.2) {
              // 设备在屏幕最左侧，面板显示在右侧，使用较大偏移
              x = clickX + 100
            } else if (screenRatio > 0.8) {
              // 设备在屏幕最右侧，面板显示在左侧，使用较大偏移
              x = clickX - panelWidth - 100
            } else if (screenRatio < 0.4) {
              // 设备在屏幕左侧区域，面板显示在右侧
              x = clickX + 60
            } else if (screenRatio > 0.6) {
              // 设备在屏幕右侧区域，面板显示在左侧
              x = clickX - panelWidth - 60
            } else {
              // 设备在屏幕中间区域，根据点击位置决定显示在左侧还是右侧
              x = clickX + 40
              if (x + panelWidth > window.innerWidth) {
                x = clickX - panelWidth - 40
              }
            }
            
            // 垂直位置根据设备高度调整
            y = clickY - estimatedHeight / 2
          } else {
            // 采油管理场景：保持原有的位置计算逻辑
            x = clickX + 20
            if (x + panelWidth > window.innerWidth) {
              x = clickX - panelWidth - 20
            }
            y = clickY - estimatedHeight / 3
          }
          
          // 优化边界检查逻辑
          // 垂直方向：确保面板完全在屏幕内
          if (y < 0) y = 10
          if (y + estimatedHeight > window.innerHeight) {
            y = window.innerHeight - estimatedHeight - 10
          }
          
          // 水平方向：允许面板部分超出屏幕
          if (screenRatio < 0.2) {
            // 最左侧区域：允许面板超出左边界
            if (x < -panelWidth) x = -panelWidth
          } else if (screenRatio > 0.8) {
            // 最右侧区域：允许面板超出右边界
            if (x > window.innerWidth) x = window.innerWidth - panelWidth
          } else {
            // 中间区域：保持面板在屏幕内
            if (x < 0) x = 10
            if (x + panelWidth > window.innerWidth) {
              x = window.innerWidth - panelWidth - 10
            }
          }
          
          devicePanelPosition.value = { x, y, visible: true }
          
          // 选中设备并开始MQTT订阅
          const deviceConfig = deviceConfigManager.findByModelName(targetObject.name);
          
          if (deviceConfig) {
            deviceStore.selectDevice({
              model_name: deviceConfig.model_name,
              display_name: deviceConfig.display_name,
              topic: deviceConfig.topic
            });
          } else {
            // 如果在配置中找不到，使用对象的userData（兼容旧逻辑）
            deviceStore.selectDevice({
              model_name: targetObject.name,
              topic: targetObject.userData.topic,
              display_name: targetObject.name
            });
          }
        }
      }
    }
  } else {
    deselectObject()
  }
}

// 动画循环
function animate() {
  requestAnimationFrame(animate)
  updateCameraPosition()
  controls.update()
  TWEEN.update()
  
  // 更新当前场景的动画混合器
  const currentMixer = scenes[currentScene.value].mixer
  if (currentMixer) {
    const delta = clock.getDelta()
    currentMixer.update(delta)
  }
  
  composer.render()
}

// 键盘控制状态
const keyboardControlsEnabled = ref(true)

// 禁用/启用键盘控制
function setKeyboardControlsEnabled(enabled) {
  keyboardControlsEnabled.value = enabled
}

// 处理键盘事件
function onKeyDown(event) {
  if (event.key === 'Escape') {
    deselectObject()
    return
  }
  
  if (!keyboardControlsEnabled.value) return
  
  const key = event.key.toLowerCase()
  if (key in keys) {
    keys[key] = true
    event.preventDefault() // 防止默认行为
  } else if (event.shiftKey) {
    keys.shift = true
    event.preventDefault()
  }
}

function onKeyUp(event) {
  if (!keyboardControlsEnabled.value) return
  
  const key = event.key.toLowerCase()
  if (key in keys) {
    keys[key] = false
    event.preventDefault() // 防止默认行为
  } else if (key === 'shift') {
    keys.shift = false
    event.preventDefault()
  }
}

// 导出控制函数供其他组件使用
defineExpose({
  setKeyboardControlsEnabled,
  findDevice // 导出设备查找函数
})

// 处理窗口大小变化时同时更新composer
function onWindowResize() {
  camera.aspect = sceneContainer.value.clientWidth / sceneContainer.value.clientHeight
  camera.updateProjectionMatrix()
  renderer.setSize(sceneContainer.value.clientWidth, sceneContainer.value.clientHeight)
  
  // 更新composer尺寸
  composer.setSize(sceneContainer.value.clientWidth, sceneContainer.value.clientHeight)
  
  // 更新轮廓通道大小
  outlinePass.resolution.set(
    sceneContainer.value.clientWidth, 
    sceneContainer.value.clientHeight
  )
}

// 处理相机控制事件
function handleCameraControl(event) {
  const { detail } = event
  if (!detail || !detail.content) return
  
  try {
    const command = JSON.parse(detail.content)
    
    // 根据指令类型控制相机
    switch (command.action) {
      case 'moveTo':
        // 移动相机到指定位置
        if (command.position) {
          new TWEEN.Tween(camera.position)
            .to({
              x: command.position.x,
              y: command.position.y,
              z: command.position.z
            }, 1000)
            .easing(TWEEN.Easing.Quadratic.InOut)
            .start()
        }
        break
      case 'lookAt':
        // 相机看向指定位置
        if (command.target) {
          const targetVector = new THREE.Vector3(
            command.target.x,
            command.target.y,
            command.target.z
          )
          new TWEEN.Tween(controls.target)
            .to(targetVector, 1000)
            .easing(TWEEN.Easing.Quadratic.InOut)
            .start()
        }
        break
      case 'reset':
        // 重置相机位置
        new TWEEN.Tween(camera.position)
          .to({ x: 300, y: 160, z: -50 }, 1000)
          .easing(TWEEN.Easing.Quadratic.InOut)
          .start()
        new TWEEN.Tween(controls.target)
          .to({ x: 0, y: 0, z: 0 }, 1000)
          .easing(TWEEN.Easing.Quadratic.InOut)
          .start()
        break
    }
  } catch (error) {
    console.error('解析相机控制指令失败:', error)
  }
}

// 监听设备定位事件
function setupDeviceTracking() {
  console.log('设置设备跟踪事件监听器')
  window.addEventListener('device-locate', (event) => {
    if (event.detail && event.detail.deviceId) {
      console.log('Scene组件收到设备定位事件:', event.detail)
      findDevice(event.detail.deviceId)
    }
  })
}

// 组件挂载时初始化
onMounted(async () => {
  initBaseScene()
  renderer.domElement.addEventListener('click', onClick)
  window.addEventListener('resize', onWindowResize)
  window.addEventListener('keydown', onKeyDown)
  window.addEventListener('keyup', onKeyUp)
  window.addEventListener('camera-control', handleCameraControl)
  
  // 设置设备跟踪
  setupDeviceTracking()
  
  // 监听打开视频面板的事件
  emitter.on('open-video-panel', handleOpenVideoPanel)
  
  // 监听打开设备面板的事件
  emitter.on('open-device-panel', handleOpenDevicePanel)
  
  await scenes[currentScene.value].setup()
  animate()
})

// 组件卸载时清理资源
onUnmounted(() => {
  renderer?.dispose()
  controls?.dispose()
  renderer?.domElement.removeEventListener('click', onClick)
  window.removeEventListener('resize', onWindowResize)
  window.removeEventListener('keydown', onKeyDown)
  window.removeEventListener('keyup', onKeyUp)
  window.removeEventListener('camera-control', handleCameraControl)
  
  // 移除设备定位事件监听，确保使用正确的处理函数
  window.removeEventListener('device-locate', (e) => findDevice(e.detail?.deviceId))
  
  deviceStore.selectDevice(null) // 清除MQTT订阅
  
  // 清理事件监听
  emitter.off('open-video-panel', handleOpenVideoPanel)
  emitter.off('open-device-panel', handleOpenDevicePanel)
})

// 修改actuallyFindDevice函数，让它接受一个参数控制是否打开面板
function actuallyFindDevice(deviceId, shouldOpenPanel = true) {
  console.log('在当前场景查找设备:', deviceId, '是否打开面板:', shouldOpenPanel)
  
  // 使用设备配置管理器查找设备信息
  const deviceConfig = deviceConfigManager.findDevice(deviceId);
  
  if (!deviceConfig) {
    console.warn('在配置中未找到设备:', deviceId);
    return;
  }
  
  // 找到的目标设备
  let targetObj = null;
  
  // 获取模型名称
  const modelName = deviceConfig.model_name;
  console.log('查找模型名称:', modelName);
  
  // 在当前场景中遍历所有对象查找精确匹配
  scenes.waterInjection.objects.forEach(obj => {
    obj.traverse((node) => {
      if (node.name === modelName) {
        console.log('找到精确匹配设备:', node.name);
        targetObj = node;
      }
    });
  });
  
  if (targetObj) {
    console.log('即将移动摄像头到设备:', targetObj.name);
    setTimeout(() => {
      moveToTarget(targetObj, shouldOpenPanel);
    }, 100);
  } else {
    console.log('未找到设备:', deviceId);
  }
}

// 根据名称查找设备对象
function findDeviceByName(deviceName) {
  if (!deviceName) return null
  
  console.log('尝试查找设备:', deviceName)
  
  // 使用设备配置管理器查找设备信息
  const deviceConfig = deviceConfigManager.findDevice(deviceName);
  
  if (!deviceConfig) {
    console.warn('在配置中未找到设备:', deviceName);
    return null;
  }
  
  // 设备配置找到后，查找3D模型对象
  const modelName = deviceConfig.model_name;
  console.log('从配置找到设备，模型名称:', modelName);
  
  // 在当前场景的对象中查找
  let foundDevice = null;
  
  // 如果是注水管理场景，在场景对象中遍历查找
  if (currentScene.value === 'waterInjection') {
    // 获取场景中的所有对象
    const sceneObjects = scenes.waterInjection.objects;
    
    // 遍历场景中的所有对象，查找匹配的设备
    sceneObjects.forEach(obj => {
      obj.traverse((node) => {
        // 检查节点名称是否完全匹配模型名称
        if (node.name === modelName) {
          console.log('找到设备:', node.name);
          foundDevice = node;
        }
      });
    });
  }
  
  return foundDevice;
}

// 移动相机到设备位置
function moveToDevicePosition(device) {
  if (!device) return
  
  console.log('移动相机到设备:', device.name)
  
  // 确保当前选中的设备被取消选中
  deselectObject()
  
  // 获取设备的世界坐标
  const devicePosition = new THREE.Vector3()
  // 计算设备的世界坐标(考虑父级变换)
  device.updateMatrixWorld()
  devicePosition.setFromMatrixPosition(device.matrixWorld)
  
  console.log('设备世界坐标:', devicePosition)
  
  // 计算相机位置 - 在设备前方稍微上方的位置
  const distanceFromDevice = 150 // 距离设备的距离
  const heightAboveDevice = 80   // 高于设备的高度
  
  // 相机位置计算，从多个方向取平均值以获得更好的视图
  const cameraPosition = {
    x: devicePosition.x + distanceFromDevice * 0.7,
    y: devicePosition.y + heightAboveDevice,
    z: devicePosition.z + distanceFromDevice * 0.7
  }
  
  // 停止所有之前的移动动画
  TWEEN.removeAll()
  
  // 先移动相机位置，然后设置目标点
  new TWEEN.Tween(camera.position)
    .to(cameraPosition, 1800)  // 1.8秒完成移动，更平滑
    .easing(TWEEN.Easing.Cubic.InOut)
    .onComplete(() => {
      // 相机移动完成后，选中设备
      setTimeout(() => {
        selectDevice(device)
      }, 200)
    })
    .start()
  
  // 相机平滑朝向设备
  new TWEEN.Tween(controls.target)
    .to({
      x: devicePosition.x,
      y: devicePosition.y,
      z: devicePosition.z
    }, 1800)
    .easing(TWEEN.Easing.Cubic.InOut)
    .start()
}

// 自动选中设备
function selectDevice(device) {
  if (!device) return
  
  console.log('自动选中设备:', device.name)
  
  // 设置为当前选中对象
  selectedObject = device
  
  // 如果是抽油机组
  if (device.name?.match(/组\d{3}/)) {
    // 保存所有子对象的原始颜色
    device.traverse((child) => {
      if (child.isMesh) {
        child.userData.originalColor = child.material.color.clone()
      }
    })
    
    // 将所有网格添加到轮廓选择中
    const outlineObjects = []
    device.traverse((child) => {
      if (child.isMesh) {
        outlineObjects.push(child)
      }
    })
    outlinePass.selectedObjects = outlineObjects
    
    // 显示设备面板
    const vector = new THREE.Vector3()
    vector.setFromMatrixPosition(device.matrixWorld)
    vector.project(camera)
    
    const clickX = (vector.x * 0.5 + 0.5) * sceneContainer.value.clientWidth
    const clickY = (vector.y * -0.5 + 0.5) * sceneContainer.value.clientHeight
    
    const panelWidth = 400
    const estimatedHeight = currentPanelHeight.value || 600
    
    // 计算设备在屏幕上的相对位置
    const screenRatio = clickX / sceneContainer.value.clientWidth
    
    // 根据设备在屏幕上的相对位置动态调整面板位置
    let x, y
    
    // 注水管理场景：根据设备位置动态调整偏移
    if (screenRatio < 0.2) {
      // 设备在屏幕最左侧，面板显示在右侧，使用较大偏移
      x = clickX + 120
    } else if (screenRatio > 0.8) {
      // 设备在屏幕最右侧，面板显示在左侧，使用较大偏移
      x = clickX - panelWidth - 120
    } else if (screenRatio < 0.4) {
      // 设备在屏幕左侧区域，面板显示在右侧
      x = clickX + 80
    } else if (screenRatio > 0.6) {
      // 设备在屏幕右侧区域，面板显示在左侧
      x = clickX - panelWidth - 80
    } else {
      // 设备在屏幕中间区域，根据点击位置决定显示在左侧还是右侧
      x = clickX + 60
      if (x + panelWidth > window.innerWidth) {
        x = clickX - panelWidth - 60
      }
    }
    
    // 垂直位置根据设备高度调整
    y = clickY - estimatedHeight / 2
    
    // 优化边界检查逻辑
    // 垂直方向：确保面板完全在屏幕内
    if (y < 0) y = 10
    if (y + estimatedHeight > window.innerHeight) {
      y = window.innerHeight - estimatedHeight - 10
    }
    
    // 水平方向：允许面板部分超出屏幕但保证内容可见
    if (x < -panelWidth/2) x = -panelWidth/2
    if (x + panelWidth/2 > window.innerWidth) {
      x = window.innerWidth - panelWidth/2
    }
    
    devicePanelPosition.value = { x, y, visible: true }
    
    // 选中设备并开始MQTT订阅
    deviceStore.selectDevice({
      station: device.userData.station,
      wellname: device.userData.wellname,
      topic: device.userData.topic,
      id: device.userData.id
    })
  }
}

// 添加一个只定位设备但不打开面板的函数
function findDeviceWithoutPanel(deviceId) {
  console.log('开始定位设备但不打开面板:', deviceId)
  
  // 如果不是注水管理场景，先切换到该场景
  if (currentScene.value !== 'waterInjection') {
    console.log('切换到注水管理场景')
    handleSceneChange('waterInjection').then(() => {
      setTimeout(() => {
        actuallyFindDevice(deviceId, false) // 传入false表示不打开面板
      }, 3000) // 等待3秒确保场景加载完成
    })
  } else {
    actuallyFindDevice(deviceId, false) // 传入false表示不打开面板
  }
}

// 修改现有的findDevice函数，确保它会打开设备面板
function findDevice(deviceId) {
  console.log('开始查找设备并打开面板:', deviceId)
  
  // 如果不是注水管理场景，先切换到该场景
  if (currentScene.value !== 'waterInjection') {
    console.log('切换到注水管理场景')
    handleSceneChange('waterInjection').then(() => {
      setTimeout(() => {
        actuallyFindDevice(deviceId, true) // 传入true表示打开面板
      }, 3000) // 等待3秒确保场景加载完成
    })
  } else {
    actuallyFindDevice(deviceId, true) // 传入true表示打开面板
  }
}

// 最简单直接的摄像头移动方法
function moveToTarget(target, shouldOpenPanel = true) {
  // 先取消选中当前对象
  deselectObject()
  
  try {
    // 获取目标位置
    const pos = target.position.clone()
    console.log('目标位置:', pos, '是否打开面板:', shouldOpenPanel)
    
    // 计算在目标上方稍远的位置
    const cameraPos = {
      x: pos.x - 150,
      y: pos.y + 100,
      z: pos.z - 150
    }
    
    // 直接设置相机位置
    console.log('设置相机位置:', cameraPos)
    camera.position.set(cameraPos.x, cameraPos.y, cameraPos.z)
    
    // 设置相机目标
    controls.target.copy(pos)
    
    // 更新相机
    camera.updateProjectionMatrix()
    controls.update()
    
    // 强制渲染一帧
    renderer.render(scene, camera)
    
    // 等待片刻后选中对象
    setTimeout(() => {
      // 选中设备
      selectedObject = target
      
      // 添加到轮廓选择中
      if (target.isMesh) {
        outlinePass.selectedObjects = [target]
      } else {
        const outlineObjects = []
        target.traverse((child) => {
          if (child.isMesh) {
            outlineObjects.push(child)
          }
        })
        outlinePass.selectedObjects = outlineObjects
      }
      
      // 根据参数决定是否显示面板
      if (shouldOpenPanel) {
        // 显示面板
        showDevicePanel(target)
      }
      
      // 添加简单的闪烁特效
      addSimpleFlashEffect(pos)
      
      console.log('摄像头移动完成，设备面板显示状态:', shouldOpenPanel)
    }, 500)
  } catch (err) {
    console.error('移动摄像头出错:', err)
  }
}

// 添加简单的闪烁特效
function addSimpleFlashEffect(position) {
  // 创建一个简单的闪光球
  const geometry = new THREE.SphereGeometry(20, 16, 16)
  const material = new THREE.MeshBasicMaterial({
    color: 0xffff00,
    transparent: true,
    opacity: 0.8
  })
  
  const flash = new THREE.Mesh(geometry, material)
  flash.position.copy(position)
  scene.add(flash)
  
  // 简单的闪烁淡出
  let opacity = 0.8
  const interval = setInterval(() => {
    opacity -= 0.05
    material.opacity = opacity
    
    if (opacity <= 0) {
      clearInterval(interval)
      scene.remove(flash)
      geometry.dispose()
      material.dispose()
    }
  }, 100)
}

// 显示设备面板
function showDevicePanel(device) {
  // 获取设备在屏幕上的位置
  const vector = device.position.clone().project(camera)
  const x = (vector.x * 0.5 + 0.5) * sceneContainer.value.clientWidth
  const y = (vector.y * -0.5 + 0.5) * sceneContainer.value.clientHeight
  
  const panelWidth = 400
  const estimatedHeight = currentPanelHeight.value || 600
  
  // 简单的面板位置计算 - 默认在右侧
  let panelX = x + 100
  
  // 如果右侧空间不足，则显示在左侧
  if (panelX + panelWidth > window.innerWidth) {
    panelX = x - panelWidth - 100
  }
  
  // 垂直居中
  const panelY = y - estimatedHeight / 2
  
  // 确保面板在屏幕内
  const finalX = Math.max(10, Math.min(window.innerWidth - panelWidth - 10, panelX))
  const finalY = Math.max(10, Math.min(window.innerHeight - estimatedHeight - 10, panelY))
  
  // 设置面板位置
  devicePanelPosition.value = { 
    x: finalX, 
    y: finalY, 
    visible: true 
  }
  
  // 选中设备并开始MQTT订阅
  if (device.userData && device.userData.topic) {
    deviceStore.selectDevice({
      station: device.userData.station || "HN3S1",
      wellname: device.userData.wellname,
      topic: device.userData.topic,
      id: device.userData.id
    })
  }
}

// 关闭设备面板
function closeDevicePanel() {
  devicePanelPosition.value.visible = false;
  deselectObject();
}

// 关闭视频面板
const closeVideoPanel = () => {
  console.log('关闭视频面板')
  // 先设置状态为false，这样组件会触发beforeUnmount和unmounted钩子
  videoPanelVisible.value = false
  // 给组件一点时间完成清理
  setTimeout(() => {
    // 重置视频面板位置和大小到默认值
    videoPanelPosition.value = { x: window.innerWidth - 840, y: 20 }
    videoPanelSize.value = { width: 800, height: 550 }
  }, 100)
}

// 打开视频面板的处理函数
const handleOpenVideoPanel = (data) => {
  console.log('接收到打开视频面板事件:', data)
  
  // 如果提供了自定义位置，则使用它
  if (data && data.position) {
    videoPanelPosition.value = data.position
  } else {
    // 否则使用默认位置（右上角）
    videoPanelPosition.value = { x: window.innerWidth - 380, y: 20 }
  }
  
  // 如果提供了自定义大小，则使用它
  if (data && data.size) {
    videoPanelSize.value = data.size
  } else {
    // 否则使用默认大小
    videoPanelSize.value = { width: 480, height: 360 }
  }
  
  // 显示视频面板
  videoPanelVisible.value = true
  
  // 如果提供了设备ID，只定位设备并让其变亮，但不打开任何面板
  if (data && data.deviceId) {
    console.log(`仅定位设备并让其变亮，不打开任何面板: ${data.deviceId}`)
    // 调用专门的函数，只定位和高亮设备，不打开任何面板
    findDeviceAndHighlight(data.deviceId)
  }
}

// 添加一个新函数，只定位设备并高亮，但不打开任何面板
function findDeviceAndHighlight(deviceId) {
  console.log('开始定位设备并高亮，不打开任何面板:', deviceId)
  
  // 如果不是注水管理场景，先切换到该场景
  if (currentScene.value !== 'waterInjection') {
    console.log('切换到注水管理场景')
    handleSceneChange('waterInjection').then(() => {
      setTimeout(() => {
        // 使用一个特殊的参数调用，表示只要高亮不打开面板
        actuallyFindDeviceAndHighlight(deviceId)
      }, 3000) // 等待3秒确保场景加载完成
    })
  } else {
    actuallyFindDeviceAndHighlight(deviceId)
  }
}

// 专门的函数，只用于找到设备并高亮，不打开任何面板
function actuallyFindDeviceAndHighlight(deviceId) {
  console.log('在当前场景查找并高亮设备，但不打开面板:', deviceId)
  
  // 查找逻辑与actuallyFindDevice相同，但结果处理不同
  let targetObj = null
  let deviceNumber = deviceId
  
  // 统一设备ID格式（与actuallyFindDevice相同的逻辑）
  if (deviceId.startsWith('JH')) {
    deviceNumber = deviceId.substring(2)
  } else if (deviceId.startsWith('H')) {
    deviceNumber = deviceId.substring(1)
  } else if (deviceId.match(/^\d+$/)) {
    deviceNumber = deviceId
  } else if (deviceId.includes('HN15V')) {
    const match = deviceId.match(/HN15V(\d+)/)
    if (match) {
      deviceNumber = match[1]
    }
  }
  
  // 确保deviceNumber是3位数字格式
  deviceNumber = deviceNumber.replace(/\D/g, '') // 移除所有非数字字符
  if (deviceNumber) {
    // 如果数字大于8，取模8的结果
    const num = parseInt(deviceNumber)
    if (num > 8) {
      deviceNumber = (num % 8 || 8).toString().padStart(3, '0')
    } else {
      deviceNumber = deviceNumber.padStart(3, '0')
    }
  }
  
  // 构建搜索模式
  const searchPattern = `组${deviceNumber}`
  console.log('查找模式:', searchPattern)
  
  // 在当前场景中遍历所有对象查找精确匹配
  scenes.waterInjection.objects.forEach(obj => {
    obj.traverse((node) => {
      if (node.name === searchPattern) {
        console.log('找到精确匹配设备:', node.name)
        targetObj = node
      }
    })
  })
  
  if (targetObj) {
    console.log('即将移动摄像头到设备并高亮:', targetObj.name)
    setTimeout(() => {
      // 使用moveToTargetAndHighlight而不是moveToTarget，确保只高亮不打开面板
      moveToTargetAndHighlight(targetObj)
    }, 100)
  } else {
    // 备用逻辑，与actuallyFindDevice类似
    console.log('未找到设备:', deviceId, '尝试最后备用方法')
    
    // 尝试直接找到组001-008
    const simpleGroupNumber = deviceNumber.replace(/^0+/, '')
    if (parseInt(simpleGroupNumber) >= 1 && parseInt(simpleGroupNumber) <= 8) {
      const targetGroupName = `组${simpleGroupNumber.padStart(3, '0')}`
      console.log(`尝试查找特定组: ${targetGroupName}`)
      
      scenes.waterInjection.objects.forEach(obj => {
        obj.traverse((node) => {
          if (!targetObj && node.name === targetGroupName) {
            console.log('找到目标组:', node.name)
            targetObj = node
          }
        })
      })
      
      if (targetObj) {
        setTimeout(() => {
          moveToTargetAndHighlight(targetObj)
        }, 100)
      }
    }
  }
}

// 专门的摄像头移动和高亮方法，不打开任何面板
function moveToTargetAndHighlight(target) {
  // 先取消选中当前对象
  deselectObject()
  
  try {
    // 获取目标位置
    const pos = target.position.clone()
    console.log('目标位置:', pos, '只高亮不打开面板')
    
    // 计算在目标上方稍远的位置
    const cameraPos = {
      x: pos.x - 150,
      y: pos.y + 100,
      z: pos.z - 150
    }
    
    // 直接设置相机位置
    console.log('设置相机位置:', cameraPos)
    camera.position.set(cameraPos.x, cameraPos.y, cameraPos.z)
    
    // 设置相机目标
    controls.target.copy(pos)
    
    // 更新相机
    camera.updateProjectionMatrix()
    controls.update()
    
    // 强制渲染一帧
    renderer.render(scene, camera)
    
    // 等待片刻后选中对象（但不打开面板）
    setTimeout(() => {
      // 选中设备
      selectedObject = target
      
      // 添加到轮廓选择中实现高亮效果
      if (target.isMesh) {
        outlinePass.selectedObjects = [target]
      } else {
        const outlineObjects = []
        target.traverse((child) => {
          if (child.isMesh) {
            outlineObjects.push(child)
          }
        })
        outlinePass.selectedObjects = outlineObjects
      }
      
      // 添加简单的闪烁特效
      addSimpleFlashEffect(pos)
      
      console.log('摄像头移动完成，设备已高亮但未打开面板')
    }, 500)
  } catch (err) {
    console.error('移动摄像头出错:', err)
  }
}

// 打开设备面板的处理函数
const handleOpenDevicePanel = (data) => {
  console.log('接收到打开设备面板事件:', data)
  
  if (data && data.deviceId) {
    const deviceId = data.deviceId
    
    // 打开设备面板
    showDeviceInfo(deviceId)
  }
}

// 显示设备信息面板
function showDeviceInfo(deviceId) {
  console.log('显示设备信息面板:', deviceId)
  
  // 先查找设备，定位到设备位置
  findDevice(deviceId);
  
  // 获取设备对象
  const device = findDeviceByName(deviceId);
  
  if (device) {
    console.log('找到设备，显示面板:', device.name);
    selectDevice(device);
  } else {
    console.warn('未找到设备:', deviceId);
    
    // 如果没找到设备，尝试通过配置管理器查找
    const deviceConfig = deviceConfigManager.findDevice(deviceId);
    
    if (deviceConfig) {
      console.log('通过配置找到设备:', deviceConfig.display_name || deviceId);
      
      // 在指定位置显示设备面板
      devicePanelPosition.value = { 
        x: window.innerWidth / 2 - 200, 
        y: window.innerHeight / 2 - 300, 
        visible: true 
      };
      
      // 根据配置订阅MQTT主题
      deviceStore.selectDevice({
        model_name: deviceConfig.model_name,
        display_name: deviceConfig.display_name,
        topic: deviceConfig.topic,
        wellname: deviceConfig.display_name || deviceId  // 确保wellname也设置为友好名称
      });
    } else {
      console.error('设备未在配置中定义:', deviceId);
    }
  }
}
</script>

<style scoped>
.scene-container {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.loading-content {
  background-color: rgba(0, 0, 0, 0.05);
  padding: 2rem;
  border-radius: 8px;
  backdrop-filter: blur(10px);
  min-width: 300px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.loading-text {
  color: #333;
  text-align: center;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background-color: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background-color: #1890ff;
  transition: width 0.3s ease;
}

.progress-text {
  color: #333;
  text-align: center;
  font-size: 0.9rem;
}
</style>