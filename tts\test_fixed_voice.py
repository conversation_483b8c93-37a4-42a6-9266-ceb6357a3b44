#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试固定音色和文本预处理功能
"""

import requests
import json
import base64
import wave
import time
from pathlib import Path

# TTS服务地址
TTS_URL = "http://localhost:8003"

def test_text_preprocessing():
    """测试文本预处理功能"""
    print("=== 测试文本预处理功能 ===")
    
    # 测试Markdown文本
    markdown_text = """
# 这是一个标题

这是一段**粗体文本**和*斜体文本*。

- 列表项目1
- 列表项目2

[链接文本](https://example.com)

`代码片段`

```python
print("Hello World")
```

> 这是引用文本

包含English words和数字123的混合文本。
"""
    
    try:
        response = requests.post(
            f"{TTS_URL}/preprocess",
            json={"text": markdown_text},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("原始文本:")
            print(result["original_text"])
            print("\n处理后文本:")
            print(result["processed_text"])
            print(f"\n固定音色种子: {result['voice_seed']}")
            print(f"固定音频种子: {result['audio_seed']}")
        else:
            print(f"预处理测试失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"预处理测试出错: {e}")

def test_fixed_voice_tts():
    """测试固定音色TTS功能"""
    print("\n=== 测试固定音色TTS功能 ===")
    
    # 测试文本
    test_texts = [
        "这是第一段测试文本，包含中文和English单词以及数字123。",
        "这是第二段测试文本，应该使用相同的音色。",
        "# Markdown标题\n\n这是**粗体**文本，包含`代码`和[链接](url)。"
    ]
    
    for i, text in enumerate(test_texts):
        print(f"\n测试文本 {i+1}: {text[:30]}...")
        
        try:
            # 发送TTS请求
            response = requests.post(
                f"{TTS_URL}/tts",
                json={
                    "text": text,
                    "preprocess_text": True  # 启用文本预处理
                },
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result["success"]:
                    # 保存音频文件
                    audio_data = base64.b64decode(result["audio_base64"])
                    output_file = f"test_output_{i+1}.wav"
                    
                    with open(output_file, "wb") as f:
                        f.write(audio_data)
                    
                    print(f"音频已保存到: {output_file}")
                    print(f"音频时长: {result['duration']:.2f}秒")
                    print(f"采样率: {result['sample_rate']}Hz")
                    print(f"消息: {result['message']}")
                else:
                    print(f"TTS生成失败: {result['message']}")
            else:
                print(f"TTS请求失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"TTS测试出错: {e}")
        
        # 等待一下再进行下一个测试
        time.sleep(1)

def test_stream_tts():
    """测试流式TTS功能"""
    print("\n=== 测试流式TTS功能 ===")
    
    text = "这是流式TTS测试，包含**Markdown格式**和English words以及数字456。"
    
    try:
        response = requests.post(
            f"{TTS_URL}/tts/stream",
            json={
                "text": text,
                "preprocess_text": True
            },
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            # 保存流式音频
            with open("test_stream_output.wav", "wb") as f:
                f.write(response.content)
            
            print("流式音频已保存到: test_stream_output.wav")
            print(f"音频大小: {len(response.content)} 字节")
            
            # 检查响应头中的音色种子信息
            voice_seed = response.headers.get("X-Voice-Seed")
            if voice_seed:
                print(f"使用的音色种子: {voice_seed}")
        else:
            print(f"流式TTS请求失败: {response.status_code} - {response.text}")
            
    except Exception as e:
        print(f"流式TTS测试出错: {e}")

def check_service_status():
    """检查服务状态"""
    print("=== 检查TTS服务状态 ===")

    try:
        response = requests.get(f"{TTS_URL}/health")
        if response.status_code == 200:
            result = response.json()
            print(f"服务状态: {result['status']}")
            print(f"模型已加载: {result['model_loaded']}")
            print(f"使用设备: {result['device']}")
            print(f"已初始化: {result['initialized']}")
            print(f"默认音色: {result.get('default_voice', 'N/A')}")
            print(f"固定音色种子: {result.get('fixed_voice_seed', 'N/A')}")
            print(f"固定音频种子: {result.get('fixed_audio_seed', 'N/A')}")
            return True
        else:
            print(f"服务状态检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"无法连接到TTS服务: {e}")
        return False

def test_voice_configurations():
    """测试音色配置功能"""
    print("\n=== 测试音色配置功能 ===")

    try:
        response = requests.get(f"{TTS_URL}/voices")
        if response.status_code == 200:
            result = response.json()
            print("可用音色配置:")
            for voice_name, description in result["voices"].items():
                print(f"  - {voice_name}: {description}")

            print(f"\n默认音色: {result['default_voice']}")
            print("当前配置:")
            config = result["current_config"]
            for key, value in config.items():
                print(f"  {key}: {value}")
        else:
            print(f"获取音色配置失败: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"音色配置测试出错: {e}")

def test_different_voices():
    """测试不同音色配置"""
    print("\n=== 测试不同音色配置 ===")

    # 获取可用音色
    try:
        response = requests.get(f"{TTS_URL}/voices")
        if response.status_code != 200:
            print("无法获取音色列表")
            return

        voices = response.json()["voices"]
        test_text = "这是测试不同音色配置的文本，包含中文和English单词。"

        for voice_name in voices.keys():
            print(f"\n测试音色: {voice_name}")

            try:
                response = requests.post(
                    f"{TTS_URL}/tts",
                    json={
                        "text": test_text,
                        "voice_profile": voice_name,
                        "preprocess_text": True
                    },
                    headers={"Content-Type": "application/json"}
                )

                if response.status_code == 200:
                    result = response.json()
                    if result["success"]:
                        # 保存音频文件
                        audio_data = base64.b64decode(result["audio_base64"])
                        output_file = f"test_voice_{voice_name}.wav"

                        with open(output_file, "wb") as f:
                            f.write(audio_data)

                        print(f"  音频已保存到: {output_file}")
                        print(f"  音频时长: {result['duration']:.2f}秒")
                        print(f"  消息: {result['message']}")
                    else:
                        print(f"  生成失败: {result['message']}")
                else:
                    print(f"  请求失败: {response.status_code}")

            except Exception as e:
                print(f"  测试音色 {voice_name} 出错: {e}")

            time.sleep(1)  # 避免请求过快

    except Exception as e:
        print(f"测试不同音色出错: {e}")

def main():
    """主测试函数"""
    print("开始测试固定音色TTS服务...")

    # 检查服务状态
    if not check_service_status():
        print("TTS服务未运行，请先启动服务")
        return

    # 测试音色配置功能
    test_voice_configurations()

    # 测试文本预处理
    test_text_preprocessing()

    # 测试固定音色TTS
    test_fixed_voice_tts()

    # 测试不同音色配置
    test_different_voices()

    # 测试流式TTS
    test_stream_tts()

    print("\n=== 测试完成 ===")
    print("请检查生成的音频文件，验证以下功能：")
    print("1. 相同音色配置的音频应该音色一致")
    print("2. 不同音色配置的音频应该有明显差异")
    print("3. Markdown格式应该被正确处理")
    print("4. 英文单词和数字应该被正确朗读")

if __name__ == "__main__":
    main()
