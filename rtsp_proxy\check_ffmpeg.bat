@echo off
echo Checking FFmpeg installation...

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Check if FFmpeg is available
ffmpeg -version >nul 2>&1
if %errorlevel% equ 0 (
    echo SUCCESS: FFmpeg is installed and available.
    echo FFmpeg version details:
    ffmpeg -version | findstr "version"
) else (
    echo ERROR: FFmpeg is not installed or not in PATH.
    echo.
    echo Please install FFmpeg first:
    echo 1. Download from https://ffmpeg.org/download.html
    echo 2. Add it to your system PATH
    echo    or
    echo    Create a 'bin' folder in the rtsp_proxy directory and place ffmpeg.exe there
    echo.
)

REM Deactivate virtual environment
call deactivate

pause 