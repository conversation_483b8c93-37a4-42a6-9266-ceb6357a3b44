#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试A2日报等字母数字组合的朗读效果
"""

import requests
import json
import base64
import time

# TTS服务地址
TTS_URL = "http://localhost:8003"

def test_text_preprocessing_a2():
    """测试A2等字母数字组合的预处理效果"""
    print("=== 测试A2字母数字组合预处理 ===")
    
    test_texts = [
        "A2日报",
        "点击查看完整的作业区生产油井A2日报",
        "B1报告",
        "C3数据",
        "A2、B1、C3报告",
        "第A2版",
        "A2级别",
        "2A区域",
        "3B部门"
    ]
    
    for text in test_texts:
        print(f"\n原始文本: {text}")
        
        try:
            response = requests.post(
                f"{TTS_URL}/preprocess",
                json={"text": text},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"处理后: {result['processed_text']}")
            else:
                print(f"预处理失败: {response.status_code}")
                
        except Exception as e:
            print(f"预处理出错: {e}")

def test_a2_tts():
    """测试A2日报的TTS效果"""
    print("\n=== 测试A2日报TTS朗读 ===")
    
    test_texts = [
        "A2日报",
        "点击查看完整的作业区生产油井A2日报",
        "今天的A2日报已经生成",
        "请查看A2、B1、C3三个区域的日报"
    ]
    
    for i, text in enumerate(test_texts):
        print(f"\n测试文本 {i+1}: {text}")
        
        try:
            # 发送TTS请求，强制使用固定音色
            response = requests.post(
                f"{TTS_URL}/tts",
                json={
                    "text": text,
                    "voice_profile": "serious_male",  # 固定使用serious_male
                    "preprocess_text": True
                },
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result["success"]:
                    # 保存音频文件
                    audio_data = base64.b64decode(result["audio_base64"])
                    output_file = f"test_a2_{i+1}.wav"
                    
                    with open(output_file, "wb") as f:
                        f.write(audio_data)
                    
                    print(f"音频已保存到: {output_file}")
                    print(f"音频时长: {result['duration']:.2f}秒")
                    print(f"消息: {result['message']}")
                else:
                    print(f"TTS生成失败: {result['message']}")
            else:
                print(f"TTS请求失败: {response.status_code} - {response.text}")
                
        except Exception as e:
            print(f"TTS测试出错: {e}")
        
        time.sleep(1)

def test_voice_consistency():
    """测试音色一致性"""
    print("\n=== 测试音色一致性 ===")
    
    # 同一文本多次生成，验证音色是否一致
    text = "A2日报测试音色一致性"
    
    for i in range(3):
        print(f"\n第 {i+1} 次生成")
        
        try:
            response = requests.post(
                f"{TTS_URL}/tts",
                json={
                    "text": text,
                    "voice_profile": "serious_male",
                    "preprocess_text": True
                },
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                result = response.json()
                
                if result["success"]:
                    # 保存音频文件
                    audio_data = base64.b64decode(result["audio_base64"])
                    output_file = f"consistency_test_{i+1}.wav"
                    
                    with open(output_file, "wb") as f:
                        f.write(audio_data)
                    
                    print(f"音频已保存到: {output_file}")
                    print(f"消息: {result['message']}")
                else:
                    print(f"生成失败: {result['message']}")
            else:
                print(f"请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"一致性测试出错: {e}")
        
        time.sleep(1)

def check_service_config():
    """检查服务配置"""
    print("=== 检查服务配置 ===")
    
    try:
        # 检查健康状态
        response = requests.get(f"{TTS_URL}/health")
        if response.status_code == 200:
            result = response.json()
            print(f"服务状态: {result['status']}")
            print(f"默认音色: {result.get('default_voice', 'N/A')}")
            print(f"固定音色种子: {result.get('fixed_voice_seed', 'N/A')}")
            print(f"固定音频种子: {result.get('fixed_audio_seed', 'N/A')}")
        
        # 检查音色配置
        response = requests.get(f"{TTS_URL}/voices")
        if response.status_code == 200:
            result = response.json()
            print(f"\n当前音色配置: {result['default_voice']}")
            config = result["current_config"]
            print("配置参数:")
            for key, value in config.items():
                print(f"  {key}: {value}")
        
        return True
    except Exception as e:
        print(f"无法连接到服务: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试A2日报朗读功能...")
    
    # 检查服务配置
    if not check_service_config():
        print("TTS服务未运行或配置有误")
        return
    
    # 测试文本预处理
    test_text_preprocessing_a2()
    
    # 测试A2日报TTS
    test_a2_tts()
    
    # 测试音色一致性
    test_voice_consistency()
    
    print("\n=== 测试完成 ===")
    print("请播放生成的音频文件，检查：")
    print("1. A2是否被正确朗读为'A二'或'A2'")
    print("2. 所有音频的音色是否完全一致")
    print("3. 朗读是否清晰、准确")

if __name__ == "__main__":
    main()
