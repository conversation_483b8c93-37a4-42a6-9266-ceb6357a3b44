/**
 * ChatTTS 前端集成模块
 * 用于在前端项目中集成 ChatTTS 语音合成功能
 */

class ChatTTSClient {
    constructor(baseUrl = 'http://localhost:8001') {
        this.baseUrl = baseUrl;
        this.isReady = false;
        this.checkHealth();
    }

    /**
     * 检查服务健康状态
     */
    async checkHealth() {
        try {
            const response = await fetch(`${this.baseUrl}/health`);
            if (response.ok) {
                const data = await response.json();
                this.isReady = data.status === 'healthy' && data.model_loaded;
                console.log('ChatTTS服务状态:', data);
            }
        } catch (error) {
            console.error('ChatTTS服务连接失败:', error);
            this.isReady = false;
        }
    }

    /**
     * 文本转语音
     * @param {string} text - 要转换的文本
     * @param {Object} options - 转换选项
     * @returns {Promise<AudioBuffer>} 音频数据
     */
    async textToSpeech(text, options = {}) {
        if (!this.isReady) {
            throw new Error('ChatTTS服务未就绪');
        }

        const requestData = {
            text: text,
            voice_seed: options.voiceSeed || 42,
            temperature: options.temperature || 0.3,
            top_p: options.topP || 0.7,
            top_k: options.topK || 20,
            refine_text: options.refineText !== false
        };

        try {
            const response = await fetch(`${this.baseUrl}/tts`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error(`TTS请求失败: ${response.status}`);
            }

            const result = await response.json();
            
            if (!result.success) {
                throw new Error(result.message);
            }

            // 解码base64音频数据
            const audioData = atob(result.audio_base64);
            const audioBytes = new Uint8Array(audioData.length);
            for (let i = 0; i < audioData.length; i++) {
                audioBytes[i] = audioData.charCodeAt(i);
            }

            return {
                audioBytes: audioBytes,
                duration: result.duration,
                sampleRate: result.sample_rate,
                audioUrl: `data:audio/wav;base64,${result.audio_base64}`
            };

        } catch (error) {
            console.error('TTS转换失败:', error);
            throw error;
        }
    }

    /**
     * 播放文本语音
     * @param {string} text - 要播放的文本
     * @param {Object} options - 播放选项
     * @returns {Promise<HTMLAudioElement>} 音频元素
     */
    async playText(text, options = {}) {
        try {
            const audioData = await this.textToSpeech(text, options);
            
            const audio = new Audio(audioData.audioUrl);
            
            // 设置音频属性
            if (options.volume !== undefined) {
                audio.volume = Math.max(0, Math.min(1, options.volume));
            }
            
            if (options.autoplay !== false) {
                await audio.play();
            }

            return audio;

        } catch (error) {
            console.error('播放文本语音失败:', error);
            throw error;
        }
    }

    /**
     * 下载语音文件
     * @param {string} text - 要转换的文本
     * @param {string} filename - 文件名
     * @param {Object} options - 转换选项
     */
    async downloadAudio(text, filename = 'tts_output.wav', options = {}) {
        try {
            const audioData = await this.textToSpeech(text, options);
            
            // 创建下载链接
            const link = document.createElement('a');
            link.href = audioData.audioUrl;
            link.download = filename;
            
            // 触发下载
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

        } catch (error) {
            console.error('下载音频失败:', error);
            throw error;
        }
    }

    /**
     * 流式获取音频
     * @param {string} text - 要转换的文本
     * @param {Object} options - 转换选项
     * @returns {Promise<Blob>} 音频Blob
     */
    async getAudioStream(text, options = {}) {
        if (!this.isReady) {
            throw new Error('ChatTTS服务未就绪');
        }

        const requestData = {
            text: text,
            voice_seed: options.voiceSeed || 42,
            temperature: options.temperature || 0.3,
            top_p: options.topP || 0.7,
            top_k: options.topK || 20,
            refine_text: options.refineText !== false
        };

        try {
            const response = await fetch(`${this.baseUrl}/tts/stream`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });

            if (!response.ok) {
                throw new Error(`流式TTS请求失败: ${response.status}`);
            }

            return await response.blob();

        } catch (error) {
            console.error('获取音频流失败:', error);
            throw error;
        }
    }
}

/**
 * Vue.js 集成示例
 */
const ChatTTSVueMixin = {
    data() {
        return {
            ttsClient: null,
            isTTSReady: false,
            isPlaying: false
        };
    },

    async mounted() {
        this.ttsClient = new ChatTTSClient();
        
        // 等待服务就绪
        const checkReady = async () => {
            await this.ttsClient.checkHealth();
            this.isTTSReady = this.ttsClient.isReady;
            
            if (!this.isTTSReady) {
                setTimeout(checkReady, 2000); // 2秒后重试
            }
        };
        
        await checkReady();
    },

    methods: {
        async playMessageAudio(message, options = {}) {
            if (!this.isTTSReady || this.isPlaying) {
                return;
            }

            try {
                this.isPlaying = true;
                const audio = await this.ttsClient.playText(message, options);
                
                audio.addEventListener('ended', () => {
                    this.isPlaying = false;
                });

                audio.addEventListener('error', () => {
                    this.isPlaying = false;
                });

            } catch (error) {
                this.isPlaying = false;
                console.error('播放语音失败:', error);
            }
        },

        async downloadMessageAudio(message, filename) {
            if (!this.isTTSReady) {
                return;
            }

            try {
                await this.ttsClient.downloadAudio(message, filename);
            } catch (error) {
                console.error('下载语音失败:', error);
            }
        }
    }
};

/**
 * 在ChatPanel.vue中的集成示例
 */
const ChatPanelTTSIntegration = {
    // 在ChatPanel组件中添加TTS功能
    
    // 1. 在script setup中添加
    setup() {
        const ttsClient = ref(null);
        const isTTSEnabled = ref(false);
        
        onMounted(async () => {
            ttsClient.value = new ChatTTSClient();
            await ttsClient.value.checkHealth();
            isTTSEnabled.value = ttsClient.value.isReady;
        });
        
        return {
            ttsClient,
            isTTSEnabled
        };
    },

    // 2. 在methods中添加语音播放方法
    methods: {
        async playAssistantMessage(messageContent) {
            if (!this.isTTSEnabled || !this.ttsClient) {
                return;
            }

            try {
                // 清理HTML标签，只保留纯文本
                const textContent = this.extractTextFromHTML(messageContent);
                
                if (textContent.trim()) {
                    await this.ttsClient.playText(textContent, {
                        voiceSeed: 42,
                        temperature: 0.3,
                        volume: 0.8
                    });
                }
            } catch (error) {
                console.error('播放助手消息语音失败:', error);
            }
        },

        extractTextFromHTML(html) {
            // 创建临时DOM元素来提取纯文本
            const temp = document.createElement('div');
            temp.innerHTML = html;
            return temp.textContent || temp.innerText || '';
        },

        // 在消息渲染完成后自动播放（可选）
        async onMessageRendered(message) {
            if (message.role === 'assistant' && this.autoPlayTTS) {
                // 延迟一点时间确保消息完全渲染
                setTimeout(() => {
                    this.playAssistantMessage(message.content);
                }, 500);
            }
        }
    }
};

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ChatTTSClient,
        ChatTTSVueMixin,
        ChatPanelTTSIntegration
    };
}
