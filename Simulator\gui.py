from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QPushButton, QLabel, QLineEdit, QTextEdit,
                             QSpinBox, QGroupBox, QListWidget, QListWidgetItem,
                             QScrollArea, QGridLayout, QMessageBox)
from PyQt6.QtCore import QTimer, Qt, pyqtSignal
import json


class DeviceItem(QWidget):
    """设备项组件"""

    def __init__(self, station, wellname):
        super().__init__()
        self.layout = QHBoxLayout(self)
        self.layout.setContentsMargins(5, 2, 5, 2)

        # 设备信息
        self.info_label = QLabel(f"{station}/{wellname}")
        self.layout.addWidget(self.info_label)

        # 状态标签
        self.status_label = QLabel("已停止")
        self.layout.addWidget(self.status_label)

        # 设置布局
        self.layout.addStretch()

    def update_status(self, status):
        """更新发布状态
        status: 0-已停止, 1-发布中, 2-发布成功
        """
        status_text = {
            0: "已停止",
            1: "发布成功--发布中",
            2: "发布成功"
        }
        self.status_label.setText(status_text.get(status, "已停止"))


class MainWindow(QMainWindow):
    # 定义信号
    start_device = pyqtSignal(str, str)  # 参数：station, wellname
    stop_device = pyqtSignal(str, str)   # 参数：station, wellname

    def __init__(self):
        super().__init__()
        self.setWindowTitle("MQTT数据仿真器")
        self.setMinimumSize(1200, 800)

        # 创建主窗口部件和布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        layout = QHBoxLayout(main_widget)

        # 左侧面板 - 设备列表和添加设备控制
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # 设备列表
        device_group = QGroupBox("设备列表")
        device_layout = QVBoxLayout()

        self.device_list = QListWidget()
        self.device_list.setMinimumWidth(300)
        self.device_list.setSelectionMode(QListWidget.SelectionMode.ExtendedSelection)  # 启用多选模式
        device_layout.addWidget(self.device_list)

        # 批量控制按钮
        batch_control_layout = QHBoxLayout()
        self.start_all_btn = QPushButton("全部开始")
        self.stop_all_btn = QPushButton("全部停止")
        self.start_all_btn.clicked.connect(self.start_all_devices)
        self.stop_all_btn.clicked.connect(self.stop_all_devices)
        self.start_all_btn.setEnabled(False)  # 初始状态禁用
        self.stop_all_btn.setEnabled(False)   # 初始状态禁用
        batch_control_layout.addWidget(self.start_all_btn)
        batch_control_layout.addWidget(self.stop_all_btn)
        device_layout.addLayout(batch_control_layout)

        # 添加设备按钮
        add_device_btn = QPushButton("添加设备")
        add_device_btn.clicked.connect(self.add_device)
        device_layout.addWidget(add_device_btn)

        device_group.setLayout(device_layout)
        left_layout.addWidget(device_group)

        # MQTT连接配置
        conn_group = QGroupBox("MQTT连接配置")
        conn_layout = QGridLayout()

        self.host_input = QLineEdit("************")
        self.port_input = QLineEdit("8083")
        self.path_input = QLineEdit("/mqtt")

        conn_layout.addWidget(QLabel("服务器地址:"), 0, 0)
        conn_layout.addWidget(self.host_input, 0, 1)
        conn_layout.addWidget(QLabel("端口:"), 1, 0)
        conn_layout.addWidget(self.port_input, 1, 1)
        conn_layout.addWidget(QLabel("WebSocket路径:"), 2, 0)
        conn_layout.addWidget(self.path_input, 2, 1)

        self.connect_btn = QPushButton("连接")
        conn_layout.addWidget(self.connect_btn, 3, 0, 1, 2)

        conn_group.setLayout(conn_layout)
        left_layout.addWidget(conn_group)

        layout.addWidget(left_panel)

        # 右侧面板 - 设备配置和数据预览
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # 设备配置区域
        config_group = QGroupBox("设备配置")
        config_layout = QGridLayout()

        self.station_input = QLineEdit("HN3S1")
        self.wellname_input = QLineEdit("HN15V25")
        self.interval_spin = QSpinBox()
        self.interval_spin.setRange(1, 60)
        self.interval_spin.setValue(20)  # 默认20秒

        config_layout.addWidget(QLabel("场站:"), 0, 0)
        config_layout.addWidget(self.station_input, 0, 1)
        config_layout.addWidget(QLabel("井名:"), 1, 0)
        config_layout.addWidget(self.wellname_input, 1, 1)
        config_layout.addWidget(QLabel("发布间隔(秒):"), 2, 0)
        config_layout.addWidget(self.interval_spin, 2, 1)

        # 控制按钮
        button_layout = QHBoxLayout()
        self.start_btn = QPushButton("开始发布")
        self.stop_btn = QPushButton("停止发布")
        self.start_btn.setEnabled(False)  # 初始状态禁用
        self.stop_btn.setEnabled(False)
        button_layout.addWidget(self.start_btn)
        button_layout.addWidget(self.stop_btn)

        config_layout.addLayout(button_layout, 3, 0, 1, 2)
        config_group.setLayout(config_layout)
        right_layout.addWidget(config_group)

        # 数据预览区域
        preview_group = QGroupBox("数据预览")
        preview_layout = QVBoxLayout()

        self.preview_text = QTextEdit()
        self.preview_text.setReadOnly(True)
        preview_layout.addWidget(self.preview_text)

        preview_group.setLayout(preview_layout)
        right_layout.addWidget(preview_group)

        layout.addWidget(right_panel)

        # 设备定时器字典
        self.device_timers = {}

        # 连接信号
        self.device_list.currentItemChanged.connect(self.on_device_selected)
        # 加载配置
        self.load_config()

    def add_device(self):
        """添加新设备到列表"""
        station = self.station_input.text()
        wellname = self.wellname_input.text()

        # 检查是否已存在
        for i in range(self.device_list.count()):
            item = self.device_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == (station, wellname):
                QMessageBox.warning(self, "警告", "设备已存在")
                return

        # 创建新的列表项
        item = QListWidgetItem()
        device_widget = DeviceItem(station, wellname)
        item.setSizeHint(device_widget.sizeHint())
        item.setData(Qt.ItemDataRole.UserRole, (station, wellname))

        self.device_list.addItem(item)
        self.device_list.setItemWidget(item, device_widget)

        # 创建设备定时器
        timer = QTimer()
        self.device_timers[(station, wellname)] = timer

    def on_device_selected(self, current, previous):
        """处理设备选择变化"""
        # 先同步所有设备状态
        self.sync_device_states()
        
        selected_items = self.device_list.selectedItems()
        if not selected_items:
            return
            
        # 使用第一个选中项更新配置面板
        station, wellname = selected_items[0].data(Qt.ItemDataRole.UserRole)
        self.station_input.setText(station)
        self.wellname_input.setText(wellname)

        # 更新按钮状态
        # 如果任何选中的设备正在发布，则启用停止按钮
        any_active = False
        all_inactive = True
        
        for item in selected_items:
            station, wellname = item.data(Qt.ItemDataRole.UserRole)
            timer = self.device_timers.get((station, wellname))
            if timer:
                if timer.isActive():
                    any_active = True
                    all_inactive = False
                    break

        # 只有在连接按钮显示"断开"时（即已连接状态）才启用开始发布按钮
        is_connected = self.connect_btn.text() == "断开"
        self.start_btn.setEnabled(all_inactive and len(selected_items) > 0 and is_connected)
        self.stop_btn.setEnabled(any_active)

    def get_mqtt_config(self):
        """获取MQTT配置"""
        return {
            "host": self.host_input.text(),
            "port": int(self.port_input.text()),
            "path": self.path_input.text()
        }

    def get_selected_devices(self):
        """获取所有选中的设备配置"""
        selected_devices = []
        for item in self.device_list.selectedItems():
            selected_devices.append(item.data(Qt.ItemDataRole.UserRole))
        return selected_devices

    def get_selected_device(self):
        """获取当前选中的设备配置（兼容旧代码）"""
        items = self.device_list.selectedItems()
        if items:
            return items[0].data(Qt.ItemDataRole.UserRole)
        return None

    def get_publish_interval(self):
        """获取发布间隔"""
        return self.interval_spin.value()

    def update_preview(self, data):
        """更新数据预览区域"""
        formatted_json = json.dumps(data, indent=2, ensure_ascii=False)
        self.preview_text.setText(formatted_json)

    def update_device_status(self, station, wellname, status):
        """更新设备发布状态
        status: 0-已停止, 1-发布中, 2-已发布
        """
        for i in range(self.device_list.count()):
            item = self.device_list.item(i)
            if item.data(Qt.ItemDataRole.UserRole) == (station, wellname):
                device_widget = self.device_list.itemWidget(item)
                device_widget.update_status(status)
                # 确保定时器状态与显示状态一致
                timer = self.device_timers.get((station, wellname))
                if timer and status == 0 and timer.isActive():
                    # 只在需要停止时处理定时器
                    timer.stop()
                break

    def sync_device_states(self):
        """同步所有设备的状态"""
        for i in range(self.device_list.count()):
            item = self.device_list.item(i)
            station, wellname = item.data(Qt.ItemDataRole.UserRole)
            device_widget = self.device_list.itemWidget(item)
            timer = self.device_timers.get((station, wellname))
            if timer:
                # 获取当前显示的状态文本
                status_text = device_widget.status_label.text()
                # 获取定时器实际状态
                timer_active = timer.isActive()
                
                # 根据定时器状态设置显示状态
                if not timer_active and status_text != "已停止":
                    self.update_device_status(station, wellname, 0)
                elif timer_active and status_text == "已停止":
                    self.update_device_status(station, wellname, 1)

    def save_config(self):
        """保存当前配置到文件"""
        config = {
            "mqtt": self.get_mqtt_config(),
            "devices": []
        }

        # 保存设备列表
        for i in range(self.device_list.count()):
            item = self.device_list.item(i)
            station, wellname = item.data(Qt.ItemDataRole.UserRole)
            config["devices"].append({
                "station": station,
                "wellname": wellname
            })

        with open("config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

    def load_config(self):
        """从文件加载配置"""
        try:
            with open("config.json", "r", encoding="utf-8") as f:
                config = json.load(f)

            # 加载MQTT配置
            self.host_input.setText(config["mqtt"]["host"])
            self.port_input.setText(str(config["mqtt"]["port"]))
            self.path_input.setText(config["mqtt"]["path"])

            # 加载设备列表
            for device in config["devices"]:
                self.station_input.setText(device["station"])
                self.wellname_input.setText(device["wellname"])
                self.add_device()

        except FileNotFoundError:
            pass

    def closeEvent(self, event):
        """窗口关闭事件处理"""
        self.save_config()
        event.accept()

    def start_all_devices(self):
        """启动所有设备的发布"""
        # 先同步所有设备状态
        self.sync_device_states()
        
        for i in range(self.device_list.count()):
            item = self.device_list.item(i)
            station, wellname = item.data(Qt.ItemDataRole.UserRole)
            timer = self.device_timers.get((station, wellname))
            if timer and not timer.isActive():
                # 触发设备开始发布的信号
                self.start_device.emit(station, wellname)
                self.update_device_status(station, wellname, 1)

    def stop_all_devices(self):
        """停止所有设备的发布"""
        # 先同步所有设备状态
        self.sync_device_states()
        
        for i in range(self.device_list.count()):
            item = self.device_list.item(i)
            station, wellname = item.data(Qt.ItemDataRole.UserRole)
            timer = self.device_timers.get((station, wellname))
            if timer and timer.isActive():
                # 触发设备停止发布的信号
                self.stop_device.emit(station, wellname)
                self.update_device_status(station, wellname, 0)
