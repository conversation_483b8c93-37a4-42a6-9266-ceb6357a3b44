#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from typing import Dict, Any
from .redmine_config import redmine_config

logger = logging.getLogger(__name__)

async def get_projects() -> Dict[str, Any]:
    """
    获取Redmine项目
    
    获取所有项目的基本信息。
    """
    try:
        redmine = redmine_config.get_client()
        
        projects = list(redmine.project.all())
        
        # 转换为可序列化的字典列表
        result = []
        
        for project in projects:
            data = {
                "id": getattr(project, 'id', 0),
                "name": getattr(project, 'name', '未知项目'),
                "identifier": getattr(project, 'identifier', ''),
                "description": getattr(project, 'description', ''),
                "created_on": getattr(project, 'created_on', '').strftime("%Y-%m-%d") if hasattr(project, 'created_on') else '',
                "updated_on": getattr(project, 'updated_on', '').strftime("%Y-%m-%d") if hasattr(project, 'updated_on') else '',
                "status": getattr(project, 'status', 1),
            }
            
            # 获取父项目信息
            if hasattr(project, 'parent') and project.parent is not None:
                data["parent"] = {
                    "id": getattr(project.parent, 'id', 0),
                    "name": getattr(project.parent, 'name', '未知项目')
                }
            else:
                data["parent"] = None
                
            result.append(data)
        
        return {
            "projects": result,
            "count": len(result)
        }
        
    except Exception as e:
        logger.error(f"获取项目失败: {str(e)}")
        raise Exception(f"获取项目失败: {str(e)}") 