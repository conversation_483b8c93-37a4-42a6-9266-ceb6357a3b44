#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import logging
import traceback
from typing import Optional, Dict, Any, Union

# 配置日志
logging.basicConfig(level=logging.DEBUG, 
                   format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

logger.debug("正在启动Redmine MCP服务器...")

# 检查并尝试安装缺失的依赖
REQUIRED_PACKAGES = {
    "fastmcp": "fastmcp",
    "redminelib": "python-redmine",
    "yaml": "pyyaml"
}

missing_packages = []
for module, package in REQUIRED_PACKAGES.items():
    try:
        __import__(module)
        logger.debug(f"检查依赖: {module} - 已安装")
    except ImportError:
        missing_packages.append(package)
        logger.error(f"检查依赖: {module} - 未安装")

if missing_packages:
    logger.error(f"缺少以下依赖包: {', '.join(missing_packages)}")
    logger.error("请运行以下命令安装缺失的依赖: pip install " + " ".join(missing_packages))
    logger.error("或者运行 setup_redmine.py 脚本自动安装依赖")
    sys.exit(1)

# 导入所需的包
from pydantic import BaseModel, Field
from fastmcp import FastMCP, Context

try:
    # 导入模块化组件
    logger.debug("导入Redmine工具...")
    from redmine_tools import (
        redmine_config,
        get_time_entries,
        get_issues, get_issue_statuses, create_issue, get_trackers,
        get_projects,
        get_users,
        get_wiki_page, update_wiki_page, WikiPageData,
        get_version, get_enumerations, get_custom_fields,
        get_recent_issues, ProjectOption
    )
    from redmine_tools.utils import search_redmine as search_redmine_api
    
    logger.debug("Redmine工具导入成功")
except Exception as e:
    error_msg = f"导入Redmine工具时出错: {str(e)}"
    logger.error(error_msg)
    logger.error(traceback.format_exc())
    
    # 尝试确定问题的原因
    if "config.yaml" in str(e):
        logger.error("配置文件不存在或无法读取。请运行 setup_redmine.py 创建配置文件。")
    elif "No module named" in str(e):
        logger.error("缺少必要的Python模块。请运行 setup_redmine.py 安装缺失的依赖。")
    elif "Connection" in str(e):
        logger.error("无法连接到Redmine服务器。请检查网络连接和API密钥。")
    
    sys.exit(1)

logger.debug("创建FastMCP服务器...")

# 创建FastMCP服务器
mcp = FastMCP("RedmineReportTool")

# 导入数据模型
class TimeRange(BaseModel):
    start_date: str = Field(..., description="开始日期 (YYYY-MM-DD)")
    end_date: str = Field(..., description="结束日期 (YYYY-MM-DD)")

class ProjectFilter(BaseModel):
    project_id: Optional[int] = Field(None, description="项目ID")
    keyword: Optional[str] = Field(None, description="关键词")

@mcp.tool()
async def get_redmine_time_entries(time_range: TimeRange) -> Dict[str, Any]:
    """
    获取Redmine工时记录
    
    获取指定时间范围内的所有工时记录。
    """
    try:
        return await get_time_entries(time_range)
    except Exception as e:
        logger.error(f"获取工时记录失败: {str(e)}")
        return {"error": str(e), "message": "获取工时记录失败"}

@mcp.tool()
async def get_redmine_issues(time_range: TimeRange, filter_by: Optional[ProjectFilter] = None) -> Dict[str, Any]:
    """
    获取Redmine问题
    
    获取指定时间范围内创建或更新的问题。可选择按项目ID或关键词过滤。
    """
    try:
        return await get_issues(time_range, filter_by)
    except Exception as e:
        logger.error(f"获取问题失败: {str(e)}")
        return {"error": str(e), "message": "获取问题失败"}

@mcp.tool()
async def get_redmine_projects() -> Dict[str, Any]:
    """
    获取Redmine项目
    
    获取所有项目的基本信息。
    """
    try:
        return await get_projects()
    except Exception as e:
        logger.error(f"获取项目失败: {str(e)}")
        return {"error": str(e), "message": "获取项目失败"}

@mcp.tool()
async def get_redmine_users() -> Dict[str, Any]:
    """
    获取Redmine用户
    
    获取所有用户的基本信息。
    """
    try:
        return await get_users()
    except Exception as e:
        logger.error(f"获取用户失败: {str(e)}")
        return {"error": str(e), "message": "获取用户失败"}

@mcp.resource("redmine://status")
def get_redmine_status() -> Dict[str, Any]:
    """
    获取Redmine状态
    
    返回Redmine服务器的连接状态和版本信息。
    """
    logger.info("访问资源: redmine://status")
    try:
        return redmine_config.get_status()
    except Exception as e:
        logger.error(f"获取状态失败: {str(e)}")
        return {"status": "error", "message": str(e)}

@mcp.resource("redmine://config")
def get_redmine_config_info() -> Dict[str, Any]:
    """
    获取Redmine配置
    
    返回Redmine的当前配置信息。
    """
    logger.info("访问资源: redmine://config")
    try:
        return redmine_config.get_config_info()
    except Exception as e:
        logger.error(f"获取配置失败: {str(e)}")
        return {"status": "error", "message": str(e)}

@mcp.resource("redmine://help")
def get_redmine_help() -> str:
    """
    获取Redmine帮助
    
    返回关于如何使用Redmine工具的帮助信息。
    """
    logger.info("访问资源: redmine://help")
    
    return """
# Redmine报表工具使用说明

## 工具功能
1. **get_redmine_time_entries**: 获取指定时间范围内的工时记录。
2. **get_redmine_issues**: 获取指定时间范围内创建或更新的问题。
3. **get_redmine_projects**: 获取所有项目的基本信息。
4. **get_redmine_users**: 获取所有用户的基本信息。
5. **get_redmine_version**: 获取Redmine服务器版本信息。
6. **search_redmine**: 使用Redmine的搜索API进行全文搜索。
7. **get_redmine_enumerations**: 获取枚举类型数据，如优先级、活动类型等。
8. **get_redmine_issue_statuses**: 获取所有可用的问题状态。
9. **create_redmine_issue**: 创建新的Redmine问题。
10. **get_redmine_custom_fields**: 获取系统中定义的所有自定义字段。
11. **get_redmine_wiki_page**: 获取指定项目中的Wiki页面内容。
12. **update_redmine_wiki_page**: 更新或创建指定项目中的Wiki页面。
13. **get_redmine_trackers**: 获取系统中定义的所有跟踪器。
14. **get_redmine_recent_issues**: 获取最新问题列表，可以指定项目ID、天数、状态等过滤条件。

## 资源
1. **redmine://status**: 获取Redmine服务器的连接状态。
2. **redmine://config**: 获取Redmine的当前配置信息。
3. **redmine://help**: 获取帮助信息。

## 使用示例
```python
# 获取最近一周的工时记录
result = await get_redmine_time_entries({
    "start_date": "2025-04-01",
    "end_date": "2025-04-07"
})

# 获取指定项目的问题
issues = await get_redmine_issues({
    "start_date": "2025-04-01",
    "end_date": "2025-04-07"
}, {
    "project_id": 1,
    "keyword": "bug"
})

# 获取Redmine版本信息
version_info = await get_redmine_version()

# 在Redmine中搜索
search_results = await search_redmine("关键词", titles_only=False, limit=20)

# 获取问题优先级枚举
priorities = await get_redmine_enumerations("issue_priorities")

# 创建新问题
new_issue = await create_redmine_issue(
    project_id=1, 
    subject="测试问题", 
    description="这是一个测试问题的详细描述",
    priority_id=2
)

# 获取Wiki页面
wiki_page = await get_redmine_wiki_page(project_id="test-project", title="HomePage")

# 更新Wiki页面
updated_wiki = await update_redmine_wiki_page({
    "project_id": "test-project",
    "title": "HomePage",
    "text": "# 主页\\n\\n这是更新后的Wiki页面内容",
    "comments": "更新了页面内容"
})

# 获取最新问题列表
recent_issues = await get_redmine_recent_issues(
    project_id=9,  # 可选，不填则获取所有项目
    limit=20,      # 可选，默认20条
    days=30,       # 可选，默认30天
    status_id='*'  # 可选，默认所有状态
)
```

## 注意事项
1. 日期格式为: YYYY-MM-DD
2. 项目ID可以通过get_redmine_projects获取
3. 用户ID可以通过get_redmine_users获取
4. 跟踪器ID可以通过get_redmine_trackers获取
5. 问题状态ID可以通过get_redmine_issue_statuses获取
6. 优先级ID可以通过get_redmine_enumerations("issue_priorities")获取
7. 自定义字段ID可以通过get_redmine_custom_fields获取
"""

@mcp.prompt("weekly_report")
def weekly_report_prompt() -> str:
    """生成周报的提示词"""
    return """
你是一位项目管理专家，擅长分析项目数据并生成周报。

请根据以下数据生成一份项目周报：
1. 项目进展情况
2. 团队工作量统计
3. 存在的问题和风险
4. 下周工作计划

要求：
- 使用中文
- 内容要具体、数据精确
- 重点突出项目进展和风险
- 格式为Markdown
"""

@mcp.prompt("workload_analysis")
def workload_analysis_prompt() -> str:
    """生成工作量分析的提示词"""
    return """
你是一位工作量分析专家，擅长分析团队工作数据。

请根据以下工时数据分析团队工作量情况：
1. 各成员工作量统计
2. 各项目工作量分布
3. 工作类型分布
4. 是否存在工作量不均衡

要求：
- 使用中文
- 内容要简洁清晰
- 突出异常情况
- 给出合理化建议
- 格式为Markdown
"""

@mcp.tool()
async def get_redmine_version() -> Dict[str, Any]:
    """
    获取Redmine版本信息
    
    返回当前连接的Redmine服务器版本信息。
    """
    try:
        return await get_version()
    except Exception as e:
        logger.error(f"获取版本失败: {str(e)}")
        return {"error": str(e), "message": "获取版本失败"}

@mcp.tool()
async def search_redmine(query: str, titles_only: bool = False, limit: int = 10) -> Dict[str, Any]:
    """
    在Redmine中搜索
    
    使用Redmine的搜索API进行全文搜索。
    """
    try:
        return await search_redmine_api(query, titles_only, limit)
    except Exception as e:
        logger.error(f"搜索失败: {str(e)}")
        return {"error": str(e), "message": "搜索失败"}

@mcp.tool()
async def get_redmine_enumerations(enumeration_type: str = "issue_priorities") -> Dict[str, Any]:
    """
    获取Redmine枚举类型数据
    
    获取指定枚举类型的所有值。支持的类型包括：issue_priorities, time_entry_activities, document_categories
    """
    return await get_enumerations(enumeration_type)

@mcp.tool()
async def get_redmine_issue_statuses() -> Dict[str, Any]:
    """
    获取Redmine问题状态
    
    获取所有可用的问题状态。
    """
    return await get_issue_statuses()

@mcp.tool()
async def create_redmine_issue(
    project_id: int, 
    subject: str, 
    description: str = "", 
    assigned_to_id: Optional[int] = None, 
    tracker_id: Optional[int] = None,
    status_id: Optional[int] = None,
    priority_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    创建Redmine问题
    
    在Redmine中创建一个新问题。
    """
    return await create_issue(
        project_id, subject, description, 
        assigned_to_id, tracker_id, status_id, priority_id
    )

@mcp.tool()
async def get_redmine_custom_fields() -> Dict[str, Any]:
    """
    获取Redmine自定义字段
    
    获取系统中定义的所有自定义字段。
    """
    return await get_custom_fields()

@mcp.tool()
async def get_redmine_wiki_page(project_id: str, title: str) -> Dict[str, Any]:
    """
    获取Redmine Wiki页面
    
    获取指定项目中的Wiki页面内容。
    """
    return await get_wiki_page(project_id, title)

@mcp.tool()
async def update_redmine_wiki_page(wiki_page: WikiPageData) -> Dict[str, Any]:
    """
    更新Redmine Wiki页面
    
    更新或创建指定项目中的Wiki页面。
    """
    return await update_wiki_page(wiki_page)

@mcp.tool()
async def get_redmine_trackers() -> Dict[str, Any]:
    """
    获取Redmine跟踪器
    
    获取系统中定义的所有跟踪器（Tracker）。
    """
    return await get_trackers()

@mcp.tool()
async def get_redmine_recent_issues(
    project_id: Optional[Union[int, str]] = None,
    limit: int = 20,
    days: int = 5,
    status_id: str = '*'
) -> Dict[str, Any]:
    """
    获取最新问题列表
    
    获取指定项目或所有项目的最新问题列表。可选择按项目ID过滤，支持指定天数、状态等。
    
    Args:
        project_id: 项目ID（整数或字符串），不指定则获取所有项目
        limit: 返回的最大记录数，默认20条
        days: 最近几天的记录，默认5天
        status_id: 问题状态ID，'*'表示所有状态
    """
    try:
        return await get_recent_issues(project_id, limit, days, status_id)
    except Exception as e:
        logger.error(f"获取最新问题失败: {str(e)}")
        return {"issues": [], "count": 0, "error": str(e), "time_range": {"start_date": "", "end_date": "", "days": days}}

logger.debug("Redmine MCP服务器已初始化，开始运行...")

if __name__ == "__main__":
    # 启动服务器
    logger.debug("启动FastMCP服务")
    try:
        mcp.run(transport="stdio")
    except Exception as e:
        logger.error(f"启动FastMCP服务失败: {str(e)}")
        logger.error(traceback.format_exc())
        sys.exit(1) 