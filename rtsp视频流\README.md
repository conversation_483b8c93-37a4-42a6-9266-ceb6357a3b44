# MP4视频转RTSP流工具

此工具包用于将MP4视频文件转换为RTSP流，模拟IP摄像头效果。

## 功能特点

- 将本地MP4视频转换为RTSP流
- 支持多路视频流同时推送
- 无限循环播放视频
- 内置RTSP服务器配置和启动功能

## 使用方法

### 准备工作

1. 确保安装了FFmpeg并添加到系统环境变量
   - 可以从 [FFmpeg官网](https://ffmpeg.org/download.html) 下载
   - 安装后确保在命令行中能运行 `ffmpeg -version`

### 启动RTSP服务器

1. 运行 `create_rtsp_server.bat`
   - 如果首次运行，脚本会自动下载并配置MediaMTX服务器
   - 服务器将在端口8554上启动

### 推送视频流

1. 确保RTSP服务器已启动
2. 运行 `start_rtsp_stream.bat`
3. 脚本将启动5个命令行窗口，分别推送5个视频流

### RTSP流地址

推送成功后，可以通过以下地址访问RTSP流：

- 视频1: `rtsp://admin:admin123@localhost:8554/stream1`
- 视频2: `rtsp://admin:admin123@localhost:8554/stream2`
- 视频3: `rtsp://admin:admin123@localhost:8554/stream3`
- 视频4: `rtsp://admin:admin123@localhost:8554/stream4`
- 视频5: `rtsp://admin:admin123@localhost:8554/stream5`

## 在RTSP代理中使用

这些RTSP流可以直接添加到RTSP代理服务器中：

1. 启动RTSP代理服务器 (../rtsp_proxy/start_proxy.bat)
2. 在浏览器中访问 http://localhost:8001
3. 添加RTSP流，使用上述RTSP地址

## 停止服务

- 要停止视频推送，关闭对应的命令行窗口
- 要停止RTSP服务器，在服务器窗口按Ctrl+C

## 自定义设置

如需修改设置：

- 编辑 `start_rtsp_stream.bat` 更改视频文件或流参数
- 编辑 `create_rtsp_server.bat` 更改服务器配置

## 故障排除

1. **找不到FFmpeg**：确保FFmpeg已正确安装并添加到环境变量
2. **服务器无法启动**：检查端口8554是否被占用
3. **推流失败**：检查视频文件路径是否正确，确保服务器已启动
4. **中文乱码问题**：
   - 脚本已经添加了 `chcp 65001` 命令设置UTF-8编码
   - 如果仍然出现乱码，可以尝试在控制台输入 `chcp 936`（使用GBK编码）
   - 也可以修改批处理文件中的 `chcp 65001` 改为 `chcp 936` 