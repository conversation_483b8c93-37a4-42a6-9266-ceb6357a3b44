@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

echo ====================================
echo    RTSP代理服务器启动脚本
echo ====================================
echo.

REM 检查命令行参数
set no_load_config=0
for %%a in (%*) do (
    if "%%a" == "--no-load-config" set no_load_config=1
    if "%%a" == "--help" (
        echo 用法: start_proxy.bat [选项]
        echo.
        echo 选项:
        echo   --no-load-config     不加载已保存的流配置
        echo   --help               显示此帮助信息
        echo.
        exit /b 0
    )
)

REM 检查Python虚拟环境
if not exist venv\Scripts\activate.bat (
    echo 虚拟环境不存在，请先运行 setup_venv.bat 创建环境
    echo.
    pause
    exit /b 1
)

REM 检查FFmpeg
call check_ffmpeg.bat
if %errorlevel% neq 0 (
    echo.
    echo 警告: FFmpeg可能未正确安装，视频流功能可能无法正常工作
    echo 请先安装FFmpeg并确保其在PATH环境变量中
    echo.
    choice /C YN /M "是否仍然继续启动服务器?"
    if !errorlevel! neq 1 exit /b 1
    echo.
)

REM 提示端口选择
set port=8001
set /p port=请输入服务器端口号(默认:8001): 

REM 检查是否有配置文件
set has_config=0
if exist stream_config.json set has_config=1

REM 激活虚拟环境并启动服务器
echo.
echo 正在启动RTSP代理服务器(端口: %port%)...
if %no_load_config% equ 1 (
    echo 已指定--no-load-config参数，将不加载已保存的流配置...
    set load_opt=--no-load-config
) else (
    if %has_config% equ 1 (
        echo 找到配置文件，将自动加载已保存的流配置...
        set load_opt=
    ) else (
        echo 未找到配置文件，将以空配置启动...
        set load_opt=
    )
)
echo 服务器启动后，请访问 http://localhost:%port%/
echo 按Ctrl+C停止服务器
echo.

REM 启动服务器
call venv\Scripts\activate && python rtsp_proxy.py --host 0.0.0.0 --port %port% %load_opt%

endlocal 