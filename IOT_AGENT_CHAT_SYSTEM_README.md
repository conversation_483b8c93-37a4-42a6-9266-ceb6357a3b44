# IOT Agent 智能聊天系统

## 系统概述

IOT Agent 智能聊天系统是一个集成在油气生产数字孪生平台中的智能对话助手，基于大语言模型技术，为用户提供自然语言交互界面，实现设备监控、数据查询、知识检索等功能。系统采用前后端分离架构，前端基于Vue 3开发交互界面，后端基于FastAPI实现API代理和数据集成。

![系统架构图](system_architecture.png)

## 核心功能

1. **智能对话**：基于通义千问2.5-32B大语言模型，提供流畅的自然语言交互体验
2. **设备控制**：通过自然语言指令控制设备，如打开摄像头、查看实时数据
3. **知识检索**：检索知识库内容，回答专业领域问题
4. **文档查看**：在对话中展示和打开PDF文档、图片等文件
5. **设备定位**：在3D场景中定位和高亮显示特定设备
6. **常用问题**：提供预设的常用问题按钮，方便用户快速提问

## 技术架构

### 前端架构

- **框架**：Vue 3 + Vite
- **UI组件**：自定义聊天面板组件
- **状态管理**：Vue Reactive状态
- **通信协议**：HTTP/WebSocket
- **数据处理**：流式响应处理
- **安全处理**：DOMPurify (HTML净化)
- **Markdown渲染**：marked + highlight.js

### 后端架构

- **Web框架**：FastAPI
- **API代理**：自定义代理中间件
- **流式响应**：Server-Sent Events (SSE)
- **设备数据集成**：MQTT客户端
- **知识库检索**：向量数据库
- **认证机制**：Bearer Token

### RAGFlow服务集成

系统使用RAGFlow作为大语言模型服务：

- **服务部署**：127.0.0.1
- **聊天ID**：c1a83f6f475c11f0bbc3345a603cb29c（固定通道ID）
- **API密钥**：ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG
- **模型**：通义千问2.5-32B（qwen2.5:32b）
- **代理模式**：前端请求 → 后端API(127.0.0.1:8000) → RAGFlow服务

### API接口

前端通过以下API接口与RAGFlow服务通信：

```
POST /api/api/v1/chats_openai/${chatId}/chat/completions
```

请求体示例：
```json
{
  "model": "qwen2.5:32b",
  "messages": [历史消息],
  "stream": true
}
```

### 系统集成

聊天系统与以下模块紧密集成：

- **设备监控系统**：获取实时设备数据
- **文档管理系统**：检索知识库内容
- **3D场景控制**：实现设备定位和摄像头控制
- **文件预览系统**：展示PDF和图片文件

## 数据流程

1. **用户输入处理**：
   - 用户在聊天界面输入问题
   - 前端检测特殊命令（如打开摄像头、查询设备数据）
   - 对特殊命令进行本地处理，其他问题发送到后端

2. **后端请求处理**：
   - 接收前端请求
   - 根据需要注入设备数据或知识库内容
   - 转发请求到大语言模型服务

3. **大语言模型处理**：
   - 分析用户问题和上下文
   - 生成响应内容
   - 以流式方式返回响应

4. **前端响应展示**：
   - 接收流式响应
   - 使用打字机效果实时展示内容
   - 处理特殊标记和格式化内容

5. **交互功能触发**：
   - 解析响应中的交互指令
   - 触发相应的UI事件（如打开文件、定位设备）
   - 更新界面状态

### 聊天请求流程详解

用户聊天的完整流程如下：

1. **前端发起请求**：
   - 前端ChatPanel组件收集用户输入和历史消息
   - 构建请求体，指定模型为`qwen2.5:32b`，设置`stream: true`
   - 使用固定的chatId（c1a83f6f475c11f0bbc3345a603cb29c）和apiKey进行认证
   - 通过fetch API发送POST请求到`/api/api/v1/chats_openai/${chatId}/chat/completions`

2. **后端代理转发**：
   - FastAPI服务器（部署在127.0.0.1:8000）接收请求
   - 验证请求格式和认证信息
   - 使用httpx.AsyncClient将请求转发到RAGFlow服务
   - 转发地址：`${RAGFLOW_BASE_URL}/api/v1/chats_openai/${chat_id}/chat/completions`

3. **RAGFlow服务处理**：
   - RAGFlow服务（部署在127.0.0.1）接收请求
   - 调用通义千问2.5-32B模型处理对话
   - 生成回复并以SSE（Server-Sent Events）格式流式返回

4. **响应转发与处理**：
   - 后端API将流式响应直接传递回前端
   - 前端解析SSE格式数据
   - 提取delta内容并更新消息显示

5. **特殊功能处理**：
   - 前端解析响应中的特殊标记（如tool-usage-mark）
   - 触发相应的UI功能（如打开设备面板、显示图片等）
   - 向用户提供实时反馈

这种架构设计将模型服务与前端UI解耦，通过API代理实现安全访问和统一认证，同时保持了流式响应的高效交互体验。

### MQTT数据流

系统实现了前后端双重订阅MQTT的设计，实现了灵活高效的设备数据处理：

#### 前端MQTT订阅
前端通过WebSocket连接到MQTT服务器：

```javascript
// MQTT服务器连接配置
const options = {
  protocol: 'ws',
  port: 5710,
  path: '/mqtt',           
  hostname: '127.0.0.1', 
  wsOptions: {
    rejectUnauthorized: false
  }
};

// 选中设备后订阅对应主题
selectDevice(device) {
  if (device && device.topic) {
    mqttClient.subscribe(device.topic)
  }
}
```

前端MQTT订阅主要用于：
- 实时更新设备数据面板
- 触发设备状态变化的界面反馈
- 实现数据可视化图表的动态更新
- 减少对后端API的请求频率

#### 后端MQTT订阅
后端使用Python的paho-mqtt库实现订阅：

```python
class MQTTClient:
    def __init__(self):
        self.client = mqtt.Client(transport="websockets")
        self.connected = False
        self.device_data = {}
        
    def connect(self, host, port, path):
        self.client.ws_set_options(path=path)
        self.client.on_message = self.on_message
        self.client.on_connect = self.on_connect
        self.client.connect(host, port, 60)
        self.client.loop_start()
```

后端MQTT订阅主要用于：
- 持久化存储设备数据
- 执行设备阈值监控和告警生成
- 将设备数据注入到聊天上下文中
- 提供数据聚合和历史查询功能

两端使用相同的MQTT服务器配置：
- 主机: 127.0.0.1
- 端口: 5710
- WebSocket路径: /mqtt

这种双端订阅设计提高了系统可靠性，减少了网络延迟，同时实现了前后端各自的数据处理需求。前端可以直接获取实时数据进行展示，而后端可以专注于数据存储和复杂处理逻辑。

## 部署指南

### 前端部署

1. **安装依赖**：
   ```bash
   cd Frontend
   npm install
   ```

2. **开发模式**：
   ```bash
   npm run dev
   ```

3. **生产构建**：
   ```bash
   npm run build
   ```

4. **环境配置**：
   编辑`.env`文件设置API地址和其他配置

### 后端部署

1. **安装依赖**：
   ```bash
   cd Backend
   pip install -r requirements.txt
   ```

2. **启动服务**：
   ```bash
   uvicorn app.main:app --host 0.0.0.0 --port 8000
   ```

3. **环境配置**：
   编辑`.env`文件设置RAGFlow API密钥和MQTT配置

## 使用指南

### 基本对话

1. 点击界面左下角的聊天图标打开聊天面板
2. 在输入框中输入问题，按Enter键或点击发送按钮
3. 等待AI助手回复

### 设备控制

- 输入"打开JH005的摄像头"可以打开指定设备的摄像头
- 输入"获取JH005的实时数据"可以查看设备数据面板

### 文档查询

- 输入"展示中控班管理手册"可以查看相关文档
- 输入"调出A2日报"可以查看生产日报

### 常用问题

聊天界面顶部提供了常用问题按钮，点击即可快速提问：
- 获取JH005的实时数据
- 站场巡检工作有哪些
- 作业三区的组织机构情况
- JH005的摄像打开头
- 调出作业区目前生产油井的A2日报
- 调出作业区目海南19-13井的最近的10幅功图
- 展示中控班管理手册

## 开发者文档

详细的开发者文档请参考：

- [前端聊天系统文档](Frontend/CHAT_DOCUMENTATION_CN.md)
- [后端聊天系统文档](Backend/CHAT_DOCUMENTATION_CN.md)

## 系统截图

![聊天界面](screenshots/chat_interface.png)
![设备控制](screenshots/device_control.png)
![文档查询](screenshots/document_query.png)

## 未来规划

1. **多模态输入**：支持语音和图像输入
2. **上下文记忆增强**：改进长对话记忆能力
3. **个性化推荐**：基于用户历史提供智能建议
4. **多语言支持**：扩展支持英语等其他语言
5. **离线模式**：添加基本的离线功能支持

## 贡献指南

欢迎为项目做出贡献！请遵循以下步骤：

1. Fork本仓库
2. 创建功能分支：`git checkout -b feature/amazing-feature`
3. 提交更改：`git commit -m 'Add some amazing feature'`
4. 推送到分支：`git push origin feature/amazing-feature`
5. 提交Pull Request

## 许可证

本项目采用 MIT 许可证 - 详情请参阅 [LICENSE](LICENSE) 文件 