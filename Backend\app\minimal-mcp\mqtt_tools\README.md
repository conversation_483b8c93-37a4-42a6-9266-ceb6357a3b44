# MQTT工具使用说明

本文件夹包含用于MQTT设备连接和配置的工具。这些工具可以帮助您连接到MQTT服务器，发现可用设备，并通过MCP（Model Context Protocol）向AI模型提供设备数据和操作能力。

## 配置流程

### 1. 修改MQTT连接配置

使用 `update_mqtt_config.py` 脚本设置MQTT服务器连接信息：

```bash
python update_mqtt_config.py
```

这将启动交互式配置过程，或者您可以直接提供参数：

```bash
python update_mqtt_config.py --host=************* --port=1883 --path=/mqtt
```

### 2. EMQX权限配置（重要）

在使用MQTT发现工具前，确保EMQX服务器已正确配置ACL（访问控制列表），允许mqtt-discovery客户端访问所有主题，包括系统主题。

在EMQX管理界面中：

1. 导航到"访问控制" > "ACL"
2. 添加新规则，配置如下：
   - 客户端ID: mqtt-discovery
   - 允许订阅: 所有主题（使用#和$SYS/#）
   - 允许发布: 所有主题（使用#）
   - 优先级: 高于默认规则的值（例如1）

此配置是必需的，因为默认情况下，EMQX限制客户端访问系统主题（$SYS/#）。

### 3. 发现MQTT设备

使用 `mqtt_discovery.py` 脚本自动发现MQTT主题并将其作为设备添加到配置中：

```bash
python mqtt_discovery.py
```

该脚本将：
- 连接到您配置的MQTT服务器（127.0.0.1:5710）
- 使用固定的客户端ID "mqtt-discovery" 连接服务器
- 订阅系统主题和普通主题
- 监听所有MQTT主题约30秒
- 自动将发现的主题解析为设备信息
- 更新配置文件

### 4. 启动主应用程序

完成配置后，返回上级目录启动主应用程序：

```bash
cd ..
python custom_interactive_client_sse.py
```

主应用程序将加载MQTT工具中的MCP服务器，使AI能够访问和控制您的MQTT设备。

## 文件说明

- `config.json` - MQTT连接配置和设备列表
- `mcp_server.py` - MQTT MCP服务器实现，为AI提供设备交互能力
- `update_mqtt_config.py` - 用于更新MQTT连接配置
- `mqtt_discovery.py` - 用于发现可用MQTT主题并更新设备列表
- `trouble.md` - 故障排除指南

## 设备命名规则

系统自动为发现的设备生成友好的显示名称（display_name），规则如下：

1. **JH系列设备**：使用编号加随机后缀，例如：
   - JH001 → 井01-123（三位随机数后缀）
   - JH002 → 井02-456（确保每次生成都不同）

2. **HN系列设备**：使用五位随机数，例如：
   - HN15V3 → 井H12345（五位随机数）
   - HN15V4 → 井H67890（确保唯一性）

3. **其他设备**：使用"井S"前缀加五位随机数，例如：
   - 未识别设备 → 井S54321（五位随机数）

这种命名规则确保了即使在大量设备的情况下，每个设备都有唯一的显示名称，避免了之前版本中出现的重复命名问题（如多个"井15"）。

## 手动编辑配置

如果您希望手动编辑配置，可以直接修改 `config.json` 文件。文件格式如下：

```json
{
  "mqtt": {
    "host": "127.0.0.1",
    "port": 5710,
    "path": "/mqtt"
  },
  "devices": [
    {
      "station": "站点ID",
      "wellname": "设备ID",
      "display_name": "显示名称"
    },
    ...
  ]
}
```

## 故障排除

### 连接问题

1. **无法连接到MQTT服务器**
   - 确保MQTT服务器地址和端口正确（当前配置使用127.0.0.1:5710）
   - 检查WebSocket路径是否正确配置（默认/mqtt）
   - 验证服务器是否启用了WebSocket支持

2. **无法发现系统主题**
   - 确认EMQX的ACL规则已正确配置，允许mqtt-discovery客户端访问$SYS/#主题
   - 检查客户端ID是否正确设置为"mqtt-discovery"
   - 调整日志级别为DEBUG以获取更多信息：修改logging.basicConfig中的level=logging.DEBUG

3. **发现的设备过少**
   - 增加discovery_time变量（默认30秒）以延长监听时间
   - 使用EMQX管理界面检查是否有活跃的主题
   - 可能是设备数据发布不频繁，尝试手动触发设备数据发布

### 配置问题

1. **设备显示名称重复**
   - 如果发现显示名称仍有重复，清空config.json中的devices数组，重新运行discovery脚本
   - 重新运行时会使用更新的命名规则生成唯一名称

2. **配置文件损坏**
   - 如果config.json文件格式错误，删除它并重新运行工具以生成新的配置文件
   - 使用JSON验证工具检查配置文件格式

## 高级功能

### API访问

除了直接通过MQTT协议发现主题外，该工具还尝试使用EMQX HTTP API获取主题列表：

- 默认API端点: http://127.0.0.1:5712/api/v5/topics
- 默认凭据: admin/public

如果API凭据有变更，请修改try_single_api_endpoint函数中的用户名和密码。

### 自定义连接选项

如需调整连接参数，可以修改以下选项：

- 连接超时：修改connect_timeout变量（默认3秒）
- 重试次数：修改retry_count < 5的条件（默认5次）
- 等待系统主题数据的时间：修改wait_time变量（默认15秒）

## 使用方法

### 1. 配置文件

配置文件`config.json`位于`mqtt_tools`目录下，包含MQTT服务器信息和设备列表。

### 2. 集成到主应用程序

MQTT工具已在`custom_interactive_client_sse.py`中集成，系统会自动加载MQTT工具服务器：

```python
# 添加MQTT工具服务器
servers["mqtt"] = {
    "command": "python",
    "args": ["mqtt_tools/mcp_server.py"],
    "transport": "stdio",
}
```

### 3. 使用MQTT工具

启动主应用程序后，AI代理可以使用以下MQTT工具：

1. `get_device_data` - 获取设备的最新数据
2. `list_devices` - 列出所有设备
3. `get_device_parameter` - 获取设备的特定参数
4. `get_device_chart_data` - 获取设备的功图数据
5. `get_device_status` - 获取设备的状态信息
6. `get_parameter_description` - 获取参数的描述和单位

### 4. 独立运行MQTT工具服务器（测试用）

```bash
cd mqtt_tools
python mcp_server.py
```

## 设备数据参数说明

数据中的主要参数含义：

- `TWT` - 井口温度 (℃)
- `WIP` - 井口压力 (MPa)
- `CPV` - 套压 (MPa)
- `SLV` - 冲程 (m)
- `CHC` - 冲次 (次/分)
- `UWL` - 最大载荷 (KN)
- `DWL` - 最小载荷 (KN)
- `WBR` - 平衡度
- `ADL` - A相电流 (A)
- `BDL` - B相电流 (A)
- `CDL` - C相电流 (A)
- `ADY` - A相电压 (V)
- `BDY` - B相电压 (V)
- `CDY` - C相电压 (V)
- `GYS` - 功率因数
- `ZYG` - 总有功功率 (KW)
- `ZWG` - 总无功功率 (KVar)
- `YGL` - 有功用电量 (KWh)

## 示例查询

以下是一些示例查询，展示如何在聊天中使用MQTT工具：

- "井01-123的当前状态是什么？"
- "显示井H12345的温度和压力"
- "获取井02-456的功图数据"
- "哪些油井当前处于异常状态？"
- "井S54321的电流和电压情况如何？"