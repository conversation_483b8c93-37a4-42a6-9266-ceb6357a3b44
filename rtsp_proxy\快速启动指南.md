# Python RTSP 代理服务器使用指南

## 简介

这个 Python RTSP 代理服务器可以将 RTSP 视频流转换为浏览器可以直接显示的 MJPEG 格式。不需要安装额外的浏览器插件，适用于各种前端应用。

## 系统要求

- Python 3.7 或更高版本
- FFmpeg（可在系统中全局安装，或放置在 bin 目录下）
- 网络连接到 RTSP 摄像头

## 快速启动

### Windows 用户

1. 确保已安装 Python 3.7+
2. 双击运行 `start_proxy.bat` 批处理文件
3. 脚本会自动安装必要的依赖并启动服务器
4. 服务器默认在 `http://localhost:8001` 运行

### Linux/Mac 用户

1. 确保已安装 Python 3.7+
2. 在终端中运行以下命令：
   ```bash
   chmod +x start_proxy.sh  # 如果尚未设置可执行权限
   ./start_proxy.sh
   ```
3. 服务器默认在 `http://localhost:8001` 运行

## 依赖兼容性问题解决

如果遇到NumPy与OpenCV兼容性问题，启动脚本会自动先卸载现有的NumPy和OpenCV，然后安装兼容版本。如果仍然遇到问题，可以手动执行以下步骤：

1. 卸载冲突的库：
   ```
   pip uninstall -y numpy opencv-python
   ```

2. 安装指定版本的依赖：
   ```
   pip install numpy<2.0.0 opencv-python==********
   ```

这样可以确保使用兼容的NumPy 1.x版本，避免与OpenCV的冲突。

## 使用方法

1. 启动服务器后，在浏览器中访问 `http://localhost:8001`
2. 使用 Web 界面创建和管理 RTSP 流
3. 对于快速测试，可以访问 `http://localhost:8001/test` 页面

## 配置管理功能

本服务器支持自动保存和加载流配置，简化重启后的设置过程：

1. **自动保存配置**：
   - 创建新流时，会自动保存到 `stream_config.json` 配置文件中
   - 停止流时，可选择是否从配置中移除该流

2. **自动加载配置**：
   - 服务器启动时会自动加载保存的流配置
   - 如果需要不加载配置，可以使用命令行参数：`start_proxy.bat --no-load-config`

3. **手动保存配置**：
   - 在 Web 界面上使用"手动保存配置"按钮保存当前所有活跃流的配置

这样可以确保服务器重启后能够恢复之前设置的所有流，无需重新手动创建。

## 在前端应用中使用

在前端应用中，可以通过以下方式显示 MJPEG 流：

```html
<img src="http://localhost:8001/api/stream/{流名称}/mjpeg" alt="RTSP Stream">
```

## API 接口

### 创建新流
```
POST /api/streams/create
Content-Type: application/json

{
  "url": "rtsp://用户名:密码@摄像头IP:端口/路径",
  "name": "流名称",
  "fps": 15,
  "width": 640,
  "height": 480
}
```

### 查看所有流
```
GET /api/streams
```

### 停止流
```
POST /api/streams/{流名称}/stop
```

## 常见问题

1. **服务器启动失败**
   - 检查 Python 版本是否为 3.7+
   - 确保已安装所有依赖: `pip install -r requirements.txt`
   - 如果出现NumPy兼容性问题，按照上述"依赖兼容性问题解决"部分操作

2. **无法连接到摄像头**
   - 确认 RTSP URL 格式正确
   - 检查摄像头是否在线并且 IP、端口、用户名和密码是否正确
   - 确认网络连接正常，没有防火墙阻止

3. **视频流无法显示**
   - 确认 FFmpeg 已正确安装
   - 查看服务器控制台输出的错误信息
   - 尝试降低帧率或分辨率

## 技术支持

如有问题，请查看控制台输出的详细日志信息，或参考 README 文件中的更多信息。 