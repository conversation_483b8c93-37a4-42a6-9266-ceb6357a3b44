#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from typing import Dict, Any
from pydantic import BaseModel
from .redmine_config import redmine_config

logger = logging.getLogger(__name__)

class TimeRange(BaseModel):
    start_date: str
    end_date: str

async def get_time_entries(time_range: TimeRange) -> Dict[str, Any]:
    """
    获取Redmine工时记录
    
    获取指定时间范围内的所有工时记录。
    """
    try:
        redmine = redmine_config.get_client()
        
        time_entries = list(redmine.time_entry.filter(
            spent_on=f'><{time_range.start_date}|{time_range.end_date}'
        ))
        
        # 转换为可序列化的字典列表
        result = []
        
        for entry in time_entries:
            data = {
                "id": getattr(entry, 'id', 0),
                "hours": float(getattr(entry, 'hours', 0)),
                "spent_on": getattr(entry, 'spent_on', ''),
                "comments": getattr(entry, 'comments', ''),
            }
            
            # 添加项目信息
            if hasattr(entry, 'project'):
                data["project"] = {
                    "id": getattr(entry.project, 'id', 0),
                    "name": getattr(entry.project, 'name', '未知项目')
                }
            else:
                data["project"] = {"id": 0, "name": "未知项目"}
            
            # 添加用户信息
            if hasattr(entry, 'user'):
                data["user"] = {
                    "id": getattr(entry.user, 'id', 0),
                    "name": getattr(entry.user, 'name', '未知用户')
                }
            else:
                data["user"] = {"id": 0, "name": "未知用户"}
            
            # 添加活动信息
            if hasattr(entry, 'activity') and entry.activity is not None:
                data["activity"] = {
                    "id": getattr(entry.activity, 'id', 0),
                    "name": getattr(entry.activity, 'name', '其他')
                }
            else:
                data["activity"] = {"id": 0, "name": "其他"}
            
            # 添加问题信息
            if hasattr(entry, 'issue') and entry.issue is not None:
                data["issue"] = {
                    "id": getattr(entry.issue, 'id', 0),
                    "subject": getattr(entry.issue, 'subject', '未知问题') if hasattr(entry.issue, 'subject') else '未知问题'
                }
            else:
                data["issue"] = None
                
            result.append(data)
        
        return {
            "timeEntries": result,
            "count": len(result),
            "totalHours": sum(entry["hours"] for entry in result)
        }
        
    except Exception as e:
        logger.error(f"获取工时记录失败: {str(e)}")
        raise Exception(f"获取工时记录失败: {str(e)}") 