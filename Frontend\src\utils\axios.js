import axios from 'axios'

// 创建 axios 实例
const http = axios.create({
  baseURL: '/',  // 修改为根路径，避免自动添加/api前缀
  timeout: 300000, // 5分钟超时
  withCredentials: true, // 允许携带凭证
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG'
  }
})

// 请求拦截器
http.interceptors.request.use(
  config => {
    // 如果是文件上传，不设置Content-Type，让浏览器自动设置
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type']
      // 确保每个请求都带上 Authorization header
      config.headers['Authorization'] = 'Bearer ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG'
    }
    return config
  },
  error => {
    console.error('API请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  response => {
    // 直接返回响应的data部分
    return response.data
  },
  error => {
    // 统一处理错误
    if (error.response) {
      console.error('API请求错误:', error.response.status, error.response.data)
    } else {
      console.error('API请求错误:', error.message)
    }
    return Promise.reject(error)
  }
)

export default http 