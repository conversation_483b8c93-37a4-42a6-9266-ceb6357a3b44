#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
from pydantic import BaseModel, field_validator
from .redmine_config import redmine_config

logger = logging.getLogger(__name__)

class ProjectOption(BaseModel):
    """项目选择选项"""
    project_id: Optional[Union[int, str]] = None
    limit: Optional[int] = 20
    days: Optional[int] = 30
    status_id: Optional[str] = '*'  # '*'表示所有状态
    
    @field_validator('project_id')
    def validate_project_id(cls, v):
        if v is None:
            return None
        try:
            if isinstance(v, str) and v.strip():
                return int(v)
            return v
        except (ValueError, TypeError):
            logger.warning(f"项目ID不是有效整数: {v}，将其设置为None")
            return None

async def get_recent_issues(
    project_id: Optional[Union[int, str]] = None, 
    limit: int = 20, 
    days: int = 30,
    status_id: str = '*'
) -> Dict[str, Any]:
    """
    获取最新问题列表
    
    获取指定项目或所有项目的最新问题，默认获取30天内更新的问题。
    """
    try:
        # 处理project_id参数，确保它是整数或None
        parsed_project_id = None
        if project_id is not None:
            try:
                if isinstance(project_id, str) and project_id.strip():
                    parsed_project_id = int(project_id)
                else:
                    parsed_project_id = project_id
            except (ValueError, TypeError):
                logger.warning(f"项目ID不是有效整数: {project_id}，将其设置为None")
                parsed_project_id = None
        
        # 创建选项对象
        options = ProjectOption(
            project_id=parsed_project_id,
            limit=limit,
            days=days,
            status_id=status_id
        )
            
        redmine = redmine_config.get_client()
        
        # 计算时间范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=options.days)
        
        # 设置过滤参数 - 修改日期格式
        filter_params = {
            'status_id': options.status_id,
            # 使用><格式的日期范围，而不是>=格式
            'updated_on': f'><{start_date.strftime("%Y-%m-%d")}|{end_date.strftime("%Y-%m-%d")}',
            'limit': options.limit
        }
        
        # 如果指定了项目ID，添加到过滤条件
        if options.project_id:
            filter_params['project_id'] = options.project_id
            
            # 验证项目是否存在
            try:
                project = redmine.project.get(options.project_id)
                project_info = {
                    "id": project.id,
                    "name": project.name,
                    "identifier": getattr(project, 'identifier', ''),
                    "description": getattr(project, 'description', '')
                }
            except Exception as e:
                logger.warning(f"获取项目信息失败: {str(e)}")
                project_info = {"id": options.project_id, "name": "未知项目"}
        else:
            project_info = None
        
        # 获取最新问题列表
        try:
            issues = list(redmine.issue.filter(**filter_params))
        except Exception as filter_error:
            # 如果日期格式出错，尝试其他方式获取
            logger.warning(f"使用日期范围过滤失败: {str(filter_error)}，尝试使用其他方式获取")
            # 移除日期过滤参数，改为按ID排序后手动筛选
            filter_params.pop('updated_on', None)
            filter_params['sort'] = 'updated_on:desc'
            issues = list(redmine.issue.filter(**filter_params))
            
            # 手动过滤日期
            filtered_issues = []
            for issue in issues:
                if hasattr(issue, 'updated_on'):
                    issue_date = issue.updated_on.date() if hasattr(issue.updated_on, 'date') else issue.updated_on
                    if start_date.date() <= issue_date <= end_date.date():
                        filtered_issues.append(issue)
                else:
                    # 如果没有更新日期，使用创建日期
                    if hasattr(issue, 'created_on'):
                        issue_date = issue.created_on.date() if hasattr(issue.created_on, 'date') else issue.created_on
                        if start_date.date() <= issue_date <= end_date.date():
                            filtered_issues.append(issue)
            
            issues = filtered_issues[:options.limit]
        
        # 转换为可序列化的字典列表
        result = []
        
        for issue in issues:
            # 基本信息
            data = {
                "id": issue.id,
                "subject": getattr(issue, 'subject', '未知问题'),
                "description": getattr(issue, 'description', ''),
                "created_on": getattr(issue, 'created_on', '').strftime("%Y-%m-%d %H:%M:%S") if hasattr(issue, 'created_on') else '',
                "updated_on": getattr(issue, 'updated_on', '').strftime("%Y-%m-%d %H:%M:%S") if hasattr(issue, 'updated_on') else '',
                "done_ratio": getattr(issue, 'done_ratio', 0),
            }
            
            # 状态信息
            if hasattr(issue, 'status'):
                data["status"] = {
                    "id": getattr(issue.status, 'id', 0),
                    "name": getattr(issue.status, 'name', '未知状态')
                }
            else:
                data["status"] = {"id": 0, "name": "未知状态"}
            
            # 项目信息
            if hasattr(issue, 'project'):
                data["project"] = {
                    "id": getattr(issue.project, 'id', 0),
                    "name": getattr(issue.project, 'name', '未知项目')
                }
            else:
                data["project"] = {"id": 0, "name": "未知项目"}
            
            # 优先级信息
            if hasattr(issue, 'priority'):
                data["priority"] = {
                    "id": getattr(issue.priority, 'id', 0),
                    "name": getattr(issue.priority, 'name', '普通')
                }
            
            # 作者信息
            if hasattr(issue, 'author'):
                data["author"] = {
                    "id": getattr(issue.author, 'id', 0),
                    "name": getattr(issue.author, 'name', '未知用户')
                }
            
            # 指派人信息
            if hasattr(issue, 'assigned_to'):
                data["assigned_to"] = {
                    "id": getattr(issue.assigned_to, 'id', 0),
                    "name": getattr(issue.assigned_to, 'name', '未知用户')
                }
            
            result.append(data)
        
        # 返回结果
        response = {
            "issues": result,
            "count": len(result),
            "time_range": {
                "start_date": start_date.strftime("%Y-%m-%d"),
                "end_date": end_date.strftime("%Y-%m-%d"),
                "days": options.days
            }
        }
        
        # 如果查询特定项目，添加项目信息
        if project_info:
            response["project"] = project_info
            
        return response
        
    except Exception as e:
        logger.error(f"获取最新问题失败: {str(e)}")
        # 返回错误信息而不是抛出异常，避免中断执行
        return {
            "issues": [],
            "count": 0,
            "error": str(e),
            "time_range": {
                "start_date": (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d"),
                "end_date": datetime.now().strftime("%Y-%m-%d"),
                "days": days
            }
        } 