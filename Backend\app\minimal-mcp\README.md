.venv\Scripts\activate ; python .\custom_interactive_client.py

# MCP交互式客户端

这是一个基于LangChain和LangGraph构建的交互式客户端，用于连接到多个MCP (Model Context Protocol)服务器并使用其提供的工具与大语言模型进行交互。

## 功能特点

- 支持连接到多个MCP服务器（数学、天气、Redmine等）
- 使用LM Studio本地运行的大语言模型
- 交互式对话界面
- 自动加载所有可用的工具
- 模型可根据需要动态调用工具解决问题

## 系统要求

- Python 3.10+
- 网络连接（用于LM Studio模型访问）
- LM Studio（用于本地运行大语言模型）

## 快速开始

### 1. 创建虚拟环境（可选但推荐）

```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境（Windows）
.\.venv\Scripts\activate

# 激活虚拟环境（Linux/Mac）
source .venv/bin/activate
```

### 2. 安装依赖

```bash
pip install -r requirements.txt
```

所需的主要依赖包括：
- pydantic (>=2.0.0)：数据验证和设置管理
- python-redmine (>=2.3.0)：Redmine API客户端
- pyyaml (>=6.0)：YAML配置文件处理
- fastmcp (>=2.0.0)：MCP服务器实现
- langchain-core (>=0.3.36,<0.4)：LangChain核心组件
- mcp (>=1.4.1,<1.7)：MCP协议实现
- langchain-openai (>=0.0.1)：OpenAI模型集成
- langgraph (>=0.0.1)：构建ReAct代理
- langchain-mcp-adapters (>=0.0.1)：LangChain与MCP协议的连接适配器

### 3. 启动LM Studio（如果尚未运行）

确保LM Studio运行在`http://127.0.0.1:1234`，并加载了`qwen2.5-7b-instruct-1m`模型。
如需使用其他模型或地址，请修改`custom_interactive_client.py`文件中的相关配置。

### 4. 启动客户端

```bash
python custom_interactive_client.py
```

## 技术架构详解

### 整体架构

本项目采用了基于MCP协议的分布式工具服务架构，主要由以下几部分组成：

1. **交互式客户端**：`custom_interactive_client.py`
   - 对话界面
   - 大语言模型连接
   - 多服务器MCP客户端管理

2. **MCP服务器**：
   - `example_math_server.py`：提供基础数学运算工具
   - `example_weather_server.py`：提供模拟天气查询工具
   - `redmine_mcp_server.py`：提供Redmine项目管理工具

3. **LangGraph ReAct代理**：
   - 基于大语言模型的决策引擎
   - 工具使用规划与执行
   - 上下文管理与对话控制

### MCP协议简介

MCP（Model Context Protocol）是一种为大语言模型设计的工具调用协议，允许模型与外部工具无缝交互。主要特点：

- **标准化工具接口**：统一的工具定义和调用方式
- **类型安全**：基于Pydantic的参数验证
- **分布式架构**：支持多服务器、多工具集成
- **传输灵活性**：支持多种传输方式（本项目使用stdio）

示例：MCP工具定义（来自`example_math_server.py`）
```python
@mcp.tool()
def add(a: int, b: int) -> int:
    """Add two numbers"""
    return a + b
```

### 客户端实现详解

`custom_interactive_client.py`是系统的核心组件，实现了：

1. **依赖检查**：自动检测必要依赖（Redmine相关）是否已安装
2. **多服务器连接**：使用`MultiServerMCPClient`同时连接数学、天气和Redmine（如果依赖已安装）服务器
3. **工具发现与整合**：自动获取所有可用的工具并展示给用户
4. **LangGraph代理创建**：使用`create_react_agent`创建一个能够使用所有工具的代理
5. **交互式会话循环**：用户输入处理、模型决策执行、工具调用和结果显示

核心代码片段：
```python
# 创建一个LangGraph agent，使用LM Studio的本地模型
model = ChatOpenAI(
    model="qwen2.5-7b-instruct-1m",  # 模型名称
    openai_api_base="http://127.0.0.1:1234/v1",  # LM Studio API地址
    openai_api_key="sk-no-key-required"  # LM Studio通常不需要真实API密钥
)
agent = create_react_agent(model, tools)
```

### MCP服务器实现详解

每个MCP服务器都基于FastMCP框架构建，提供不同领域的工具：

1. **数学服务器**（`example_math_server.py`）
   - 简单的加法和乘法功能
   - 代码结构简洁，适合作为入门示例

2. **天气服务器**（`example_weather_server.py`）
   - 模拟天气数据查询功能
   - 展示了如何处理字符串输入和返回复杂结构

3. **Redmine服务器**（`redmine_mcp_server.py`）
   - 完整的Redmine API集成
   - 配置管理（基于YAML）
   - 异步API调用
   - 多种工具和资源端点
   - 错误处理和日志记录

Redmine服务器实现了更复杂的工具集，包括问题查询、创建、Wiki管理等功能。

示例：Redmine工具定义
```python
@mcp.tool()
async def create_redmine_issue(
    project_id: int, 
    subject: str, 
    description: str = "", 
    assigned_to_id: Optional[int] = None, 
    tracker_id: Optional[int] = None,
    status_id: Optional[int] = None,
    priority_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    创建Redmine问题
    
    在Redmine中创建一个新问题。
    """
    return await create_issue(
        project_id, subject, description, 
        assigned_to_id, tracker_id, status_id, priority_id
    )
```

### LangGraph ReAct代理详解

本项目使用了LangGraph的ReAct（Reasoning and Acting）代理模式：

1. **工作原理**：
   - 模型根据用户输入进行推理（Reasoning）
   - 决定是否需要调用工具（Acting）
   - 根据工具执行结果继续推理
   - 直到形成最终回答

2. **代理创建**：
   ```python
   agent = create_react_agent(model, tools)
   ```

3. **代理调用流程**：
   - 用户输入传递给代理
   - 代理分析输入并决定行动方案
   - 如需工具，代理构造参数并调用
   - 工具结果返回给代理
   - 代理继续推理直到得出最终回答

4. **消息处理**：
   代理处理多种消息类型，包括：
   - 人类消息（human）
   - 工具消息（tool）
   - AI消息（ai）

### 配置管理

Redmine服务器使用配置文件进行管理：

1. **配置文件位置**：`config/config.yaml`
2. **配置内容**：
   - Redmine服务器URL
   - API密钥
   - 各种选项和自定义设置

配置由`RedmineConfig`类管理，提供统一的加载、验证和访问接口。

### 多服务器通信机制

`MultiServerMCPClient`实现了与多个MCP服务器的并行通信：

1. **服务器配置**：定义每个服务器的启动命令和参数
2. **自动启动**：客户端启动时自动启动所有服务器
3. **工具发现**：从所有服务器收集工具定义
4. **资源管理**：管理每个服务器的资源端点
5. **生命周期管理**：确保在客户端关闭时关闭所有服务器

### 异步执行模型

系统广泛使用Python的`asyncio`异步编程模型：

1. **客户端主函数是异步的**：`async def main()`
2. **Redmine工具多采用异步实现**：`async def get_redmine_issues(...)`
3. **多服务器并行通信**：使用异步上下文管理器

这种设计确保系统可以高效处理多个并行工具调用，提高响应速度。

## 支持的工具

系统默认加载以下MCP服务器提供的工具：

1. **add**：加法计算
   - 参数：
     - `a`（必选，数字）：第一个加数
     - `b`（必选，数字）：第二个加数
   - 返回值：两数之和

2. **multiply**：乘法计算
   - 参数：
     - `a`（必选，数字）：第一个乘数
     - `b`（必选，数字）：第二个乘数
   - 返回值：两数之积

3. **get_weather**：获取指定城市的天气信息
   - 参数：
     - `location`（必选，字符串）：城市名称
   - 返回值：包含天气状况、温度等信息的字典

4. **get_redmine_time_entries**：获取Redmine工时记录
   - 参数：
     - `from_date`（可选，字符串）：起始日期，格式为YYYY-MM-DD
     - `to_date`（可选，字符串）：结束日期，格式为YYYY-MM-DD
   - 返回值：指定时间范围内的工时记录列表

5. **get_redmine_issues**：获取Redmine问题
   - 参数：
     - `from_date`（可选，字符串）：起始日期，格式为YYYY-MM-DD
     - `to_date`（可选，字符串）：结束日期，格式为YYYY-MM-DD
     - `project_id`（可选，整数或字符串）：项目ID
     - `query`（可选，字符串）：搜索关键词
   - 返回值：符合条件的问题列表

6. **get_redmine_projects**：获取所有项目的基本信息
   - 参数：无
   - 返回值：项目信息列表

7. **get_redmine_users**：获取所有用户的基本信息
   - 参数：无
   - 返回值：用户信息列表

8. **get_redmine_version**：获取当前连接的Redmine服务器版本信息
   - 参数：无
   - 返回值：Redmine服务器版本信息

9. **search_redmine**：使用Redmine的搜索API进行全文搜索
   - 参数：
     - `q`（必选，字符串）：搜索关键词
   - 返回值：搜索结果列表

10. **get_redmine_enumerations**：获取指定枚举类型的所有值
    - 参数：
      - `enumeration_type`（必选，字符串）：枚举类型，支持issue_priorities, time_entry_activities, document_categories
    - 返回值：指定枚举类型的所有值

11. **get_redmine_issue_statuses**：获取所有可用的问题状态
    - 参数：无
    - 返回值：问题状态列表

12. **create_redmine_issue**：在Redmine中创建一个新问题
    - 参数：
      - `project_id`（必选，整数或字符串）：项目ID
      - `subject`（必选，字符串）：问题标题
      - `description`（可选，字符串）：问题描述
      - `tracker_id`（可选，整数）：跟踪器ID
      - `status_id`（可选，整数）：状态ID
      - `priority_id`（可选，整数）：优先级ID
      - `assigned_to_id`（可选，整数）：指派给用户的ID
    - 返回值：创建的问题信息

13. **get_redmine_custom_fields**：获取系统中定义的所有自定义字段
    - 参数：无
    - 返回值：自定义字段列表

14. **get_redmine_wiki_page**：获取指定项目中的Wiki页面内容
    - 参数：
      - `project_id`（必选，整数或字符串）：项目ID
      - `page_name`（必选，字符串）：Wiki页面名称
    - 返回值：Wiki页面内容

15. **update_redmine_wiki_page**：更新或创建指定项目中的Wiki页面
    - 参数：
      - `project_id`（必选，整数或字符串）：项目ID
      - `page_name`（必选，字符串）：Wiki页面名称
      - `text`（必选，字符串）：页面内容
      - `comments`（可选，字符串）：更新注释
    - 返回值：更新结果

16. **get_redmine_trackers**：获取系统中定义的所有跟踪器（Tracker）
    - 参数：无
    - 返回值：跟踪器列表

17. **get_redmine_recent_issues**：获取最新问题列表
    - 参数：
      - `project_id`（可选，整数或字符串）：项目ID，不指定则获取所有项目
      - `limit`（可选，整数）：返回的最大记录数，默认20条
      - `days`（可选，整数）：最近几天的记录，默认5天
      - `status_id`（可选，字符串）：问题状态ID，'*'表示所有状态
    - 返回值：符合条件的最新问题列表

## 项目结构

```
.
├── .venv/                     # 虚拟环境目录
├── config/                    # 配置文件目录
│   └── config.yaml            # Redmine配置
├── redmine_tools/             # Redmine工具实现
│   ├── __init__.py            # 包初始化
│   ├── redmine_api.py         # Redmine API封装
│   ├── redmine_config.py      # 配置管理类
│   └── utils.py               # 工具函数
├── example_math_server.py     # 数学服务器实现
├── example_weather_server.py  # 天气服务器实现
├── redmine_mcp_server.py      # Redmine MCP服务器
├── custom_interactive_client.py  # 主客户端程序
├── requirements.txt           # 依赖列表
└── README.md                  # 本文档
```

## 使用示例

启动客户端后，您可以与模型进行交互，询问任何问题。模型会根据需要自动调用相应的工具：

```
您: 请计算 15 + 27 的结果
AI: 15 + 27 = 42

您: 北京今天的天气怎么样？
AI: 根据我获取的信息，北京今天的天气是晴天，气温28°C。

您: 帮我查询项目123的最新问题
AI: [使用Redmine工具查询后的结果...]
```

## 自定义MCP服务器

如果您想添加其他MCP服务器，请在`custom_interactive_client.py`的`main`函数中修改`servers`字典：

```python
servers = {
    "math": {
        "command": "python",
        "args": ["example_math_server.py"],
        "transport": "stdio",
    },
    "weather": {
        "command": "python",
        "args": ["example_weather_server.py"],
        "transport": "stdio",
    },
    # 添加您自己的MCP服务器
    "your_server": {
        "command": "python",
        "args": ["your_server.py"],
        "transport": "stdio",
    }
}
```

## 开发自定义MCP工具服务器

您可以参考以下步骤开发自己的MCP工具服务器：

1. **创建新的Python文件**：例如`custom_server.py`

2. **初始化FastMCP**：
   ```python
   from mcp.server.fastmcp import FastMCP
   
   mcp = FastMCP("YourServiceName")
   ```

3. **定义工具函数**：
   ```python
   @mcp.tool()
   def your_tool(param1: str, param2: int = 0) -> dict:
       """
       您的工具描述
       
       详细说明工具的功能和用途。
       """
       # 实现您的工具逻辑
       result = {"param1": param1, "param2": param2, "result": "处理结果"}
       return result
   ```

4. **添加资源端点**（可选）：
   ```python
   @mcp.resource("yourservice://resource")
   def get_resource() -> dict:
       """资源描述"""
       return {"data": "资源数据"}
   ```

5. **启动服务器**：
   ```python
   if __name__ == "__main__":
       mcp.run(transport="stdio")
   ```

6. **在客户端中注册服务器**：修改`custom_interactive_client.py`中的`servers`字典，添加您的服务器。

## 工具函数实现最佳实践

在实现MCP工具函数时，建议遵循以下最佳实践：

1. **详细的文档字符串**：提供清晰的工具描述，帮助模型理解工具的用途和使用方法
2. **类型注解**：为所有参数和返回值提供准确的类型注解
3. **参数验证**：在函数内部进行参数验证，确保输入有效
4. **错误处理**：捕获并处理可能的异常，返回友好的错误信息
5. **异步实现**：对于I/O密集型操作，考虑使用异步实现提高性能
6. **日志记录**：记录工具调用和结果，便于调试和监控

## 模型与工具交互流程

客户端、模型和工具的交互流程如下：

1. **用户输入**：用户向客户端发送查询或指令
2. **模型分析**：大语言模型分析用户输入，确定是否需要工具辅助
3. **工具选择**：如果需要工具，模型选择最合适的工具并准备参数
4. **参数准备**：模型将用户输入转换为工具所需的参数格式
5. **工具调用**：客户端通过MCP协议调用相应的工具
6. **结果处理**：工具执行操作并返回结果
7. **模型解释**：模型接收工具结果，生成最终回答
8. **用户输出**：客户端将模型的回答展示给用户

## 安全考虑

使用本系统时，请注意以下安全方面的考虑：

1. **敏感信息保护**：在配置文件中存储的API密钥和其他敏感信息应当妥善保护
2. **权限控制**：确保Redmine API密钥仅有必要的最小权限
3. **输入验证**：系统自动验证工具参数，但用户输入仍需谨慎处理
4. **错误处理**：系统设计了错误处理机制，以防止敏感信息泄露

## 常见问题

**Q: 如何更改使用的大语言模型？**
A: 修改`custom_interactive_client.py`中的`ChatOpenAI`配置，更改`model`和`openai_api_base`参数。

**Q: 如何调试MCP服务器？**
A: 设置环境变量`MCP_DEBUG=1`，服务器会输出详细的调试信息。

**Q: 如何添加新的工具？**
A: 在相应的MCP服务器文件中添加新的工具函数，使用`@mcp.tool()`装饰器。

**Q: 可以将系统与其他LLM平台集成吗？**
A: 是的，只需修改`ChatOpenAI`配置，将其指向不同的API端点，如OpenAI、Azure或其他兼容OpenAI API的服务。

## 未来改进计划

- 添加用户认证机制
- 支持更多传输方式（如WebSocket）
- 实现工具结果缓存
- 提供Web界面
- 支持更多Redmine功能
- 增加真实天气API集成

## 许可证

MIT

## 贡献

欢迎提交问题和建议！ 本项目开源，欢迎社区贡献。贡献方式包括：
- 提交bug报告
- 功能请求
- 文档改进
- 代码贡献

请通过GitHub Issues提交问题和建议。 