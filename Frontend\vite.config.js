import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  server: {
    host: '0.0.0.0',    // 允许局域网访问
    port: 5173,         // 指定端口号
    strictPort: true,   // 端口被占用时直接退出
    open: true,         // 自动打开浏览器
    proxy: {
      // 本地MCP服务的API代理
      '/api': {
        target: 'http://127.0.0.1:8080',  // 改用127.0.0.1而不是localhost
        changeOrigin: true,
        secure: false,
        ws: true,
        // 添加日志
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.error('代理错误详情:', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('发送代理请求:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('接收代理响应:', proxyRes.statusCode, req.url);
          });
        }
      },
      // RAGFlow服务的API代理
      '/ragflow': {
        target: 'http://127.0.0.1:9222/',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/ragflow/, ''),
        secure: false
      }
    }
  },
})
