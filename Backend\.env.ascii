# Base Configuration
APP_ENV=development
SECRET_KEY=your_secret_key_here

# Database Configuration
DATABASE_URL=sqlite:///./app.db

# API Configuration
API_TITLE=IOT_Device_Monitoring_System
API_VERSION=0.1.0
API_PREFIX=/api

# Alert Log Configuration
# Set to TRUE to enable detailed alert logs, FALSE to disable
ALERT_LOGGING_ENABLED=TRUE

# Alert log level: DEBUG, INFO, WARNING, ERROR, CRITICAL
ALERT_LOG_LEVEL=INFO

# MQTT Configuration
MQTT_HOST=************
MQTT_PORT=8083
MQTT_PATH=/mqtt
MQTT_USE_WEBSOCKET=TRUE 