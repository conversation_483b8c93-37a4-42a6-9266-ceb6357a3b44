@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo RTSP服务器启动工具
echo =================

:: 检查是否已下载mediamtx
if not exist "%~dp0mediamtx.exe" (
    echo 未找到mediamtx.exe，正在下载...
    
    :: 创建临时目录
    mkdir "%~dp0temp" 2>nul
    
    :: 检查是否有curl可用
    where curl >nul 2>nul
    if !errorlevel! neq 0 (
        echo 错误：未找到curl。请手动下载mediamtx并放到此目录。
        echo 下载地址：https://github.com/bluenviron/mediamtx/releases
        goto :end
    )
    
    :: 为Windows下载最新版本的mediamtx
    echo 正在下载mediamtx...
    curl -L -o "%~dp0temp\mediamtx.zip" "https://github.com/bluenviron/mediamtx/releases/download/v1.12.2/mediamtx_v1.12.2_windows_amd64.zip"
    
    if !errorlevel! neq 0 (
        echo 下载失败，请检查网络连接或手动下载。
        goto :end
    )
    
    :: 解压文件
    echo 正在解压文件...
    
    :: 使用PowerShell解压(内置于Windows 10/11)
    powershell -command "Expand-Archive -Path '%~dp0temp\mediamtx.zip' -DestinationPath '%~dp0temp' -Force"
    
    if !errorlevel! neq 0 (
        echo 解压失败，请手动解压文件。
        goto :end
    )
    
    :: 移动文件到当前目录
    move "%~dp0temp\mediamtx.exe" "%~dp0" >nul 2>nul
    move "%~dp0temp\mediamtx.yml" "%~dp0" >nul 2>nul
    
    :: 清理临时文件
    rmdir /s /q "%~dp0temp" >nul 2>nul
    
    if not exist "%~dp0mediamtx.exe" (
        echo 下载或解压失败，请手动下载mediamtx并放到此目录。
        echo 下载地址：https://github.com/bluenviron/mediamtx/releases
        goto :end
    )
    
    echo mediamtx下载并解压成功！
)

:: 创建mediamtx配置文件
if not exist "%~dp0mediamtx.yml" (
    echo 创建mediamtx配置文件...
    (
        echo paths:
        echo   all:
        echo     readUser: admin
        echo     readPass: admin123
        echo     publishUser: admin
        echo     publishPass: admin123
    ) > "%~dp0mediamtx.yml"
)

echo 启动RTSP服务器...
echo 服务器已启动，RTSP地址为：rtsp://localhost:8554/
echo 用户名：admin
echo 密码：admin123
echo.
echo 按Ctrl+C停止服务器

:: 启动mediamtx服务器
"%~dp0mediamtx.exe"

:end
pause 