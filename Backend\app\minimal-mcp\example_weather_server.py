from mcp.server.fastmcp import FastMCP

mcp = FastMCP("Weather")

@mcp.tool()
def get_weather(location: str) -> str:
    """Get the weather for a location"""
    # 这是一个模拟的天气服务，实际应用中可以连接到真实的天气API
    weather_data = {
        "北京": "晴天, 28°C",
        "上海": "多云, 26°C",
        "广州": "小雨, 30°C",
        "深圳": "阵雨, 29°C",
        "成都": "晴朗, 25°C",
        "盘锦": "多云, 20°C",
        "沈阳": "多云, 21°C",
    }
    
    return weather_data.get(location, f"无法获取{location}的天气信息")

if __name__ == "__main__":
    mcp.run(transport="stdio") 