
<!DOCTYPE html>
<html>
<head>
    <title>RTSP 代理服务器</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        h2 {
            color: #444;
            margin-top: 20px;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .streams-container {
            margin-top: 20px;
        }
        .stream-item {
            border: 1px solid #ddd;
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .stream-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        .stream-title {
            font-weight: bold;
            font-size: 18px;
        }
        .stream-controls button {
            padding: 5px 10px;
            margin-left: 5px;
            background-color: #f44336;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .stream-controls button.start-btn {
            background-color: #4CAF50;
        }
        .stream-preview {
            width: 100%;
            height: 300px;
            background-color: #000;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
        }
        .stream-preview img {
            max-width: 100%;
            max-height: 100%;
        }
        .stream-info {
            margin-top: 10px;
            font-size: 14px;
            color: #666;
        }
        .create-form {
            margin-top: 20px;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
        }
        .form-row {
            margin-bottom: 10px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="number"] {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button[type="submit"] {
            padding: 8px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        .error {
            color: #f44336;
            margin-top: 5px;
        }
        .success {
            color: #4CAF50;
            margin-top: 5px;
        }
        .config-info {
            margin: 15px 0;
            padding: 10px;
            background-color: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .config-btn {
            padding: 5px 10px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RTSP 代理服务器</h1>
        
        <div class="create-form">
            <h2>创建新的RTSP流</h2>
            <form id="createStreamForm">
                <div class="form-row">
                    <label for="url">RTSP URL:</label>
                    <input type="text" id="url" name="url" placeholder="rtsp://username:password@ip:port/path" required>
                </div>
                <div class="form-row">
                    <label for="name">流名称:</label>
                    <input type="text" id="name" name="name" placeholder="唯一标识符，如camera1" required>
                </div>
                <div class="form-row">
                    <label for="fps">帧率 (FPS):</label>
                    <input type="number" id="fps" name="fps" value="15" min="1" max="30">
                </div>
                <div class="form-row">
                    <label for="width">宽度:</label>
                    <input type="number" id="width" name="width" value="640" min="160" max="1920">
                </div>
                <div class="form-row">
                    <label for="height">高度:</label>
                    <input type="number" id="height" name="height" value="480" min="120" max="1080">
                </div>
                <button type="submit">创建流</button>
                <div id="formMessage"></div>
            </form>
        </div>
        
        <h2>当前活跃流</h2>
        <div class="config-info">
            <p>流配置保存在: <code id="configPath">stream_config.json</code></p>
            <p>启动时会自动加载保存的流。如不希望自动加载，请使用 <code>--no-load-config</code> 参数启动。</p>
            <button id="saveConfigBtn" class="config-btn">手动保存配置</button>
            <button id="loadConfigBtn" class="config-btn">从配置加载流</button>
            <span id="saveConfigMessage"></span>
        </div>
        <div id="streamsContainer" class="streams-container">
            <div>加载中...</div>
        </div>
    </div>

    <script>
        // 获取所有流并显示
        function loadStreams() {
            fetch('/api/streams')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('streamsContainer');
                    container.innerHTML = '';
                    
                    if (Object.keys(data).length === 0) {
                        container.innerHTML = '<div>没有活跃的流</div>';
                        return;
                    }
                    
                    for (const [name, info] of Object.entries(data)) {
                        const streamItem = document.createElement('div');
                        streamItem.className = 'stream-item';
                        
                        const header = document.createElement('div');
                        header.className = 'stream-header';
                        
                        const title = document.createElement('div');
                        title.className = 'stream-title';
                        title.textContent = name;
                        
                        const controls = document.createElement('div');
                        controls.className = 'stream-controls';
                        
                        if (info.running) {
                            const stopButton = document.createElement('button');
                            stopButton.textContent = '停止';
                            stopButton.onclick = () => stopStream(name);
                            controls.appendChild(stopButton);
                        } else {
                            const startButton = document.createElement('button');
                            startButton.textContent = '启动';
                            startButton.className = 'start-btn';
                            startButton.onclick = () => startStream(name);
                            controls.appendChild(startButton);
                        }
                        
                        header.appendChild(title);
                        header.appendChild(controls);
                        
                        const preview = document.createElement('div');
                        preview.className = 'stream-preview';
                        
                        if (info.running) {
                            const img = document.createElement('img');
                            // 添加时间戳防止缓存
                            img.src = `/api/stream/${name}/mjpeg?t=${Date.now()}`;
                            preview.appendChild(img);
                        } else {
                            preview.textContent = '流已停止';
                        }
                        
                        const infoDiv = document.createElement('div');
                        infoDiv.className = 'stream-info';
                        infoDiv.innerHTML = `
                            <div>URL: ${info.url}</div>
                            <div>分辨率: ${info.resolution}</div>
                            <div>FPS: ${info.fps}</div>
                            <div>客户端数: ${info.clients}</div>
                            <div>创建时间: ${new Date(info.created_at).toLocaleString()}</div>
                            ${info.has_error ? `<div style="color:red">错误: ${info.error}</div>` : ''}
                        `;
                        
                        streamItem.appendChild(header);
                        streamItem.appendChild(preview);
                        streamItem.appendChild(infoDiv);
                        
                        container.appendChild(streamItem);
                    }
                })
                .catch(error => {
                    console.error('加载流失败:', error);
                    document.getElementById('streamsContainer').innerHTML = 
                        `<div class="error">加载流失败: ${error.message}</div>`;
                });
        }
        
        // 停止流
        function stopStream(name) {
            if (!confirm(`确定要停止流 "${name}" 吗?`)) return;
            
            // 询问是否从配置中移除
            const removeFromConfig = confirm(`是否要从配置文件中移除流 "${name}"?
点击"确定"移除, 点击"取消"仅停止但保留配置(推荐)`);
            
            fetch(`/api/streams/${name}/stop`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ remove_from_config: removeFromConfig })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadStreams();
                } else {
                    alert(`停止流失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('停止流错误:', error);
                alert(`停止流错误: ${error.message}`);
            });
        }
        
        // 启动/重启流
        function startStream(name) {
            fetch(`/api/streams/${name}/start`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    loadStreams();
                } else {
                    alert(`启动流失败: ${data.error}`);
                }
            })
            .catch(error => {
                console.error('启动流错误:', error);
                alert(`启动流错误: ${error.message}`);
            });
        }
        
        // 创建新流
        document.getElementById('createStreamForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                url: document.getElementById('url').value,
                name: document.getElementById('name').value,
                fps: parseInt(document.getElementById('fps').value),
                width: parseInt(document.getElementById('width').value),
                height: parseInt(document.getElementById('height').value)
            };
            
            fetch('/api/streams/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                const messageDiv = document.getElementById('formMessage');
                if (data.success) {
                    messageDiv.className = 'success';
                    messageDiv.textContent = `成功创建流: ${formData.name}`;
                    // 重置表单
                    document.getElementById('createStreamForm').reset();
                    // 重新加载流列表
                    loadStreams();
                } else {
                    messageDiv.className = 'error';
                    messageDiv.textContent = `创建流失败: ${data.error}`;
                }
            })
            .catch(error => {
                console.error('创建流错误:', error);
                const messageDiv = document.getElementById('formMessage');
                messageDiv.className = 'error';
                messageDiv.textContent = `创建流错误: ${error.message}`;
            });
        });
        
        // 页面加载时获取流
        document.addEventListener('DOMContentLoaded', function() {
            loadStreams();
            // 更新配置文件路径
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('configPath').textContent = data.config_file;
                })
                .catch(error => console.error('获取状态失败:', error));
                
            // 添加保存配置按钮事件处理
            document.getElementById('saveConfigBtn').addEventListener('click', function() {
                fetch('/api/config/save', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    const messageElem = document.getElementById('saveConfigMessage');
                    if (data.success) {
                        messageElem.textContent = data.message;
                        messageElem.style.color = 'green';
                    } else {
                        messageElem.textContent = '保存失败: ' + data.error;
                        messageElem.style.color = 'red';
                    }
                    // 3秒后清除消息
                    setTimeout(() => {
                        messageElem.textContent = '';
                    }, 3000);
                })
                .catch(error => {
                    console.error('保存配置失败:', error);
                    const messageElem = document.getElementById('saveConfigMessage');
                    messageElem.textContent = '保存失败: ' + error.message;
                    messageElem.style.color = 'red';
                });
            });
            
            // 添加加载配置按钮事件处理
            document.getElementById('loadConfigBtn').addEventListener('click', function() {
                fetch('/api/config/load', {
                    method: 'POST'
                })
                .then(response => response.json())
                .then(data => {
                    const messageElem = document.getElementById('saveConfigMessage');
                    if (data.success) {
                        messageElem.textContent = data.message;
                        messageElem.style.color = 'green';
                        // 重新加载流列表
                        loadStreams();
                    } else {
                        messageElem.textContent = '加载失败: ' + data.error;
                        messageElem.style.color = 'red';
                    }
                    // 3秒后清除消息
                    setTimeout(() => {
                        messageElem.textContent = '';
                    }, 3000);
                })
                .catch(error => {
                    console.error('加载配置失败:', error);
                    const messageElem = document.getElementById('saveConfigMessage');
                    messageElem.textContent = '加载失败: ' + error.message;
                    messageElem.style.color = 'red';
                });
            });
        });
        
        // 定期刷新流列表
        setInterval(loadStreams, 30000);
    </script>
</body>
</html>
        