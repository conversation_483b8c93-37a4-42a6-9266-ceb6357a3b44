import os
from typing import Annotated, Dict, List, Literal, TypedDict, Tuple, Any
from enum import Enum

from langchain_core.messages import AIMessage, HumanMessage
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
from langchain_ollama import ChatOllama
from langchain_core.output_parsers import JsonOutputParser

class QuestionType(str, Enum):
    """问题类型枚举"""
    KNOWLEDGE_BASE = "knowledge_base"  # 知识库查询
    MQTT_DATA = "mqtt_data"  # MQTT设备数据查询
    HYBRID = "hybrid"  # 混合查询（同时需要知识库和MQTT数据）
    GENERAL = "general"  # 普通问题，不需要外部信息

class ClassificationResult(TypedDict):
    """分类结果数据结构"""
    question_type: QuestionType
    explanation: str
    confidence: float  # 分类置信度，0-1
    device_ids: List[str]  # 可能包含的设备ID
    topics: List[str]  # 可能需要订阅的MQTT主题
    knowledge_areas: List[str]  # 可能涉及的知识领域

class QuestionClassifierAgent:
    """问题分类智能体，用于分析用户问题并确定需要的工具和数据源"""
    
    def __init__(self, model_name: str = "qwen2.5:32b"):
        """初始化分类智能体
        
        Args:
            model_name: 使用的语言模型名称
        """
        # 初始化语言模型
        self.llm = ChatOllama(
            model=model_name,
            temperature=0,  # 降低温度以获得更确定性的分类结果
        )
        
        # 创建分类模板
        self.classification_prompt = ChatPromptTemplate.from_messages([
            ("system", """你是一个专门分析用户问题类型的智能分类器。你的任务是分析用户的问题，并确定该问题属于以下哪种类型：

1. KNOWLEDGE_BASE - 知识库查询：需要从文档、规程、规范等知识库中检索信息的问题
2. MQTT_DATA - 设备数据查询：涉及实时设备数据、传感器数据、监控数据的问题
3. HYBRID - 混合查询：同时需要知识库内容和实时设备数据的问题
4. GENERAL - 普通问题：不需要特殊工具或外部数据的一般性问题

你必须以JSON格式返回结果，包含以下字段：
- question_type: 上述四种类型之一
- explanation: 解释为什么属于这个类型
- confidence: 分类的置信度，0-1之间的小数
- device_ids: 问题中可能提到的设备ID列表（如H005、JH005、HN15V25等）
- topics: 可能需要订阅的MQTT主题列表，例如/HN3S1/HN15V25（如果能推断）
- knowledge_areas: 可能涉及的知识领域列表，如安全规程、操作手册等

以下是一些提示：
- 如果问题中包含"油井"、"设备"、"数据"、"实时"、"监控"等词，可能是MQTT_DATA
- 如果问题中包含"规定"、"流程"、"标准"、"规范"、"手册"等词，可能是KNOWLEDGE_BASE
- 如果同时询问设备状态和相关规程，那就是HYBRID类型
- 如果是一般性的问候或闲聊，则为GENERAL类型

请仅返回JSON格式的结果，不要有其他文本。"""),
            ("human", "问题: {question}\n\n聊天历史: {chat_history}"),
        ])
        
        # 创建输出解析器，用于处理JSON输出
        self.parser = JsonOutputParser()
        
        # 创建完整的分类链
        self.classification_chain = self.classification_prompt | self.llm | self.parser
        
    def classify_question(self, question: str, chat_history: List[Dict[str, str]] = None) -> Dict[str, Any]:
        """
        对用户问题进行分类，判断属于哪种类型的查询
        
        Args:
            question: 用户问题文本
            chat_history: 聊天历史，格式为[{"role": "user", "content": "..."}, {"role": "assistant", "content": "..."}]
            
        Returns:
            Dict: 分类结果，包含问题类型、解释、置信度等信息
        """
        # 设置默认参数
        if chat_history is None:
            chat_history = []
            
        # 格式化历史记录以便在提示中使用
        formatted_history = ""
        if chat_history:
            formatted_history = "\n".join([
                f"{item['role']}: {item['content']}" 
                for item in chat_history[-5:]  # 只使用最近的5条消息作为上下文
            ])
        
        # 调用分类链
        try:
            result = self.classification_chain.invoke({
                "question": question,
                "chat_history": formatted_history
            })
            
            # 验证结果格式
            if not isinstance(result, dict):
                # 如果返回的不是字典，尝试进行JSON解析
                try:
                    import json
                    result = json.loads(result)
                except:
                    # 如果解析失败，返回默认值
                    return {
                        "question_type": QuestionType.GENERAL,
                        "explanation": "无法解析分类结果，使用默认分类",
                        "confidence": 0.5,
                        "device_ids": [],
                        "topics": [],
                        "knowledge_areas": []
                    }
            
            # 确保返回的是一个有效的ClassificationResult
            return {
                "question_type": result.get("question_type", QuestionType.GENERAL),
                "explanation": result.get("explanation", ""),
                "confidence": float(result.get("confidence", 0.5)),
                "device_ids": result.get("device_ids", []),
                "topics": result.get("topics", []),
                "knowledge_areas": result.get("knowledge_areas", [])
            }
            
        except Exception as e:
            print(f"问题分类出错: {e}")
            # 出错时返回默认分类
            return {
                "question_type": QuestionType.GENERAL,
                "explanation": f"分类过程出错: {str(e)}",
                "confidence": 0.5,
                "device_ids": [],
                "topics": [],
                "knowledge_areas": []
            }

# 单元测试代码
if __name__ == "__main__":
    # 创建分类器
    classifier = QuestionClassifierAgent()
    
    # 测试样例
    test_questions = [
        "站场巡检工作有哪些规定？",
        "H005的实时数据是什么？",
        "油井设备故障时应该如何处理？需要查看哪些监控数据？",
        "你好，今天天气怎么样？"
    ]
    
    # 测试分类
    for q in test_questions:
        result = classifier.classify_question(q)
        print(f"\n问题: {q}")
        print(f"分类结果: {result['question_type']}")
        print(f"解释: {result['explanation']}")
        print(f"置信度: {result['confidence']}")
        if result["device_ids"]:
            print(f"设备ID: {', '.join(result['device_ids'])}")
        if result["topics"]:
            print(f"可能主题: {', '.join(result['topics'])}")
        if result["knowledge_areas"]:
            print(f"知识领域: {', '.join(result['knowledge_areas'])}") 