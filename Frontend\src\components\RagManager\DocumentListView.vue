<template>
    <div class="document-list-view" style="background: white; color: #333;">
      <div class="list-header">
        <h3 style="color: #1890ff;">文档列表</h3>
        <div class="list-controls">
          <!-- 搜索区域 -->
          <div class="search-area">
            <input 
              type="text" 
              v-model="searchQuery" 
              placeholder="搜索数据集名称..."
              class="search-input"
              @input="handleSearch"
              style="background: #f5f5f5; color: #333; border: 1px solid #d9d9d9;"
            >
          </div>
          <button @click="refreshDocuments" :disabled="loading" class="refresh-btn" style="background: #f0f2f5; color: #333; border: 1px solid #d9d9d9;">
            <span class="refresh-icon" :class="{ 'rotating': loading }">🔄</span>
            刷新
          </button>
        </div>
      </div>

      <div v-if="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
      </div>
      <div v-else-if="error" class="error-message">
        {{ error }}
        <button @click="refreshDocuments">重试</button>
      </div>
      <div v-else-if="documents.length === 0" class="empty-state">
        暂无文档
      </div>
      <div v-else class="document-table">
        <div class="document-table-header">
          <div class="col-checkbox">
            <input 
              type="checkbox" 
              :checked="isAllSelected"
              @change="handleSelectAll"
              :disabled="loading || documents.length === 0"
            >
          </div>
          <div class="col-filename">文件名</div>
          <div class="col-type">类型</div>
          <div class="col-size">大小</div>
          <div class="col-status">解析状态</div>
          <div class="col-progress">解析进度</div>
          <div class="col-time sortable" @click="handleSortChange('create_time')">
            创建时间
            <span class="sort-icon" v-if="sortField === 'create_time'">
              {{ sortDesc ? '↓' : '↑' }}
            </span>
          </div>
          <div class="col-time sortable" @click="handleSortChange('update_time')">
            更新时间
            <span class="sort-icon" v-if="sortField === 'update_time'">
              {{ sortDesc ? '↓' : '↑' }}
            </span>
          </div>
          <div class="col-actions">
            <button 
              v-if="selectedDocuments.length > 0"
              @click="batchDelete"
              class="batch-delete-btn"
              :disabled="isDeleting"
            >
              批量删除 ({{ selectedDocuments.length }})
            </button>
            <span v-else>操作</span>
          </div>
        </div>
        <div class="document-table-body">
          <div v-for="doc in filteredDocuments" :key="doc.id" class="document-table-row">
            <div class="col-checkbox">
              <input 
                type="checkbox" 
                :checked="isSelected(doc.id)"
                @change="handleSelect(doc.id)"
                :disabled="loading || isDeleting"
              >
            </div>
            <div class="col-filename" :title="doc.file_name">
              <span class="file-icon">{{ getFileIcon(doc.file_type) }}</span>
              <span class="filename-text">{{ doc.file_name }}</span>
            </div>
            <div class="col-type">{{ doc.file_type.toUpperCase() }}</div>
            <div class="col-size">{{ formatFileSize(doc.file_size) }}</div>
            
            <!-- 解析状态 -->
            <div class="col-status" :class="getStatusClass(doc.process_status)">
              {{ getStatusText(doc.process_status) }}
              <span v-if="doc.process_status === 'FAIL'" class="error-tooltip" :title="doc.description">❗</span>
            </div>
            
            <!-- 解析进度 -->
            <div class="col-progress">
              <div v-if="doc.process_status === 'RUNNING'" class="progress-bar-container">
                <div class="progress-bar-inner" :style="{ width: `${doc.progress}%` }"></div>
                <span class="progress-text">{{ Math.round(doc.progress) }}%</span>
              </div>
              <div v-else-if="doc.process_status === 'DONE'" class="progress-complete">
                <span>完成 ({{ doc.chunk_count }}个片段)</span>
              </div>
              <div v-else-if="doc.process_status === 'FAIL'" class="progress-failed">
                失败
              </div>
              <div v-else-if="doc.process_status === 'UNSTART'" class="progress-not-started">
                未开始
              </div>
              <div v-else-if="doc.process_status === 'CANCEL'" class="progress-cancelled">
                已取消
              </div>
            </div>
            
            <div class="col-time">{{ formatDate(doc.created_at) }}</div>
            <div class="col-time">{{ formatDate(doc.updated_at) }}</div>
            <div class="col-actions">
              <div class="action-buttons">
                <!-- 添加开始解析按钮 -->
                <button 
                  v-if="doc.process_status === 'UNSTART' || doc.process_status === 'FAIL' || doc.process_status === 'CANCEL'" 
                  @click="startParsing(doc.id)" 
                  class="action-btn parse" 
                  title="开始解析"
                  :disabled="isProcessing(doc.id)"
                >
                  <span class="btn-icon" :class="{ 'spinning': isProcessing(doc.id) }">🔄</span>
                  <span class="btn-text">{{ isProcessing(doc.id) ? '处理中...' : '解析' }}</span>
                </button>
                
                <!-- 添加取消解析按钮 -->
                <button 
                  v-if="doc.process_status === 'RUNNING'" 
                  @click="cancelParsing(doc.id)" 
                  class="action-btn cancel-parse" 
                  title="取消解析"
                  :disabled="isProcessing(doc.id)"
                >
                  <span class="btn-icon">⏹️</span>
                  <span class="btn-text">取消</span>
                </button>
                
                <button @click="downloadDocument(doc.id, doc.file_name)" class="action-btn download" title="下载文档">
                  <span class="btn-icon">⬇️</span>
                  <span class="btn-text">下载</span>
                </button>
                <button @click="deleteDocument(doc.id)" class="action-btn delete" title="删除文档" :disabled="isDeleting">
                  <span class="btn-icon" :class="{ 'spinning': isDeleting && pendingDeleteId === doc.id }" v-if="isDeleting && pendingDeleteId === doc.id">🔄</span>
                  <span class="btn-icon" v-else>🗑️</span>
                  <span class="btn-text">{{ isDeleting && pendingDeleteId === doc.id ? '删除中...' : '删除' }}</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页组件 -->
      <div class="pagination">
        <button @click="handlePageChange(currentPage - 1)" :disabled="currentPage === 1" class="prev-btn">上一页</button>
        
        <!-- 添加页码选择器 -->
        <div class="page-selector">
          <span>第</span>
          <select v-model="currentPage" @change="handlePageChange(currentPage)" class="page-select">
            <option v-for="page in totalPages" :key="page" :value="page">{{ page }}</option>
          </select>
          <span>页</span>
        </div>
        
        <span class="page-info">共 {{ total }} 个文档</span>
        
        <!-- 添加每页数量选择器 -->
        <div class="page-size-selector">
          <span>每页</span>
          <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
            <option v-for="size in pageSizeOptions" :key="size" :value="size">{{ size }}</option>
          </select>
          <span>条</span>
        </div>
        
        <button @click="handlePageChange(currentPage + 1)" :disabled="currentPage === totalPages" class="next-btn">下一页</button>
      </div>

      <!-- 文档详情弹窗 -->
      <teleport to="body">
        <div v-if="showDocumentModal" class="modal">
          <div class="modal-content">
            <h3>文档详情</h3>
            <div v-if="selectedDocument">
              <p><strong>文件名:</strong> {{ selectedDocument.file_name }}</p>
              <p><strong>文件类型:</strong> {{ selectedDocument.file_type }}</p>
              <p><strong>文件大小:</strong> {{ formatFileSize(selectedDocument.file_size) }}</p>
              <p><strong>创建时间:</strong> {{ formatDate(selectedDocument.created_at) }}</p>
              <p><strong>更新时间:</strong> {{ formatDate(selectedDocument.updated_at) }}</p>
              <div class="chunks" v-if="selectedDocument.chunks && selectedDocument.chunks.length > 0">
                <h4>文档片段:</h4>
                <div v-for="chunk in selectedDocument.chunks" :key="chunk.id" class="chunk">
                  {{ chunk.content }}
                </div>
              </div>
            </div>
            <div class="modal-actions">
              <button @click="closeDocumentModal" class="cancel">关闭</button>
            </div>
          </div>
        </div>
      </teleport>

      <!-- 删除确认弹窗 -->
      <teleport to="body">
        <div v-if="showDeleteModal" class="modal delete-modal">
          <div class="modal-content delete-modal-content">
            <h3>确认删除</h3>
            <p>确定要删除这个文档吗？此操作无法撤销。</p>
            <div class="modal-actions">
              <button @click="confirmDelete" class="delete" :disabled="isDeleting">
                <span class="btn-icon" :class="{ 'spinning': isDeleting }" v-if="isDeleting">🔄</span>
                {{ isDeleting ? '删除中...' : '确认删除' }}
              </button>
              <button @click="cancelDelete" class="cancel" :disabled="isDeleting">取消</button>
            </div>
          </div>
        </div>
      </teleport>

      <!-- 批量删除进度弹窗 -->
      <teleport to="body">
        <div v-if="showDeleteProgress" class="modal delete-progress-modal">
          <div class="modal-content">
            <h3 class="progress-title">批量删除进度</h3>
            <div class="delete-progress">
              <div class="progress-bar">
                <div 
                  class="progress-bar-inner" 
                  :style="{ width: `${deleteProgress}%` }"
                ></div>
              </div>
              <div class="progress-text">
                <span class="btn-icon spinning">🔄</span>
                批量处理中: {{ processedDeletes }}/{{ totalDeletes }}
              </div>
              <div class="delete-status" v-if="deleteErrors.length > 0">
                <p>删除失败的文件：</p>
                <ul>
                  <li v-for="error in deleteErrors" :key="error.id">
                    {{ error.name }} - {{ error.reason }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </teleport>

      <!-- 批量删除确认弹窗 -->
      <teleport to="body">
        <div v-if="showBatchDeleteModal" class="modal delete-modal">
          <div class="modal-content delete-modal-content">
            <h3>确认批量删除</h3>
            <p>确定要删除选中的 {{ selectedDocuments.length }} 个文档吗？此操作无法撤销。</p>
            <div class="selected-files-preview" v-if="selectedDocuments.length > 0">
              <div class="preview-header">选中的文件：</div>
              <div class="preview-list">
                <div v-for="docId in selectedDocuments.slice(0, 3)" :key="docId" class="preview-item">
                  {{ documents.find(doc => doc.id === docId)?.file_name }}
                </div>
                <div v-if="selectedDocuments.length > 3" class="preview-more">
                  还有 {{ selectedDocuments.length - 3 }} 个文件...
                </div>
              </div>
            </div>
            <div class="modal-actions">
              <button @click="confirmBatchDelete" class="delete" :disabled="isDeleting">
                <span class="btn-icon">🗑️</span>
                {{ isDeleting ? '删除中...' : '确认删除' }}
              </button>
              <button @click="cancelBatchDelete" class="cancel" :disabled="isDeleting">取消</button>
            </div>
          </div>
        </div>
      </teleport>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, onActivated, onUnmounted } from 'vue'
import { debounce } from 'lodash-es'
import http from '@/utils/axios'

const documents = ref([])
const loading = ref(false)
const error = ref('')
const searchQuery = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const pageSizeOptions = [10, 20, 30, 50, 100]
const total = ref(0)
const lastFetchTime = ref(0)
const CACHE_DURATION = 30000 // 30秒缓存

// 添加排序相关的响应式数据
const sortField = ref('create_time')
const sortDesc = ref(true)

// 文档详情相关
const showDocumentModal = ref(false)
const selectedDocument = ref(null)

// 删除相关
const showDeleteModal = ref(false)
const pendingDeleteId = ref(null)
const selectedDocuments = ref([])
const isDeleting = ref(false)
const showDeleteProgress = ref(false)
const deleteProgress = ref(0)
const processedDeletes = ref(0)
const totalDeletes = ref(0)
const deleteErrors = ref([])
const showBatchDeleteModal = ref(false)

// 解析相关
const processingDocuments = ref(new Set())
const progressPollingTimers = {}

// 计算属性
const filteredDocuments = computed(() => {
  if (!documents.value) return []
  return documents.value
})

const isAllSelected = computed(() => {
  return documents.value.length > 0 && 
         documents.value.every(doc => selectedDocuments.value.includes(doc.id))
})

// 计算总页数
const totalPages = computed(() => {
  return Math.max(1, Math.ceil(total.value / pageSize.value))
})

// 获取文档状态对应的CSS类
const getStatusClass = (status) => {
  const statusClasses = {
    'UNSTART': 'status-unstart',
    'RUNNING': 'status-running',
    'CANCEL': 'status-cancel',
    'DONE': 'status-done',
    'FAIL': 'status-fail'
  }
  return statusClasses[status] || 'status-unknown'
}

// 获取文档状态的显示文本
const getStatusText = (status) => {
  const statusTexts = {
    'UNSTART': '未开始',
    'RUNNING': '处理中',
    'CANCEL': '已取消',
    'DONE': '已完成',
    'FAIL': '失败'
  }
  return statusTexts[status] || '未知'
}

// 检查文档是否正在处理中
const isProcessing = (docId) => {
  return processingDocuments.value.has(docId)
}

// 方法
const fetchDocuments = async () => {
  if (loading.value) return

  loading.value = true
  error.value = ''

  try {
    // 构建查询参数
    const params = {
      page: currentPage.value,
      page_size: pageSize.value,
      orderby: sortField.value,
      desc: sortDesc.value
    }

    // 如果有搜索关键词，添加到查询参数
    if (searchQuery.value) {
      params.keywords = searchQuery.value
    }

    const response = await http.get(`/ragflow/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents`, {
      params,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG'
      }
    })

    // 检查响应数据
    if (response?.code === 0 && response?.data?.docs) {
      console.log('API响应文档数据:', response.data.docs[0]); // 添加调试日志
      
      documents.value = response.data.docs.map(doc => {
        // 确保chunk_num字段有值，如果没有则尝试使用其他可能的字段
        const chunkCount = doc.chunk_num !== undefined ? doc.chunk_num : 
                          (doc.chunk_count !== undefined ? doc.chunk_count : 0);
        
        return {
          id: doc.id,
          file_name: doc.name,
          file_type: doc.type || 'unknown',
          file_size: doc.size || 0,
          created_at: doc.create_date,
          updated_at: doc.update_date,
          status: doc.status,
          description: doc.progress_msg,
          document_count: chunkCount,
          chunk_count: chunkCount,
          progress: doc.progress * 100, // 确保进度是百分比
          process_status: doc.run || 'UNSTART'  // 确保有解析状态
        };
      })

      // 为正在处理的文档启动进度轮询
      documents.value.forEach(doc => {
        if (doc.process_status === 'RUNNING') {
          startProgressPolling(doc.id)
        }
      })

      total.value = response.data.total
      error.value = ''
      lastFetchTime.value = Date.now()
    } else {
      throw new Error(response?.message || '获取文档列表失败')
    }
  } catch (err) {
    console.error('获取文档列表失败:', err)
    console.error('错误详情:', {
      message: err.message,
      response: err.response?.data,
      status: err.response?.status,
      stack: err.stack,
      axiosError: err.isAxiosError,
      config: err.config
    })
    documents.value = []
    total.value = 0
    error.value = err.response?.data?.message || err.message || '获取文档列表失败'
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page) => {
  if (page < 1 || page > totalPages.value) return
  currentPage.value = page
  fetchDocuments()
}

const handlePageSizeChange = () => {
  // 重置到第一页
  currentPage.value = 1
  fetchDocuments()
}

const handleSearch = debounce(() => {
  currentPage.value = 1
  fetchDocuments()
}, 300)

const refreshDocuments = async () => {
  // 重置分页到第一页
  currentPage.value = 1
  await fetchDocuments()
}

// 开始解析文档
const startParsing = async (docId) => {
  try {
    processingDocuments.value.add(docId)
    
    // 修改为使用正确的Parse documents接口
    const response = await http.post(`/ragflow/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/chunks`, {
      document_ids: [docId]  // 按照API文档要求，传入document_ids数组
    }, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG'
      }
    })
    
    if (response?.code === 0) {
      // 更新文档状态
      const docIndex = documents.value.findIndex(doc => doc.id === docId)
      if (docIndex !== -1) {
        documents.value[docIndex].process_status = 'RUNNING'  // 设置状态为运行中
        documents.value[docIndex].progress = 0
      }
      
      // 启动轮询以更新进度
      startProgressPolling(docId)
    } else {
      throw new Error(response?.message || '开始解析失败')
    }
  } catch (err) {
    console.error('开始解析文档失败:', err)
    error.value = err.response?.data?.message || err.message || '开始解析文档失败'
  } finally {
    processingDocuments.value.delete(docId)
  }
}

// 获取最新的文档信息
const getLatestDocumentInfo = async (docId) => {
  try {
    const response = await http.get(`/ragflow/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents`, {
      params: {
        page: 1,
        page_size: 50
      },
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG'
      }
    })
    
    if (response?.code === 0 && response?.data?.docs) {
      // 从列表中找到对应的文档
      const docData = response.data.docs.find(doc => doc.id === docId)
      
      if (docData) {
        const docIndex = documents.value.findIndex(doc => doc.id === docId)
        
        if (docIndex !== -1) {
          // 更新文档信息
          documents.value[docIndex].updated_at = docData.update_date
          
          // 确保chunk_num字段有值，如果没有则尝试使用其他可能的字段
          const chunkCount = docData.chunk_num !== undefined ? docData.chunk_num : 
                            (docData.chunk_count !== undefined ? docData.chunk_count : 0);
          documents.value[docIndex].chunk_count = chunkCount
          
          // 确保状态为完成
          if (docData.run === '3' || docData.run === 3) {
            documents.value[docIndex].process_status = 'DONE'
            documents.value[docIndex].progress = 100
          }
          
          return true
        }
      }
    }
    return false
  } catch (err) {
    console.error('获取最新文档信息失败:', err)
    return false
  }
}

// 检查文档是否已经解析完成
const checkDocumentCompletion = async (docId) => {
  try {
    // 先尝试获取最新文档信息
    const updated = await getLatestDocumentInfo(docId)
    
    // 如果已经成功更新了文档信息，则不需要刷新整个列表
    if (updated) {
      return
    }
    
    // 如果没有成功更新，则刷新整个文档列表
    await refreshDocuments()
    
    // 检查文档状态
    const doc = documents.value.find(d => d.id === docId)
    if (doc) {
      // 如果文档状态不是运行中，说明已经完成或失败
      if (doc.process_status !== 'RUNNING') {
        return
      }
      
      // 如果文档状态仍然是运行中，但实际上可能已经完成
      // 设置为完成状态
      const docIndex = documents.value.findIndex(d => d.id === docId)
      if (docIndex !== -1) {
        documents.value[docIndex].process_status = 'DONE'
        documents.value[docIndex].progress = 100
      }
    }
  } catch (err) {
    console.error('检查文档完成状态失败:', err)
  }
}

// 轮询进度
const startProgressPolling = (docId) => {
  // 清除可能存在的旧定时器
  if (progressPollingTimers[docId]) {
    clearInterval(progressPollingTimers[docId])
    delete progressPollingTimers[docId]
  }
  
  let retryCount = 0;
  const maxRetries = 5;
  let documentCompleted = false;
  
  // 设置新的轮询定时器
  progressPollingTimers[docId] = setInterval(async () => {
    // 如果文档已经完成，停止轮询
    if (documentCompleted) {
      clearInterval(progressPollingTimers[docId])
      delete progressPollingTimers[docId]
      return
    }
    
    try {
      // 修改为使用文档列表接口获取进度
      const response = await http.get(`/ragflow/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents`, {
        params: {
          page: 1,
          page_size: 50 // 获取足够多的文档以确保包含我们需要的文档
        },
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG'
        }
      })
      
      // 检查响应是否有效
      if (response?.code === 0 && response?.data?.docs) {
        // 从列表中找到对应的文档
        const docData = response.data.docs.find(doc => doc.id === docId)
        
        if (docData) {
          console.log('文档状态数据:', docData)
          
          const docIndex = documents.value.findIndex(doc => doc.id === docId)
          
          if (docIndex !== -1) {
            // 更新文档状态和进度
            // 状态映射
            const statusMap = {
              0: 'UNSTART',
              1: 'UNSTART',
              2: 'RUNNING',
              3: 'DONE',
              4: 'FAIL',
              5: 'CANCEL'
            }
            
            // 解析run字段
            const runStatus = typeof docData.run === 'number' ? 
              statusMap[docData.run] : 
              (typeof docData.run === 'string' && !isNaN(parseInt(docData.run)) ? 
                statusMap[parseInt(docData.run)] : docData.run)
            
            // 更新文档信息
            documents.value[docIndex].process_status = runStatus
            documents.value[docIndex].progress = docData.progress * 100 // 确保进度是百分比
            documents.value[docIndex].description = docData.progress_msg
            
            // 确保chunk_num字段有值，如果没有则尝试使用其他可能的字段
            const chunkCount = docData.chunk_num !== undefined ? docData.chunk_num : 
                              (docData.chunk_count !== undefined ? docData.chunk_count : 0);
            documents.value[docIndex].chunk_count = chunkCount
            
            documents.value[docIndex].updated_at = docData.update_date
            
            // 如果状态是完成、失败或取消，停止轮询
            if (runStatus === 'DONE' || runStatus === 'FAIL' || runStatus === 'CANCEL') {
              documentCompleted = true
              clearInterval(progressPollingTimers[docId])
              delete progressPollingTimers[docId]
              return
            }
          }
        } else {
          // 如果在列表中找不到文档，增加重试次数
          retryCount++;
          
          if (retryCount >= maxRetries) {
            console.error('在文档列表中找不到指定文档，停止轮询')
            clearInterval(progressPollingTimers[docId])
            delete progressPollingTimers[docId]
            
            // 检查文档是否已经解析完成
            checkDocumentCompletion(docId)
          }
        }
      } else {
        // 增加重试次数
        retryCount++;
        
        if (retryCount >= maxRetries) {
          console.error('获取文档列表失败，停止轮询:', response?.message)
          clearInterval(progressPollingTimers[docId])
          delete progressPollingTimers[docId]
          
          // 检查文档是否已经解析完成
          checkDocumentCompletion(docId)
        }
      }
    } catch (err) {
      console.error('获取文档进度失败:', err)
      
      // 增加重试次数
      retryCount++;
      
      if (retryCount >= maxRetries) {
        console.error('获取文档进度失败，停止轮询')
        clearInterval(progressPollingTimers[docId])
        delete progressPollingTimers[docId]
        
        // 检查文档是否已经解析完成
        checkDocumentCompletion(docId)
      }
    }
  }, 10000) // 修改为每10秒轮询一次
}

const viewDocument = async (docId) => {
  try {
    const response = await http.get(`/ragflow/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents/${docId}`, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG'
      }
    })
    
    if (response?.code === 0 && response?.data) {
      // 确保chunk_num字段有值，如果没有则尝试使用其他可能的字段
      const chunkCount = response.data.chunk_num !== undefined ? response.data.chunk_num : 
                        (response.data.chunk_count !== undefined ? response.data.chunk_count : 0);
      
      selectedDocument.value = {
        ...response.data,
        filename: response.data.name,
        file_type: response.data.type,
        file_size: response.data.size,
        created_at: response.data.create_date,
        updated_at: response.data.update_date,
        chunk_count: chunkCount,
        chunks: response.data.chunks || []
      }
      showDocumentModal.value = true
    } else {
      throw new Error('无效的文档数据')
    }
  } catch (error) {
    console.error('获取文档详情失败:', error)
  }
}

const closeDocumentModal = () => {
  showDocumentModal.value = false
  selectedDocument.value = null
}

const deleteDocument = (docId) => {
  pendingDeleteId.value = docId
  showDeleteModal.value = true
}

const confirmDelete = async () => {
  try {
    isDeleting.value = true
    
    const response = await http.delete(`/ragflow/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents`, {
      data: { ids: [pendingDeleteId.value] },
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG'
      }
    })
    
    if (response?.code === 0) {
      showDeleteModal.value = false
      // 从选中列表中移除被删除的文档
      const index = selectedDocuments.value.indexOf(pendingDeleteId.value)
      if (index > -1) {
        selectedDocuments.value.splice(index, 1)
      }
      pendingDeleteId.value = null
      await refreshDocuments()
    } else {
      throw new Error(response?.message || '删除文档失败')
    }
  } catch (error) {
    console.error('删除文档失败:', error)
    error.value = error.response?.data?.message || error.message || '删除文档失败'
  } finally {
    isDeleting.value = false
  }
}

const cancelDelete = () => {
  showDeleteModal.value = false
  pendingDeleteId.value = null
}

const handleSelectAll = () => {
  if (isAllSelected.value) {
    selectedDocuments.value = []
  } else {
    selectedDocuments.value = documents.value.map(doc => doc.id)
  }
}

const handleSelect = (docId) => {
  const index = selectedDocuments.value.indexOf(docId)
  if (index === -1) {
    selectedDocuments.value.push(docId)
  } else {
    selectedDocuments.value.splice(index, 1)
  }
}

const isSelected = (docId) => {
  return selectedDocuments.value.includes(docId)
}

const batchDelete = () => {
  if (selectedDocuments.value.length === 0 || isDeleting.value) return
  showBatchDeleteModal.value = true
}

const confirmBatchDelete = async () => {
  try {
    showBatchDeleteModal.value = false
    showDeleteProgress.value = true
    isDeleting.value = true
    deleteProgress.value = 0
    processedDeletes.value = 0
    totalDeletes.value = selectedDocuments.value.length
    deleteErrors.value = []

    // 批量删除请求
    const response = await http.delete('/ragflow/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents', {
      data: { ids: selectedDocuments.value },
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG'
      }
    })

    if (response?.code === 0) {
      // 更新进度
      processedDeletes.value = selectedDocuments.value.length
      totalDeletes.value = selectedDocuments.value.length
      deleteProgress.value = 100

      await refreshDocuments()
      selectedDocuments.value = []
    } else {
      throw new Error(response?.message || '批量删除文档失败')
    }
  } catch (error) {
    console.error('批量删除文档失败:', error)
    deleteErrors.value = [{
      id: 'batch',
      name: '批量删除',
      reason: error.response?.data?.detail || error.message || '批量删除失败'
    }]
  } finally {
    isDeleting.value = false
    setTimeout(() => {
      showDeleteProgress.value = false
    }, 2000) // 显示2秒后关闭进度弹窗
  }
}

const cancelBatchDelete = () => {
  showBatchDeleteModal.value = false
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleString('zh-CN')
}

const formatFileSize = (bytes) => {
  if (!bytes) return '0 B'
  const units = ['B', 'KB', 'MB', 'GB']
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  return `${size.toFixed(1)} ${units[unitIndex]}`
}

const getFileIcon = (fileType) => {
  const icons = {
    'pdf': '📕',
    'docx': '📘',
    'md': '📝'
  }
  return icons[fileType.toLowerCase()] || '📄'
}

const downloadDocument = async (docId, fileName) => {
  try {
    // 创建一个新的XMLHttpRequest
    const xhr = new XMLHttpRequest();
    xhr.open('GET', `/ragflow/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents/${docId}`, true);
    xhr.responseType = 'blob';
    xhr.setRequestHeader('Authorization', 'Bearer ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG');

    xhr.onload = function() {
      if (this.status === 200) {
        // 创建一个blob链接
        const blob = new Blob([this.response]);
        const url = window.URL.createObjectURL(blob);
        
        // 创建一个隐藏的a标签用于下载
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = fileName || `document_${docId}`;
        document.body.appendChild(a);
        a.click();
        
        // 清理
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        throw new Error('文件下载失败');
      }
    };

    xhr.onerror = function() {
      console.error('下载文档失败');
    };

    xhr.send();
  } catch (error) {
    console.error('下载文档失败:', error);
  }
}

// 生命周期钩子
onMounted(() => {
  // 确保组件挂载时获取数据
  if (Date.now() - lastFetchTime.value > CACHE_DURATION) {
    fetchDocuments()
  }
})

onActivated(() => {
  // 当组件被 keep-alive 激活时，检查是否需要刷新数据
  if (Date.now() - lastFetchTime.value > CACHE_DURATION) {
    fetchDocuments()
  }
})

// 定义 emit 事件
defineEmits(['changeView'])

// 导出刷新方法供父组件调用
defineExpose({
  refreshDocuments
})
</script>

<style scoped>
.document-list-view {
  padding: 24px;
  max-width: 1600px;
  margin: 0 auto;
  width: 95%;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.list-header h3 {
  font-size: 1.4em;
  color: var(--primary-light);
  margin: 0;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.list-controls {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-area {
  display: flex;
  gap: 8px;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--bg-light);
  color: var(--text);
  min-width: 200px;
}

.refresh-btn {
  padding: 8px 16px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--bg-light);
  color: var(--text);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
}

.refresh-btn:hover:not(:disabled) {
  background: var(--bg-lighter);
  transform: translateY(-1px);
}

.refresh-icon {
  display: inline-block;
  transition: transform 0.5s ease;
}

.refresh-icon.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.document-table {
  border: 1px solid var(--border);
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
}

.document-table-header {
  display: grid;
  grid-template-columns: 40px 3fr 1fr 1fr 1fr 1fr 1.5fr 1.5fr 150px;
  padding: 12px;
  background: var(--bg-light);
  border-bottom: 1px solid var(--border);
  font-weight: 500;
}

.document-table-body {
  max-height: 600px;
  overflow-y: auto;
}

.document-table-row {
  display: grid;
  grid-template-columns: 40px 3fr 1fr 1fr 1fr 1fr 1.5fr 1.5fr 150px;
  padding: 12px;
  border-bottom: 1px solid var(--border);
  align-items: center;
  transition: background-color 0.2s ease;
}

.document-table-row:hover {
  background: var(--bg-light);
}

.document-table-row:last-child {
  border-bottom: none;
}

.col-checkbox {
  display: flex;
  justify-content: center;
  align-items: center;
}

.col-filename {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  font-size: 1.2em;
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.action-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.9em;
  transition: all 0.2s ease;
}

.action-btn.view {
  background: var(--primary);
  color: white;
}

.action-btn.download {
  background: var(--primary);
  color: white;
}

.action-btn.delete {
  background: var(--error);
  color: white;
}

.action-btn:hover {
  filter: brightness(1.1);
  transform: translateY(-1px);
}

.batch-delete-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  background: var(--error);
  color: white;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.2s ease;
}

.batch-delete-btn:hover:not(:disabled) {
  filter: brightness(1.1);
  transform: translateY(-1px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  color: var(--text-secondary);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border);
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: var(--text-secondary);
}

.error-message {
  text-align: center;
  padding: 20px;
  color: var(--error);
}

.error-message button {
  margin-top: 10px;
  padding: 8px 16px;
  border: 1px solid var(--error);
  border-radius: 4px;
  background: transparent;
  color: var(--error);
  cursor: pointer;
  transition: all 0.2s ease;
}

.error-message button:hover {
  background: var(--error);
  color: white;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 20px;
}

.page-selector, .page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-select, .page-size-select {
  padding: 6px 10px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--bg-light);
  color: var(--text);
  cursor: pointer;
  min-width: 60px;
  text-align: center;
}

.page-select:hover, .page-size-select:hover {
  background: var(--bg-lighter);
}

.prev-btn,
.next-btn {
  padding: 8px 16px;
  border: 1px solid var(--border);
  border-radius: 6px;
  background: var(--bg-light);
  color: var(--text);
  cursor: pointer;
  transition: all 0.2s ease;
}

.prev-btn:hover:not(:disabled),
.next-btn:hover:not(:disabled) {
  background: var(--bg-lighter);
  transform: translateY(-1px);
}

.page-info {
  color: var(--text-secondary);
}

/* 移除重复的模态框基础样式，改用全局样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: var(--bg);
  padding: 32px;
  border-radius: 16px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  border: 1px solid var(--border);
  box-shadow: var(--shadow-lg);
}

/* 删除确认弹窗样式 */
.delete-modal-content {
  max-width: 480px;
}

.delete-modal-content h3 {
  color: var(--error);
  margin: 0 0 16px 0;
  font-size: 1.5em;
}

.delete-modal-content p {
  color: var(--text);
  margin: 0 0 24px 0;
  line-height: 1.5;
}

/* 批量删除进度样式 */
.delete-progress {
  margin-top: 24px;
}

.delete-progress-modal .modal-content {
  background: var(--bg);
}

.delete-progress-modal .modal-content h3 {
  color: var(--text);
  margin: 0 0 16px 0;
  font-size: 1.5em;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 107, 107, 0.1);
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
}

.progress-bar-inner {
  height: 100%;
  background: var(--error);
  transition: width 0.3s ease;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.15);
}

.progress-text {
  text-align: center;
  color: var(--text);
  margin-bottom: 20px;
  font-size: 0.95em;
}

.delete-status {
  margin-top: 20px;
  padding: 16px;
  background: var(--bg-light);
  border-radius: 12px;
  border: 1px solid var(--border);
}

.delete-status p {
  margin: 0 0 12px 0;
  color: var(--text);
  font-size: 0.95em;
}

.delete-status ul {
  margin: 0;
  padding-left: 24px;
}

.delete-status li {
  color: var(--error);
  margin-bottom: 8px;
  font-size: 0.9em;
}

/* 按钮样式 */
.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
}

.modal-actions button {
  padding: 10px 24px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.95em;
  font-weight: 500;
}

.modal-actions .delete {
  background: var(--error);
  color: white;
  border: none;
}

.modal-actions .delete:hover {
  filter: brightness(1.1);
  transform: translateY(-1px);
}

.modal-actions .cancel {
  background: transparent;
  border: 1px solid var(--border);
  color: var(--text);
}

.modal-actions .cancel:hover {
  background: var(--bg-light);
  transform: translateY(-1px);
}

/* 文件预览样式 */
.selected-files-preview {
  margin: 20px 0;
  padding: 16px;
  background: var(--bg-light);
  border-radius: 12px;
  border: 1px solid var(--border);
}

.preview-header {
  font-size: 0.95em;
  color: var(--text-secondary);
  margin-bottom: 12px;
  font-weight: 500;
}

.preview-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.preview-item {
  padding: 10px 16px;
  background: var(--bg);
  border-radius: 8px;
  font-size: 0.9em;
  border: 1px solid var(--border);
  color: var(--text);
}

.preview-more {
  color: var(--text-secondary);
  font-size: 0.85em;
  padding: 8px 16px;
  background: var(--bg);
  border-radius: 8px;
  border: 1px solid var(--border);
  text-align: center;
}

/* 添加响应式样式 */
@media screen and (max-width: 768px) {
  .document-list-view {
    padding: 12px;
    width: 100%;
  }

  .list-header {
    flex-direction: column;
    gap: 12px;
  }

  .list-controls {
    flex-direction: column;
    width: 100%;
    gap: 12px;
  }

  .search-area {
    width: 100%;
    flex-direction: column;
    gap: 8px;
  }

  .search-input {
    width: 100%;
  }

  .document-table {
    overflow-x: auto;
  }

  .document-table-header,
  .document-table-row {
    min-width: 800px;
  }

  .col-actions {
    position: sticky;
    right: 0;
    background: var(--bg);
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .pagination {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media screen and (max-width: 480px) {
  .document-list-view {
    padding: 8px;
  }

  .document-table-header,
  .document-table-row {
    font-size: 0.9em;
  }

  .btn-text {
    display: none;
  }

  .action-btn {
    padding: 6px;
  }

  .modal-content {
    width: 90%;
    margin: 10% auto;
    max-height: 80vh;
  }
}

.progress-title {
  color: var(--text) !important;
}

/* 添加旋转动画 */
.btn-icon {
  display: inline-block;
}

.action-btn:disabled,
.delete:disabled,
.cancel:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-btn:disabled:hover,
.delete:disabled:hover {
  filter: none;
  transform: none;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 只有带有spinning类的图标才会旋转 */
.btn-icon.spinning {
  animation: spin 1s linear infinite;
}

/* 添加进度图标样式 */
.progress-icon {
  display: inline-block;
  margin-left: 8px;
  font-size: 1.1em;
}

.sortable {
  cursor: pointer;
  user-select: none;
  display: flex;
  align-items: center;
  gap: 4px;
}

.sortable:hover {
  color: var(--primary-light);
}

.sort-icon {
  font-size: 0.8em;
  color: var(--primary-light);
}
</style> 