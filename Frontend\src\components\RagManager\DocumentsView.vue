<template>
    <div class="documents" style="background: white; color: #333;">
      <!-- 合并的上传和进度区域 -->
      <div class="upload-section" style="background: white; border: 1px solid #e5e5e5; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);">
        <div class="section-header">
          <h3 style="color: #1890ff;">文档上传</h3>
          <div class="upload-info">
            <span class="info-item" style="background: #f5f5f5; color: #666;"><i class="fas fa-file-alt"></i> 支持：TXT, PDF, DOC, DOCX, MD</span>
            <span class="info-item" style="background: #f5f5f5; color: #666;"><i class="fas fa-weight-hanging"></i> 单文件大小：10MB</span>
            <span class="info-item" style="background: #f5f5f5; color: #666;"><i class="fas fa-layer-group"></i> 限制：128个文件</span>
          </div>
        </div>

        <!-- 上传区域 -->
        <div 
          class="upload-area"
          v-show="!uploading"
          :class="{ 'drag-over': isDragOver, 'has-files': selectedFile }"
          @dragover.prevent="handleDragOver"
          @dragleave.prevent="handleDragLeave"
          @drop.prevent="handleDrop"
          @click="triggerFileInput"
          style="border: 2px dashed #d9d9d9; background: #fafafa; color: #333;"
        >
          <input 
            type="file" 
            @change="handleFileSelect" 
            ref="fileInput"
            accept=".txt,.pdf,.doc,.docx,.md"
            multiple
            style="display: none;"
          >
          <div class="upload-placeholder" v-if="!selectedFile" style="color: #333;">
            <span class="upload-icon" style="color: #1890ff;">📄</span>
            <p class="upload-text" style="color: #333;">点击或拖拽文件至此区域即可上传</p>
            <p class="upload-hint" style="color: #666;">支持单次或批量上传，单个文件不超过10MB，最多128个文件</p>
            <p class="upload-warning" style="color: #f5222d;">严禁上传违禁文件</p>
          </div>
        </div>

        <!-- 文件列表和进度显示 -->
        <div class="file-list-container" v-if="selectedFile">
          <div class="file-progress-list">
            <div 
              v-for="(file, index) in selectedFile" 
              :key="file.name"
              class="file-progress-item"
              :class="{
                'current': index === processedFileCount,
                'completed': index < processedFileCount,
                'pending': index > processedFileCount,
                'failed': failedFiles.includes(file.name)
              }"
            >
              <div class="file-info">
                <span class="file-icon">{{ getFileIcon(file.name.split('.').pop()) }}</span>
                <span class="file-name">{{ file.name }}</span>
                <span class="file-size">({{ formatFileSize(file.size) }})</span>
              </div>
              
              <div class="file-status">
                <template v-if="uploading && index === processedFileCount">
                  <div class="progress-stages">
                    <div 
                      v-for="(stage, stageIndex) in stages" 
                      :key="stageIndex"
                      class="stage-indicator"
                      :class="{
                        'completed': isStageCompleted(index, stageIndex),
                        'current': isCurrentStage(index, stageIndex)
                      }"
                      :data-stage="stage.name"
                    >
                      {{ stage.name }}
                      <span v-if="isCurrentStage(index, stageIndex)" class="stage-progress">
                        {{ Math.round(getCurrentStageProgress(index)) }}%
                      </span>
                    </div>
                  </div>
                  <div class="progress-message" v-if="fileProcessingStates[index]?.message">
                    {{ fileProcessingStates[index].message }}
                  </div>
                  <div class="progress-bar">
                    <div 
                      class="progress-bar-inner" 
                      :style="{ width: `${getFileProgress(index)}%` }"
                    ></div>
                  </div>
                </template>
                <template v-else-if="index < processedFileCount">
                  <span class="status-text success">上传完成</span>
                </template>
                <template v-else-if="failedFiles.includes(file.name)">
                  <span class="status-text error">上传失败</span>
                </template>
                <template v-else>
                  <span class="status-text pending">等待上传</span>
                </template>
              </div>
            </div>
          </div>

          <!-- 总体进度信息 -->
          <div class="total-progress-info">
            <div class="progress-summary">
              <span class="total-count">总计: {{ selectedFile.length }}个文件</span>
              <span class="processed-count">已完成: {{ processedFileCount }}/{{ selectedFile.length }}</span>
              <span class="failed-count" v-if="failedFiles.length > 0">失败: {{ failedFiles.length }}</span>
            </div>
            <div class="total-progress-bar">
              <div 
                class="progress-bar-inner" 
                :style="{ width: `${totalProgress}%` }"
              ></div>
            </div>
            <div class="progress-size">
              {{ formatFileSize(processedSize) }}/{{ formatFileSize(totalSize) }}
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="upload-actions">
            <button 
              @click="uploadDocument" 
              :disabled="!selectedFile || uploading"
              class="upload-button"
              :class="{ 'uploading': uploading }"
            >
              <span class="button-icon">{{ uploading ? '📤' : '📥' }}</span>
              <span class="button-text">{{ uploading ? '上传中...' : '开始上传' }}</span>
            </button>
            <button 
              @click="clearFiles" 
              class="clear-button"
              :disabled="uploading"
            >
              清空选择
            </button>
          </div>
        </div>
      </div>

      <!-- 通知提示 -->
      <div v-if="notification.show" class="notification" :class="notification.type">
        <div class="notification-content">
          <span class="notification-icon">
            {{ notification.type === 'success' ? '✓' : notification.type === 'error' ? '✕' : 'ℹ' }}
          </span>
          <span class="notification-message">{{ notification.message }}</span>
        </div>
        <button class="notification-close" @click="closeNotification">×</button>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed, onUnmounted } from 'vue'
  import http from '@/utils/axios'
  
  const selectedFile = ref(null)
  const fileInput = ref(null)
  const uploading = ref(false)
  const failedFiles = ref([])
  const processedFileCount = ref(0)
  const totalSize = ref(0)
  const processedSize = ref(0)
  const fileProcessingStates = ref([])
  
  // 定义上传阶段
  const stages = [
    { name: '验证文件', threshold: 30 },
    { name: '上传文件', threshold: 100 }
  ]
  
  // WebSocket连接管理
  const webSockets = ref({})
  
  // 通知提示相关
  const notification = ref({
    show: false,
    message: '',
    type: 'info',
    timer: null
  })
  
  const showNotification = (message, type = 'info', duration = 3000) => {
    if (notification.value.timer) {
      clearTimeout(notification.value.timer)
    }
    
    let displayMessage = message
    if (type === 'error' && message.includes('上传文档时出错')) {
      const errorMatch = message.match(/上传文档时出错: (.+)/)
      displayMessage = errorMatch ? errorMatch[1] : message
    }
    
    notification.value = {
      show: true,
      message: displayMessage,
      type,
      timer: setTimeout(() => {
        notification.value.show = false
      }, duration)
    }
  }
  
  const closeNotification = () => {
    if (notification.value.timer) {
      clearTimeout(notification.value.timer)
    }
    notification.value.show = false
  }
  
  // 获取单个文件的上传进度
  const getFileProgress = (fileIndex) => {
    const state = fileProcessingStates.value[fileIndex]
    if (!state) return 0
    return state.progress || 0
  }
  
  // 获取当前阶段的进度
  const getCurrentStageProgress = (fileIndex) => {
    const state = fileProcessingStates.value[fileIndex]
    if (!state) return 0

    const currentStage = state.stage
    const currentProgress = state.progress

    // 找到当前阶段的阈值范围
    const stageStart = currentStage > 0 ? stages[currentStage - 1]?.threshold || 0 : 0
    const stageEnd = stages[currentStage]?.threshold || 100
    
    // 计算当前阶段内的进度百分比
    const stageProgress = ((currentProgress - stageStart) / (stageEnd - stageStart)) * 100
    return Math.min(100, Math.max(0, Math.round(stageProgress)))
  }
  
  // 判断阶段是否完成
  const isStageCompleted = (fileIndex, stageIndex) => {
    const state = fileProcessingStates.value[fileIndex]
    if (!state) return false
    return state.progress >= stages[stageIndex].threshold
  }
  
  // 判断是否为当前阶段
  const isCurrentStage = (fileIndex, stageIndex) => {
    const state = fileProcessingStates.value[fileIndex]
    if (!state) return false
    return state.stage === stageIndex
  }
  
  // 计算总体进度
  const totalProgress = computed(() => {
    if (!selectedFile.value || selectedFile.value.length === 0) return 0
    const totalProgress = fileProcessingStates.value.reduce((acc, state) => acc + (state?.progress || 0), 0)
    return Math.round(totalProgress / selectedFile.value.length)
  })
  
  // 文件图标映射
  const getFileIcon = (extension) => {
    const iconMap = {
      'pdf': '📄',
      'doc': '📝',
      'docx': '📝',
      'txt': '📄',
      'md': '📋'
    }
    return iconMap[extension.toLowerCase()] || '📄'
  }
  
  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`
  }
  
  // 拖拽相关状态
  const isDragOver = ref(false)
  
  const handleDragOver = (event) => {
    event.preventDefault()
    isDragOver.value = true
  }
  
  const handleDragLeave = (event) => {
    event.preventDefault()
    isDragOver.value = false
  }
  
  const handleDrop = (event) => {
    event.preventDefault()
    isDragOver.value = false
    handleFileSelect(event)
  }
  
  const triggerFileInput = () => {
    if (fileInput.value && !uploading.value) {
      fileInput.value.click()
    }
  }
  
  // 清空文件选择
  const clearFiles = () => {
    if (uploading.value) return
    selectedFile.value = null
    processedFileCount.value = 0
    totalSize.value = 0
    processedSize.value = 0
    failedFiles.value = []
    fileProcessingStates.value = []
    
    // 清空所有WebSocket连接
    Object.values(webSockets.value).forEach(ws => ws.close())
    webSockets.value = {}
  }
  
  // 文件选择上传
  const handleFileSelect = (event) => {
    const files = event.target.files || event.dataTransfer?.files
    if (!files || files.length === 0) return

    // 检查文件数量限制
    if (files.length > 128) {
      showNotification('超出文件数量限制：最多支持128个文件', 'error')
      return
    }

    // 检查文件大小
    const oversizedFiles = Array.from(files).filter(file => file.size > 10 * 1024 * 1024)
    if (oversizedFiles.length > 0) {
      showNotification(`以下文件超出大小限制(10MB)：${oversizedFiles.map(f => f.name).join(', ')}`, 'error')
      return
    }

    // 重置所有状态
    selectedFile.value = Array.from(files)
    processedFileCount.value = 0
    failedFiles.value = []
    fileProcessingStates.value = Array(files.length).fill().map(() => ({
      progress: 0,
      stage: 0,
      message: '等待上传...',
      completed: false
    }))
    
    // 计算总文件大小
    totalSize.value = Array.from(files).reduce((acc, file) => acc + file.size, 0)
    processedSize.value = 0
    
    // 清空文件输入框，允许重新选择相同文件
    if (fileInput.value) {
      fileInput.value.value = ''
    }
  }
  
  // 修改上传文档函数
  const uploadDocument = async () => {
    if (!selectedFile.value?.length || uploading.value) return
  
    uploading.value = true
    processedFileCount.value = 0
    processedSize.value = 0
    failedFiles.value = []
  
    try {
      for (let i = 0; i < selectedFile.value.length; i++) {
        const file = selectedFile.value[i]
        const formData = new FormData()
        formData.append('file', file)
  
        try {
          // 更新状态为开始验证
          fileProcessingStates.value[i] = {
            ...fileProcessingStates.value[i],
            progress: 10,
            stage: 0,
            message: '正在验证文件...'
          }
          fileProcessingStates.value = [...fileProcessingStates.value]
  
          // 验证文件大小
          if (file.size > 10 * 1024 * 1024) { // 10MB
            throw new Error('文件大小超过10MB限制')
          }
  
          // 验证文件类型
          const fileExt = file.name.split('.').pop()?.toLowerCase()
          if (!['txt', 'pdf', 'doc', 'docx', 'md'].includes(fileExt)) {
            throw new Error('不支持的文件类型')
          }
  
          // 更新验证完成状态
          fileProcessingStates.value[i] = {
            ...fileProcessingStates.value[i],
            progress: 30,
            stage: 0,
            message: '文件验证完成，准备上传...'
          }
          fileProcessingStates.value = [...fileProcessingStates.value]
  
          // 发送上传请求
          console.log('正在上传文件:', file.name)
          fileProcessingStates.value[i] = {
            ...fileProcessingStates.value[i],
            progress: 50,
            stage: 1,
            message: '正在上传文件...'
          }
          fileProcessingStates.value = [...fileProcessingStates.value]
  
          const response = await http.post('/ragflow/api/v1/datasets/4afb1382475c11f0ac3e345a603cb29c/documents', formData, {
            headers: {
              'Content-Type': 'multipart/form-data',
              'Authorization': 'Bearer ragflow-JmYzAxZmYyNDdmMzExZjA5OTUxODhmNG'
            }
          })
  
          console.log('上传响应:', response)
          
          // 检查响应格式和状态码
          if (response?.code === 0 && Array.isArray(response?.data) && response.data.length > 0) {
            const uploadedDoc = response.data[0];
            
            // 验证必要的字段
            if (!uploadedDoc.id || !uploadedDoc.name || !uploadedDoc.run) {
              throw new Error('服务器响应数据格式不完整')
            }

            // 更新上传完成状态
            fileProcessingStates.value[i] = {
              ...fileProcessingStates.value[i],
              progress: 100,
              stage: stages.length - 1,
              message: `文件上传完成，文档ID: ${uploadedDoc.id}，状态: ${uploadedDoc.run}`,
              completed: true,
              documentId: uploadedDoc.id,
              status: uploadedDoc.run,
              size: uploadedDoc.size || file.size,
              type: uploadedDoc.type || 'doc'
            }
            fileProcessingStates.value = [...fileProcessingStates.value]
            
            processedFileCount.value++
            processedSize.value += (uploadedDoc.size || file.size)
            
            // 根据文档状态显示不同的提示信息
            let statusMessage = '上传成功'
            if (uploadedDoc.run === 'UNSTART') {
              statusMessage += '，等待上传'
            } else if (uploadedDoc.run === 'PROCESSING') {
              statusMessage += '，正在上传'
            } else if (uploadedDoc.run === 'FINISHED') {
              statusMessage += '，上传完成'
            } else if (uploadedDoc.run === 'FAILED') {
              statusMessage += '，上传失败'
            }
            
            showNotification(`文件 ${uploadedDoc.name} ${statusMessage}`, 'success')
          } else {
            // 上传错误响应
            const errorMessage = response?.message || '上传响应格式错误'
            console.error('上传失败:', errorMessage)
            throw new Error(errorMessage)
          }
        } catch (error) {
          console.error(`上传文件 ${file.name} 失败:`, error)
          console.error('错误详情:', error.response || error)
          failedFiles.value.push(file.name)
          fileProcessingStates.value[i] = {
            progress: 0,
            stage: -1,
            message: `上传失败: ${error.response?.data?.message || error.message}`,
            failed: true
          }
          showNotification(`文件 ${file.name} 上传失败: ${error.response?.data?.message || error.message}`, 'error')
        }
      }
    } finally {
      uploading.value = false
      
      if (processedFileCount.value > 0) {
        showNotification('文件上传完成', 'success')
      }
    }
  }
  
  // 组件卸载时清理WebSocket连接
  onUnmounted(() => {
    Object.values(webSockets.value).forEach(ws => ws.close())
  })
  </script>
  
  <style scoped>
  /* 基础变量 */
  :root {
    --primary: #1890ff;
    --primary-light: #40a9ff;
    --primary-dark: #096dd9;
    --error: #f5222d;
    --success: #52c41a;
    --warning: #faad14;
    --text: #333333;
    --text-secondary: #666666;
    --border: rgba(0, 0, 0, 0.1);
    --border-strong: rgba(0, 0, 0, 0.15);
    --bg: #ffffff;
    --bg-light: #f5f5f5;
    --bg-lighter: #fafafa;
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  .documents {
    padding: 24px;
    color: var(--text);
    max-width: 1600px;
    margin: 0 auto;
    width: 95%;
  }
  
  /* 添加媒体查询，适配小屏幕 */
  @media screen and (max-width: 768px) {
    .documents {
      padding: 12px;
      width: 100%;
    }

    .upload-section {
      padding: 16px;
    }

    .section-header {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;
    }

    .upload-info {
      width: 100%;
      justify-content: flex-start;
    }

    .info-item {
      padding: 4px 12px;
      font-size: 0.9em;
    }

    .file-progress-list {
      max-height: none;
    }

    .progress-stages {
      gap: 4px;
    }

    .stage-indicator {
      font-size: 0.8em;
      padding: 2px 6px;
    }

    .upload-actions {
      flex-direction: column;
    }

    .upload-button,
    .clear-button {
      width: 100%;
      max-width: none;
    }

    .notification {
      width: 90%;
      min-width: auto;
      max-width: none;
      margin: 0 auto;
    }
  }

  /* 添加更小屏幕的适配 */
  @media screen and (max-width: 480px) {
    .documents {
      padding: 8px;
    }

    .upload-section {
      padding: 12px;
    }

    .file-info {
      flex-wrap: wrap;
    }

    .file-name {
      width: 100%;
      margin-top: 4px;
    }

    .progress-stages {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
      gap: 4px;
    }

    .stage-indicator {
      text-align: center;
      justify-content: center;
    }

    .total-progress-info {
      padding: 12px;
    }

    .progress-summary {
      flex-direction: column;
      gap: 8px;
    }
  }
  
  /* 合并的上传和进度区域 */
  .upload-section {
    background: var(--bg);
    border-radius: 16px;
    padding: 24px;
    border: 1px solid var(--border);
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
  }
  
  .section-header {
    margin-bottom: 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .section-header h3 {
    font-size: 1.4em;
    color: var(--primary-light);
    margin: 0;
    font-weight: 600;
    letter-spacing: 0.5px;
  }
  
  .upload-info {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
  
  .info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 16px;
    background: var(--bg-light);
    border-radius: 20px;
    font-size: 1em;
    color: var(--text-secondary);
    transition: all 0.2s ease;
  }
  
  .info-item:hover {
    background: var(--bg-lighter);
    transform: translateY(-1px);
  }
  
  /* 上传区域样式 */
  .upload-area {
    border: 2px dashed var(--border);
    border-radius: 12px;
    padding: 16px;
    text-align: center;
    background: var(--bg-light);
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    max-width: 100%;
  }
  
  .upload-area.drag-over {
    border-color: var(--primary-light);
    background: rgba(59, 130, 246, 0.1);
    transform: scale(1.01);
  }
  
  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    width: 100%;
  }
  
  .upload-icon {
    font-size: 32px;
    color: var(--primary-light);
    opacity: 0.8;
    transition: all 0.3s ease;
  }
  
  .upload-area:hover .upload-icon {
    transform: translateY(-4px);
    opacity: 1;
  }
  
  .upload-text {
    font-size: 1em;
    margin: 0;
  }
  
  .upload-hint {
    font-size: 0.85em;
    margin: 0;
    color: var(--text-secondary);
  }
  
  /* 文件列表和进度显示样式 */
  .file-list-container {
    margin-top: 24px;
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .file-progress-list {
    background: var(--bg-light);
    border-radius: 12px;
    border: 1px solid var(--border);
    overflow: hidden;
    max-height: 320px;
    overflow-y: auto;
  }
  
  .file-progress-item {
    padding: 12px 16px;
    border-bottom: 1px solid var(--border);
  }
  
  .file-progress-item:last-child {
    border-bottom: none;
  }
  
  .file-progress-item.current {
    background: var(--bg-lighter);
  }
  
  .file-progress-item.completed {
    background: rgba(16, 185, 129, 0.05);
  }
  
  .file-progress-item.failed {
    background: rgba(239, 68, 68, 0.05);
  }
  
  .file-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
  }
  
  .file-icon {
    font-size: 1.1em;
  }
  
  .file-name {
    font-size: 1em;
    flex: 1;
  }
  
  .file-size {
    font-size: 0.9em;
    color: var(--text-secondary);
  }
  
  .file-status {
    margin-top: 8px;
  }
  
  .progress-stages {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    flex-wrap: wrap;
  }
  
  .stage-indicator {
    font-size: 0.85em;
    color: var(--text-secondary);
    padding: 2px 8px;
    border-radius: 12px;
    background: var(--bg);
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    opacity: 0.5; /* 默认状态显示为半透明 */
  }
  
  .stage-indicator.completed {
    color: var(--success);
    background: rgba(16, 185, 129, 0.1);
    opacity: 1;
  }
  
  .stage-indicator.current {
    color: var(--primary);
    background: rgba(59, 130, 246, 0.1);
    font-weight: 500;
    position: relative;
    overflow: hidden;
    opacity: 1;
  }
  
  /* 移除之前的静态颜色样式 */
  /* 根据上传阶段动态设置颜色 */
  .stage-indicator.processing {
    opacity: 1;
    color: var(--primary);
    background: rgba(59, 130, 246, 0.1);
  }
  
  .stage-indicator.current::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    animation: shimmer 1.5s infinite;
  }
  
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
  
  .stage-progress {
    font-size: 0.85em;
    opacity: 0.9;
  }
  
  .progress-bar {
    height: 4px;
    background: var(--bg);
    border-radius: 2px;
    overflow: hidden;
    margin: 8px 0;
  }
  
  .progress-bar-inner {
    height: 100%;
    background: var(--primary);
    transition: width 0.3s ease;
  }
  
  .progress-message {
    font-size: 0.9em;
    color: var(--text-secondary);
    margin: 8px 0;
    padding: 4px 12px;
    background: var(--bg);
    border-radius: 4px;
  }
  
  .status-text {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 0.9em;
  }
  
  .status-text.success {
    color: var(--success);
    background: rgba(16, 185, 129, 0.1);
  }
  
  .status-text.error {
    color: var(--error);
    background: rgba(239, 68, 68, 0.1);
  }
  
  .status-text.pending {
    color: var(--text-secondary);
    background: var(--bg);
  }
  
  /* 总体进度信息 */
  .total-progress-info {
    margin-top: 0;
    padding: 16px 20px;
    background: var(--bg-light);
    border-radius: 12px;
    border: 1px solid var(--border);
    position: sticky;
    bottom: 0;
    z-index: 1;
  }
  
  .progress-summary {
    display: flex;
    gap: 16px;
    margin-bottom: 8px;
    flex-wrap: wrap;
    padding: 4px 0;
  }
  
  .total-count,
  .processed-count,
  .failed-count {
    font-size: 0.95em;
    line-height: 1.5;
  }
  
  .total-progress-bar {
    height: 6px;
    background: var(--bg);
    border-radius: 3px;
    overflow: hidden;
    margin: 12px 0;
  }
  
  .progress-size {
    text-align: right;
    font-size: 0.9em;
    color: var(--text-secondary);
  }
  
  /* 操作按钮 */
  .upload-actions {
    display: flex;
    gap: 12px;
    margin-top: 0;
    position: sticky;
    bottom: 0;
    z-index: 2;
    background: var(--bg);
    padding: 12px 0;
    border-top: 1px solid var(--border);
  }
  
  .upload-button,
  .clear-button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 1em;
    min-width: 120px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
  }

  .button-icon {
    font-size: 1.2em;
  }

  .upload-button {
    background: var(--primary);
    color: white;
    border: none;
    flex: 1;
    max-width: 300px;
  }

  .clear-button {
    background: transparent;
    border: 1px solid var(--border);
    color: var(--text);
    min-width: 120px;
  }
  
  button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
  }
  
  /* 通知提示样式 */
  .notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 16px 24px;
    border-radius: 12px;
    background: var(--bg-light);
    color: var(--text);
    display: flex;
    align-items: center;
    gap: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    z-index: 3002;
    min-width: 300px;
    max-width: 500px;
    animation: fadeInDown 0.3s ease;
  }
  
  /* 添加新的动画 */
  @keyframes fadeInDown {
    from {
      opacity: 0;
      transform: translate(-50%, -60%);
    }
    to {
      opacity: 1;
      transform: translate(-50%, -50%);
    }
  }
  
  .notification.success {
    background: var(--success);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
  }
  
  .notification.error {
    background: var(--error);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
  }
  
  .notification.warning {
    background: var(--warning);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
  }
  
  .notification-content {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
  }
  
  .notification-icon {
    font-size: 1.2em;
  }
  
  .notification-close {
    background: none;
    border: none;
    color: var(--text);
    cursor: pointer;
    font-size: 1.2em;
    padding: 4px;
    opacity: 0.8;
    transition: all 0.2s ease;
  }
  
  .notification-close:hover {
    opacity: 1;
    transform: scale(1.1);
  }
  
  @keyframes slideIn {
    from {
      opacity: 0;
      margin-top: -20px;
    }
    to {
      opacity: 1;
      margin-top: 0;
    }
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  </style> 