/**
 * MQTT数据服务
 * 
 * 使用API调用后端的MQTT数据服务，取代直接连接MQTT服务器
 */

import mqtt from 'mqtt';
import { useDeviceStore } from '../stores/deviceStore'
import { useWebsocketStore } from '../stores/websocketStore'

// 存储当前订阅的主题和设备ID
let currentTopic = null;
let currentDeviceId = null;
let pollInterval = null;
let pollingActive = false;
let store = null;
let isInitialized = false;

class MQTTClient {
    constructor() {
        this.client = null;
        this.activeTopics = new Set();
        this.store = null;
    }

    connect() {
        // 确保store已初始化
        if (!this.store) {
            this.store = useDeviceStore();
        }

        // 如果已经有连接，先断开
        if (this.client) {
            try {
                this.client.end(true);
                this.client = null;
            } catch (err) {
                console.warn('断开旧连接时出错:', err);
            }
        }

        // MQTT服务器连接配置
        const options = {
            username: '111',
            protocol: 'ws',
            port: 8083,
            path: '/mqtt',           // 显式指定WebSocket路径
            hostname: '************', // 显式指定主机名
            // WebSocket特定选项
            wsOptions: {
                rejectUnauthorized: false,
                headers: {
                    'User-Agent': 'Mozilla/5.0',
                },
                protocolVersion: 13,
                perMessageDeflate: true
            },
            // 重连配置
            reconnectPeriod: 5000,    // 重连间隔5秒
            connectTimeout: 30000,     // 连接超时30秒
            clean: true,              // 清除会话
            keepalive: 30,           // 降低心跳间隔到30秒
            clientId: `mqtt_client_${Math.random().toString(16).slice(2, 10)}`, // 随机客户端ID
            will: {                  // 遗嘱消息
                topic: 'client/status',
                payload: JSON.stringify({ status: 'offline' }),
                qos: 1,
                retain: false
            }
        };

        // 连接MQTT服务器
        try {
            console.log('正在连接MQTT服务器...', options);
            
            // 使用完整的WebSocket URL
            const wsUrl = `ws://${options.hostname}:${options.port}${options.path}`;
            this.client = mqtt.connect(wsUrl, options);

            // 连接成功回调
            this.client.on('connect', () => {
                console.log('MQTT连接成功');
                this.store.setConnectionStatus('connected');
                
                // 发送上线状态
                this.client.publish('client/status', JSON.stringify({ 
                    status: 'online',
                    clientId: options.clientId,
                    timestamp: new Date().toISOString()
                }), { qos: 1 });
                
                // 重新订阅之前的topics
                this.activeTopics.forEach(topic => {
                    this.subscribe(topic);
                });
            });

            // 接收消息回调
            this.client.on('message', (topic, message) => {
                try {
                    const data = JSON.parse(message.toString());
                    console.log(`来自 ${topic} 的数据:`, data);
                    
                    // 更新设备数据
                    this.store.updateDeviceData(data);
                    
                    // 如果是摄像头移动消息，发送到WebSocket
                    if (data.type === 'camera' && data.action === 'moveTo') {
                        const wsStore = useWebsocketStore();
                        if (wsStore && wsStore.isConnected) {
                            wsStore.sendMessage({
                                type: 'camera_movement',
                                action: data.action,
                                position: data.position,
                                timestamp: new Date().toISOString()
                            });
                        }
                    }
                } catch (error) {
                    console.error('数据解析错误:', error);
                    console.log('原始消息:', message.toString());
                }
            });

            // 错误处理
            this.client.on('error', (error) => {
                console.error('MQTT错误:', error);
                this.store.setConnectionStatus('error');
                
                // 记录错误详情
                console.log('错误详情:', {
                    message: error.message,
                    stack: error.stack,
                    timestamp: new Date().toISOString()
                });

                // 如果是WebSocket特定错误，尝试重新初始化连接
                if (error.message?.includes('WebSocket')) {
                    console.log('检测到WebSocket错误，将在5秒后尝试重新连接...');
                    setTimeout(() => {
                        this.connect();
                    }, 5000);
                }
            });

            // 断开连接处理
            this.client.on('close', () => {
                console.log('MQTT连接已断开');
                this.store.setConnectionStatus('disconnected');
            });

            // 重连事件处理
            this.client.on('reconnect', () => {
                console.log('MQTT正在尝试重新连接...');
                this.store.setConnectionStatus('reconnecting');
            });

            // 离线事件处理
            this.client.on('offline', () => {
                console.log('MQTT客户端离线');
                this.store.setConnectionStatus('offline');
            });

            // 包发送完成事件
            this.client.on('packetsend', (packet) => {
                if (packet.cmd === 'publish') {
                    console.log(`发送消息到主题: ${packet.topic}`);
                }
            });

            // 包接收完成事件
            this.client.on('packetreceive', (packet) => {
                if (packet.cmd === 'publish') {
                    console.log(`接收到主题 ${packet.topic} 的消息`);
                }
            });

        } catch (error) {
            console.error('MQTT连接创建失败:', error);
            this.store.setConnectionStatus('error');
            
            // 记录错误详情
            console.log('连接创建错误详情:', {
                message: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString()
            });

            // 设置延迟重试
            setTimeout(() => {
                console.log('尝试重新建立连接...');
                this.connect();
            }, 5000);
        }
    }

    subscribe(topic) {
        if (!this.client) return;

        this.client.subscribe(topic, (err) => {
            if (!err) {
                console.log(`成功订阅topic: ${topic}`);
                this.activeTopics.add(topic);
            } else {
                console.error(`订阅topic失败: ${topic}`, err);
            }
        });
    }

    unsubscribe(topic) {
        if (!this.client) return;

        this.client.unsubscribe(topic, (err) => {
            if (!err) {
                console.log(`取消订阅topic: ${topic}`);
                this.activeTopics.delete(topic);
            }
        });
    }

    unsubscribeAll() {
        if (!this.client) return;
        
        this.activeTopics.forEach(topic => {
            this.unsubscribe(topic);
        });
        this.activeTopics.clear();
    }

    disconnect() {
        if (this.client) {
            this.unsubscribeAll();
            this.client.end();
            this.client = null;
            this.store.setConnectionStatus('disconnected');
            console.log('MQTT连接已断开');
        }
    }
}

// 创建单例实例
const mqttClient = new MQTTClient();

/**
 * 获取设备实时数据
 * 
 * @param {Array} deviceIds - 设备ID列表，如['JH005', 'H008'] 
 * @param {Array} topics - 可选的MQTT主题列表
 * @returns {Promise} 包含设备数据的Promise
 */
export const fetchDeviceData = async (deviceIds, topics = null) => {
  try {
    // 准备请求参数
    const params = {
      device_ids: deviceIds
    }
    
    if (topics && topics.length > 0) {
      params.topics = topics
    }
    
    // 调用API获取设备数据
    const response = await http.post('/api/device/data', params)
    
    // 检查响应状态
    if (response && response.data && response.data.success) {
      return {
        success: true,
        devices: response.data.devices,
        timestamp: new Date().toISOString()
      }
    } else {
      // 记录错误信息
      console.error('获取设备数据失败:', 
        response ? (response.data ? response.data.error : '响应数据为空') : '响应为空')
      
      // 创建模拟数据
      const mockData = createMockDeviceData(deviceIds[0]);
      
      return {
        success: true,
        devices: [mockData],
        timestamp: new Date().toISOString(),
        isMock: true
      }
    }
  } catch (error) {
    console.error('获取设备数据请求出错:', error)
    
    // 创建模拟数据
    const mockData = createMockDeviceData(deviceIds[0]);
    
    return {
      success: true,
      devices: [mockData],
      timestamp: new Date().toISOString(),
      isMock: true
    }
  }
}

/**
 * 创建模拟设备数据
 * 当API请求失败时使用
 * 
 * @param {string} deviceId - 设备ID
 * @returns {Object} 模拟的设备数据
 */
const createMockDeviceData = (deviceId) => {
  // 生成随机值的辅助函数
  const randomValue = (min, max) => Math.floor(Math.random() * (max - min + 1)) + min;
  const randomFloat = (min, max) => parseFloat((Math.random() * (max - min) + min).toFixed(2));
  
  // 从设备ID中提取井名
  const wellname = deviceId || 'JH000';
  
  // 创建模拟位移和载荷数据点
  const displacementPoints = [];
  const loadPoints = [];
  const currentPoints = [];
  
  // 生成100个数据点
  for (let i = 0; i < 100; i++) {
    const position = i / 50; // 位移范围0-2
    displacementPoints.push(position);
    
    // 载荷呈半椭圆形
    const loadBase = 30;
    const loadAmplitude = 25; 
    const loadPhase = Math.PI * (i / 100);
    loadPoints.push(loadBase + loadAmplitude * Math.sin(loadPhase));
    
    // 电流呈正弦形
    const currentBase = 20;
    const currentAmplitude = 10;
    const currentPhase = Math.PI * (i / 100);
    currentPoints.push(currentBase + currentAmplitude * Math.sin(currentPhase));
  }
  
  // 连接成字符串，使用逗号分隔x和y值
  const displacement = displacementPoints.map((x, i) => `${x.toFixed(3)},${loadPoints[i].toFixed(3)}`).join('|');
  const disp_load = loadPoints.map((y, i) => `${displacementPoints[i].toFixed(3)},${y.toFixed(3)}`).join('|');
  const disp_current = currentPoints.map((y, i) => `${displacementPoints[i].toFixed(3)},${y.toFixed(3)}`).join('|');
  
  // 创建模拟传感器值
  const values = [
    { id: 'WIP', Value: randomFloat(2, 8) },     // 井口压力
    { id: 'CPV', Value: randomFloat(1, 5) },     // 套压
    { id: 'TWT', Value: randomFloat(35, 65) },   // 井口温度
    { id: 'SLV', Value: randomFloat(1.5, 3.5) }, // 冲程
    { id: 'CHC', Value: randomFloat(3, 8) },     // 冲次
    { id: 'ZYG', Value: randomFloat(10, 35) },   // 总有功功率
    { id: 'ZWG', Value: randomFloat(5, 15) },    // 总无功功率
    { id: 'GYS', Value: randomFloat(0.7, 0.95) },// 功率因数
    { id: 'YGL', Value: randomFloat(30, 90) },   // 总功耗
    { id: 'ADY', Value: randomFloat(370, 400) }, // A相电压
    { id: 'ADL', Value: randomFloat(20, 40) },   // A相电流
    { id: 'BDY', Value: randomFloat(370, 400) }, // B相电压
    { id: 'BDL', Value: randomFloat(20, 40) },   // B相电流
    { id: 'CDY', Value: randomFloat(370, 400) }, // C相电压
    { id: 'CDL', Value: randomFloat(20, 40) }    // C相电流
  ];
  
  // 返回完整的模拟设备数据
  return {
    Wellname: wellname,
    Status: Math.random() > 0.8 ? 1 : 0, // 80%正常，20%异常
    Check_date: new Date().toISOString().replace('T', ' ').split('.')[0],
    Displacement: displacement,
    Disp_load: disp_load,
    Disp_current: disp_current,
    Values: values
  };
}

/**
 * 获取指定设备的实时数据
 * 
 * @param {string} deviceId - 单个设备ID
 * @returns {Promise} 包含设备数据的Promise
 */
export const getDeviceStatus = async (deviceId) => {
  return fetchDeviceData([deviceId])
}

/**
 * 解析设备ID
 * 如果是H00X格式，转换为JH00X格式
 * 
 * @param {string} deviceId - 原始设备ID
 * @returns {string} 格式化后的设备ID
 */
export const formatDeviceId = (deviceId) => {
  if (!deviceId) return deviceId
  
  // 如果以H开头但不是HN开头，转换为JH格式
  if (deviceId.match(/^H\d+$/) && !deviceId.startsWith('HN')) {
    return 'J' + deviceId
  }
  
  return deviceId
}

/**
 * 初始化设备状态存储
 * 确保只初始化一次
 */
const initStore = () => {
  if (!store) {
    try {
      store = useDeviceStore();
      if (store) {
        store.setConnectionStatus('connected');
      }
    } catch (error) {
      console.error('无法初始化设备存储:', error);
    }
  }
  return store;
}

/**
 * 开始轮询指定设备的数据
 * 
 * @param {string} deviceId - 设备ID
 */
const startPolling = (deviceId) => {
  if (pollingActive) {
    stopPolling();
  }
  
  initStore();
  
  currentDeviceId = deviceId;
  pollingActive = true;
  
  // 立即获取一次数据
  pollDeviceData();
  
  // 设置轮询间隔（例如每5秒）
  pollInterval = setInterval(pollDeviceData, 5000);
}

/**
 * 停止轮询
 */
const stopPolling = () => {
  if (pollInterval) {
    clearInterval(pollInterval);
    pollInterval = null;
  }
  pollingActive = false;
  currentTopic = null;
  currentDeviceId = null;
}

/**
 * 轮询设备数据
 */
const pollDeviceData = async () => {
  if (!currentDeviceId || !pollingActive) return;
  
  try {
    const result = await getDeviceStatus(currentDeviceId);
    if (result.success && result.devices && result.devices.length > 0) {
      // 将数据传递给store处理
      const deviceStore = initStore();
      if (deviceStore) {
        deviceStore.updateDeviceData(result.devices[0]);
      }
    }
  } catch (error) {
    console.error('轮询设备数据失败:', error);
    const deviceStore = initStore();
    if (deviceStore) {
      deviceStore.setConnectionStatus('error');
    }
  }
}

/**
 * 从主题中提取设备ID
 * 
 * @param {string} topic - MQTT主题，格式如 /HN3S1/JH001
 * @returns {string} 设备ID
 */
const extractDeviceIdFromTopic = (topic) => {
  if (!topic) return null;
  
  // 尝试提取最后一段作为设备ID（例如从/HN3S1/JH001提取JH001）
  const parts = topic.split('/');
  return parts[parts.length - 1];
}

/**
 * 订阅MQTT主题
 * 在API模式下，开始轮询相应设备的数据
 * 
 * @param {string} topic - 要订阅的主题
 * @returns {boolean} 订阅是否成功
 */
export const subscribe = (topic) => {
  console.log('订阅MQTT主题:', topic);
  // 确保初始化
  if (!isInitialized) {
    initialize();
  }
  
  currentTopic = topic;
  
  // 从主题中提取设备ID
  const deviceId = extractDeviceIdFromTopic(topic);
  if (deviceId) {
    startPolling(deviceId);
    return true;
  }
  
  return false;
}

/**
 * 取消所有MQTT订阅
 * 在API模式下，停止所有轮询
 * 
 * @returns {void}
 */
export const unsubscribeAll = () => {
  console.log('取消所有MQTT订阅');
  stopPolling();
  return true;
}

/**
 * 连接MQTT服务
 * 在API模式下，只是设置状态为已连接
 */
export const connect = () => {
  const deviceStore = initStore();
  if (deviceStore) {
    console.log('MQTT连接已建立（API模式）');
  } else {
    console.log('MQTT连接延迟初始化');
  }
  return true;
}

/**
 * 断开MQTT连接
 * 在API模式下，停止所有轮询并设置状态为已断开
 */
export const disconnect = () => {
  stopPolling();
  if (store) {
    store.setConnectionStatus('disconnected');
  }
  console.log('MQTT连接已断开（API模式）');
  return true;
}

/**
 * 初始化MQTT客户端
 * 延迟到实际使用时再初始化，避免循环依赖问题
 */
export const initialize = () => {
  if (!isInitialized) {
    connect();
    isInitialized = true;
    console.log('MQTT客户端已初始化');
  }
  return true;
}

// 导出对象
export default mqttClient;