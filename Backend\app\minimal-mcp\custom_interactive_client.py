import asyncio
import os
import logging
import subprocess
import sys
import importlib.util
from pathlib import Path
import time

# 导入必要的依赖
from langchain_openai import ChatOpenAI
from langgraph.prebuilt import create_react_agent
from langchain_mcp_adapters.client import MultiServerMCPClient

# 设置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 检查必要的依赖是否已安装
def check_and_install_dependencies():
    """检查并安装必要的依赖"""
    # 直接通过pip子进程检查包是否已安装
    def check_pip_package(package_name):
        try:
            # 运行pip show命令检查包是否已安装
            result = subprocess.run(
                [sys.executable, "-m", "pip", "show", package_name],
                capture_output=True,
                text=True,
                check=False
            )
            if result.returncode == 0:
                logger.debug(f"依赖包 {package_name} 已安装")
                return True
            else:
                logger.warning(f"缺少依赖包: {package_name}")
                return False
        except Exception as e:
            logger.error(f"检查依赖包 {package_name} 时出错: {str(e)}")
            return False
    
    # 检查redmine相关依赖
    redmine_ready = check_pip_package("python-redmine")
    yaml_ready = check_pip_package("pyyaml") 
    fastmcp_ready = check_pip_package("fastmcp")
    
    all_ready = redmine_ready and yaml_ready and fastmcp_ready
    
    # 检查是否需要安装依赖
    if not all_ready:
        logger.warning("缺少必要的依赖，redmine功能将被禁用")
        logger.info("您可以运行 python install_redmine_deps.py 安装必要的依赖")
        return False
    
    # 检查配置文件
    config_dir = Path("config")
    config_file = config_dir / "config.yaml"
    
    if not config_file.exists():
        logger.warning(f"缺少配置文件: {config_file}")
        logger.info("您可以运行 python install_redmine_deps.py 创建必要的配置文件")
        return False
    
    return True

async def main():
    """
    交互式客户端主函数：直接连接到LM Studio的本地大语言模型，
    使用MCP服务器提供的数学和天气工具
    """
    print("\n==== MCP交互式客户端 ====")
    print("连接到LM Studio模型: http://127.0.0.1:1234")
    print("模型: qwen2.5-7b-instruct-1m")
    print("启动MCP服务器中...\n")
    
    # 检查redmine依赖
    redmine_ready = check_and_install_dependencies()
    
    # 准备服务器配置
    servers = {
        "math": {
            "command": "python",
            "args": ["example_math_server.py"],
            "transport": "stdio",
        },
        "weather": {
            "command": "python",
            "args": ["example_weather_server.py"],
            "transport": "stdio",
        }
    }
    
    # 如果redmine依赖准备就绪，添加redmine服务器
    if redmine_ready:
        logger.info("Redmine依赖已就绪，启用Redmine MCP服务器")
        servers["redmine"] = {
            "command": "python",
            "args": ["redmine_mcp_server.py"],
            "transport": "stdio",
        }
    else:
        logger.warning("Redmine依赖未就绪，禁用Redmine MCP服务器")
        
    # 使用MultiServerMCPClient连接到多个服务器
    logger.debug("准备连接到MCP服务器...")
    try:
        async with MultiServerMCPClient(servers) as client:
            logger.debug("成功连接到所有MCP服务器")
            
            # 获取所有可用工具
            logger.debug("获取可用工具...")
            tools = client.get_tools()
            
            # 列出可用的工具
            print("可用的工具:")
            for i, tool in enumerate(tools):
                print(f"  {i+1}. {tool.name}")
            
            # 显示工具的详细描述
            print("\n工具描述:")
            for i, tool in enumerate(tools):
                print(f"  {i+1}. {tool.name}: {tool.description}")
            
            # 创建一个LangGraph agent，使用LM Studio的本地模型
            model = ChatOpenAI(
                model="qwen2.5:32b",  # 模型名称
                openai_api_base="http://***********:11434/v1",  # LM Studio API地址
                openai_api_key="sk-no-key-required"  # LM Studio通常不需要真实API密钥
            )
            agent = create_react_agent(model, tools)
            
            print("\n现在您可以与模型对话，模型会根据需要使用可用的工具。")
            print("输入 'quit' 或 'exit' 退出。\n")
            
            # 交互式对话循环
            while True:
                user_input = input("您: ")
                
                if user_input.lower() in ['quit', 'exit']:
                    print("再见!")
                    break
                
                print("AI正在思考...")
                start_time = time.time()
                
                # 调用agent处理用户输入
                result = await agent.ainvoke({"messages": [{"role": "user", "content": user_input}]})
                
                # 打印响应
                print(f"\n处理时间: {time.time() - start_time:.2f}秒")
                
                # 打印所有的消息
                for message in result["messages"]:
                    if hasattr(message, "content") and message.content:
                        if message.type == "human":
                            # 人类消息已经在输入时显示了
                            pass
                        elif message.type == "tool":
                            # 工具消息显示工具名称和结果
                            print(f"[工具 {message.name}]: {message.content}")
                        elif message.type == "ai":
                            # AI的最终回答
                            print(f"AI: {message.content}")
                print()  # 空行分隔对话
    except Exception as e:
        logger.error(f"错误: {str(e)}", exc_info=True)
        print(f"发生错误: {str(e)}")
        
        if "redmine" in str(e).lower():
            print("\n可能是Redmine服务器导致的问题。请运行python install_redmine_deps.py安装必要的依赖，或者禁用Redmine服务。")


if __name__ == "__main__":
    asyncio.run(main()) 